<?xml version="1.0" encoding="utf-8"?>
<odoo>

  <data>

    <record id="time_deposit_view_form" model="ir.ui.view">
      <field name="name">time.deposit.view.form</field>
      <field name="model">time.deposit</field>
      <field name="arch" type="xml">
        <form string="Time Deposit">
          <header>
            <!-- <button string="Draft" name="action_button_draft" type="object" class="oe_highlight" /> -->
            <button string="Placement" name="action_button_placement" type="object" class="oe_highlight" invisible="state != 'draft'"/>
            <button string="Cancel" name="action_cancel" type="object" class="oe_highlight" invisible="state != 'draft' or is_interest_rate_extend"/>			
            <button string="Cancel" name="action_button_cancel" type="object" class="oe_highlight" invisible="state != 'placement' or is_interest_rate_extend"/>
            <button string="Withdraw" name="action_button_withdraw" type="object" class="oe_highlight" invisible="state != 'placement' or is_interest_rate_extend"/>
            <button string="Extend" name="action_button_extend" type="object" class="oe_highlight" invisible="state != 'placement' or not is_interest_rate_extend"/>
            <button string="Create After Partial" name="action_create_after_partial" type="object" class="oe_highlight" invisible="state != 'withdraw' or withdraw_type != 'partial'"/>
            <field name="state" widget="statusbar" statusbar_visible="draft,placement,extend,withdraw,cancel"/>
          </header>
          <sheet>
            <div class="oe_button_box" name="button_box">
              <button name="action_open_journal_withdraw" type="object" class="oe_stat_button" icon="fa-book">
                <div class="o_stat_info">
                  <span class="o_stat_text">Journal Withdraw</span>
                </div>
              </button>
              <button name="action_open_journal_placement" type="object" class="oe_stat_button" icon="fa-book">
                <div class="o_stat_info">
                  <span class="o_stat_text">Journal Placement</span>
                </div>
              </button>
            </div>
            <div class="oe_title">
              <label for="name"/>
              <h1>
                <field name="name" readonly="1"/>
              </h1>
            </div>
            <group>
              <group>
                <field name="company_id" readonly="state in ['cancel', 'withdraw', 'placement', 'extend']" />
                <field name="deposit_name" readonly="state in ['cancel', 'withdraw', 'placement', 'extend']" />
                <field name="deposit_note" readonly="state in ['cancel', 'withdraw', 'placement', 'extend']"/>
                <field name="deposit_type" readonly="state in ['cancel', 'withdraw', 'placement', 'extend']"/>
                <field name="deposit_product" readonly="state in ['cancel', 'withdraw', 'placement', 'extend']"/>
                <field name="deposit_partner_bank_id" required="1" readonly="state in ['cancel', 'withdraw', 'placement', 'extend']" />
                <field name="bank_account" />
                <field name="bank" />
                <field name="open_date" required="1" readonly="state in ['cancel', 'withdraw', 'placement', 'extend']"/>
                <field name="maturity_date" required="1" readonly="state in ['cancel', 'withdraw', 'placement', 'extend']" />
                <field name="deposit_in_days_placement" />
                <field name="deposit_in_month" readonly="state in ['cancel', 'withdraw', 'placement', 'extend']" />
              </group>
              <group>
                <field name="currency_id" required="1" readonly="state in ['cancel', 'withdraw', 'placement', 'extend']" />
                <field name="deposit_original_amount" required="1" readonly="state in ['cancel', 'withdraw', 'placement', 'extend']" />
                <field name="deposit_idr_amount_placement" />
                <field name="beneficiary_bank_id" required="1" readonly="state in ['cancel', 'withdraw', 'placement', 'extend']" />
                <field name="interest_rate" required="withdraw_type == 'full' and state not in ['placement', 'withdraw', 'extend', 'cancel']" readonly="is_interest_rate_extend or withdraw_type == 'partial'" />
                <field name="interest_original_amount" readonly="1" />
          			<field name="period_id" invisible="0" />
				        <field name="reference" />
              </group>
            </group>
            <notebook>
              <page string="Placement Information">
                <group>
                  <field name="interest_amount_idr_placement" />
                  <!--                <field name="interest_income_account_id" context="{'compute_display_model': 'interest_income'}" required="1" readonly="state == 'placement'"/>                -->
                  <field name="interest_income_account_id" required="1" readonly="state in ['cancel', 'withdraw', 'placement', 'extend']"/>
                  <field name="interest_day_calc" required="1" readonly="state in ['cancel', 'withdraw', 'placement', 'extend']" />
                  <!--                <field name="interest_income_account_desc" />-->
                  <field name="journal_desc_interest_income" required="1" readonly="state in ['cancel', 'withdraw', 'placement', 'extend']" />
                  <!--                <field name="time_deposit_account_id" context="{'compute_display_model': 'interest_income'}" required="1" readonly="state == 'placement'"/>-->
                  <field name="time_deposit_account_id" required="1" readonly="state in ['cancel', 'withdraw', 'placement', 'extend']"/>
                  <field name="journal_desc_time_deposit" required="1" readonly="state in ['cancel', 'withdraw', 'placement', 'extend']" force_save="1"/>
                </group>
              </page>
              <page string="Withdrawal Information">
                <group>
                  <field name="deposit_clasification" readonly="state in ['cancel', 'withdraw', 'extend']"/>
                  <field name="attachment" readonly="state in ['cancel', 'withdraw', 'extend']"/>
                  <field name="journal_desc_withdraw" required="state in ['withdraw','extend', 'placement']" readonly="state in ['draft', 'cancel', 'withdraw', 'extend']" />
                  <field name="is_interest_rate_extend" readonly="state in ['cancel', 'withdraw', 'extend']" />
                  <field name="interest_rate_extend" required="is_interest_rate_extend" readonly="state in ['cancel', 'withdraw', 'extend'] or not is_interest_rate_extend or is_interest_rate_extend == False" />
                  <field name="start_date" required="is_interest_rate_extend" readonly="state in ['cancel', 'withdraw', 'extend'] or not is_interest_rate_extend"/>
                  <field name="new_maturity_date" required="is_interest_rate_extend" readonly="state in ['cancel', 'withdraw', 'extend'] or not is_interest_rate_extend"/>
                  <field name="deposit_in_days_extend" readonly="1"/>
                  <field name="withdraw" required="state == 'withdraw'" invisible="1" readonly="1" force_save="1"/>
                  <field name="withdraw_type" required="1" readonly="state in ['cancel', 'withdraw', 'placement', 'extend'] or withdraw_type == 'partial' or withdraw_type == 'full' or extend_partial_readonly" force_save="1"/>
                  <field name="withdraw_amount" required="withdraw_type == 'partial'" readonly="state in ['extend', 'withdraw', 'cancel'] or withdraw_type != 'partial'" force_save="1"/>
                  <field name="interest_amount_ac_settlement" />
                  <field name="withdraw_date" required="state == 'placement'" readonly="state in ['cancel', 'withdraw', 'extend']"/>
                  <field name="bank_charges_account_id" required="1" readonly="state in ['cancel', 'withdraw', 'extend']"/>
                  <field name="bank_charge" readonly="state in ['cancel', 'withdraw', 'extend']"/>
                </group>
              </page>
            </notebook>
          </sheet>
          <chatter />
        </form>
      </field>
    </record>

    <record id="time_deposit_view_list" model="ir.ui.view">
      <field name="name">time.deposit.view.tree</field>
      <field name="model">time.deposit</field>
      <field name="arch" type="xml">
        <list string="Time Deposit">
          <field name="name" />
          <field name="deposit_name" />
          <field name="company_id" />
          <field name="reference" />
          <field name="deposit_type" />
		      <field name="reference" />
          <field name="state" />
        </list>
      </field>
    </record>

    <record id="time_deposit_action" model="ir.actions.act_window">
      <field name="name">Time Deposit</field>
      <field name="res_model">time.deposit</field>
      <field name="view_mode">list,form</field>
      <field name="domain">[]</field>
      <field name="context">{}</field>
      <field name="help" type="html">
        <p class="oe_view_nocontent_create">
          There is no examples click here to add new Time Deposit.
        </p>
      </field>
    </record>

  </data>


</odoo>
