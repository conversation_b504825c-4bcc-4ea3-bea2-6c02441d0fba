<odoo>
    <data>
        <record id="mail_budget_relocation_approval" model="mail.template">
            <field name="name">Budget Relocation: Request Approval</field>
            <field name="model_id" ref="account_budget_relocation.model_account_relocation"/>
            <field name="subject">Budget Relocation (Ref {{ object.name or 'n/a' }})</field>
            <field name="email_to">{{ ctx['mail_to'] }}</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            Bank Relocation Approval:<br/><br/>
                            <table width="100%">
                                <tr>
                                    <td>Number</td>
                                    <td>: <t t-out="object.name or ''"></t></td>
                                </tr>
                                <tr>
                                    <td><strong>Total Budget Donor</strong></td>
                                    <td><strong>: <t t-out="object.company_currency_id.symbol or ''"></t><t t-out="'{:,.0f}'.format(object.total_budget_amount) or ''"></t></strong></td>
                                </tr>
                            </table>
                        </div>
                    </p>
                    
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <a t-att-href="ctx['link']" >Click here to see details</a> 
                    </p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>

        <record id="mail_budget_relocation_info" model="mail.template">
            <field name="name">Budget Relocation: Info</field>
            <field name="model_id" ref="account_budget_relocation.model_account_relocation"/>
            <field name="subject">Budget Relocation (Ref {{object.name or 'n/a' }})</field>
            <field name="email_to">{{ ctx['mail_to'] }}</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            <t t-out="ctx['message']" />
                        </div>
                    </p>
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <a t-att-href="ctx['link']" >Click here to see details</a> 
                    </p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>
    </data>
</odoo>
