<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data>
    
        <record id="budget_relocation_hierarchy_wizard_view_form" model="ir.ui.view">
            <field name="name">account.relocation.hierarchy.wizard.view.form</field>
            <field name="model">account.relocation.hierarchy.wizard</field>
            <field name="arch" type="xml">
                <form string="Budget Relocation Hierarchy Wizard">
                    <sheet>
                        <group>
                            <group>
                                <field name="from_employee_id" invisible="not context.get('reassign')" readonly="context.get('readonly')" required="context.get('reassign')" domain="employee_domain" />
                                <field name="employee_domain" invisible='1' />
                                <field name="to_employee_id" invisible="not context.get('reassign')" required="context.get('reassign')" />
                                <field name="note" invisible="context.get('reassign')" required="not context.get('reassign')" />
                                <field name="budget_relocation_id" invisible='1' />
                            </group>
                            <group>
                                
                            </group>
                        </group>
                        <footer>
                            <button name="action_approve" type="object" invisible="not context.get('approve')" string="Approve" class="oe_highlight"/>
                            <button name="action_reject" type="object" invisible="not context.get('reject')" string="Reject" class="oe_highlight"/>
                            <button name="action_reassign" type="object" invisible="not context.get('reassign')" string="Reassign" class="oe_highlight"/>
                            <button string="Discard" class="btn-default" special="cancel" />
                        </footer>
                    </sheet>
                </form>
            </field>
        </record>
    
    </data>
    

</odoo>
