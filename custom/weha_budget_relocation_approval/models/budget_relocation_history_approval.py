
# -*- coding: utf-8 -*-
###############################################################################
#    License, author and contributors information in:                         #
#    __manifest__.py file at the root folder of this module.                  #
###############################################################################

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError



class WehaBudgetRelocationHistoryApproval(models.Model):
    _name = 'account.relocation.history.approval'
    _inherit = 'approval.history.mixin'
    _description = 'Budget Relocation Approval'

    budget_relocation_id = fields.Many2one(string='Budget Relocation',comodel_name='account.relocation')
    employee_ids = fields.Many2many(
        string='Employees',
        comodel_name='hr.employee',
        relation='account_history_employee_rel',
        column1='account_relocation_history_approval_id',
        column2='hr_employee_id',
    )
    reassign_employee_ids = fields.Many2many(
        string='Reassign Employees',
        comodel_name='hr.employee',
        relation='account_history_employee_reassign_rel',
        column1='account_relocation_history_approval_id',
        column2='hr_employee_id',
    )
    approval_employee_ids = fields.Many2many(
        string='Employees Approval',
        comodel_name='hr.employee',
        relation='account_history_employee_approval_rel',
        column1='account_relocation_history_approval_id',
        column2='hr_employee_id',
    )
    # same_group = fields.Boolean(string='Same Group', default=False)
    group_recipient = fields.Boolean(string='Group Recipient', default=False)
    group_donor = fields.Boolean(string='Group Donor', default=False)


class BudgetRelocationApprovalDetail(models.Model):
    _name = 'account.relocation.approval.detail'
    _inherit = 'approval.history.detail.mixin'
    _description = 'Budget Relocation Approval Detail'

    budget_relocation_id = fields.Many2one(string='Budget Relocation',comodel_name='account.relocation')

    

