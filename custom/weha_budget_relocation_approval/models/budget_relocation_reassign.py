
# -*- coding: utf-8 -*-
###############################################################################
#    License, author and contributors information in:                         #
#    __manifest__.py file at the root folder of this module.                  #
###############################################################################

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError



class BudgetRelocationReassign(models.Model):
    _name = 'account.relocation.reassign'
    _description = 'Budget Relocation Reassign'

    budget_relocation_id = fields.Many2one('account.relocation', 'Budget Relocation',)
    from_employee_id = fields.Many2one('hr.employee', string='From Employee')
    to_employee_id = fields.Many2one('hr.employee', string='To Employee')

    

