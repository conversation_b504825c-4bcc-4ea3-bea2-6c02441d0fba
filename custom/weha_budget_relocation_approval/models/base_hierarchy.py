from odoo import fields, models, api


class BaseHierarchy(models.Model):
    _inherit = 'base.hierarchy'

    
    model_name = fields.Char(related='model_id.model')


class BaseHierarchyLine(models.Model):
    _inherit = 'base.hierarchy.line'

    
    # same_group = fields.Boolean(string='Same Group', default=False)
    group_recipient = fields.Boolean(string='Group Recipient', default=False)
    group_donor = fields.Boolean(string='Group Donor', default=False)
