<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data>
    
        <record id="account_justification_fpjp_view_tree" model="ir.ui.view">
            <field name="name">account.justification.fpjp.view.tree</field>
            <field name="model">account.justification</field>
            <field name="arch" type="xml">
                <list string="">
                    <field name="name" />
                    <field name="date" />
                    <field name="description" />
                    <field name="employee_id" />
                    <field name="amount_currency" />
                    <field name="amount_justif" column_invisible="1" />
                    <field name="remaining_amount" column_invisible="context.get('hide_pr')"/>
                    <field name="remaining_amount_fpjp" column_invisible="context.get('hide_fpjp')"/>
                    <field name="state" />
                </list>
            </field>
        </record>
    
    </data>
    

</odoo>
