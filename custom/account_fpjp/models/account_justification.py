# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

class AccountJustificationFPJP(models.Model):
    _inherit = 'account.justification'

    fpjp_ids = fields.One2many('account.fpjp', 'justification_id', string='FPJP')
    fpjp_count = fields.Integer(compute='_compute_fpjp_count', string='FPJP Count')
    # remaining_amount = fields.Monetary(compute='_compute_remaining_amount',search="_get_remaining_amount", string='Remaining Amount', currency_field='company_currency_id', store=True)
    remaining_amount = fields.Monetary(compute='_compute_remaining_amount', string='Remaining Amount (PR)', currency_field='company_currency_id', store=True)
    remaining_amount_fpjp = fields.Monetary('Remaining Amount (FPJP)', compute='_compute_remaining_amount_fpjp', currency_field='company_currency_id', store=True)
    
    @api.depends('fpjp_ids.total_amount', 'fpjp_ids.state', 'fpjp_ids.line_ids')
    def _compute_remaining_amount_fpjp(self):
        for rec in self:

            # Get Justif Line
            justif_fpjp_line = rec.line_ids.filtered_domain([('procurement_type_id.type', '=', 'non_procurement')])
            # Compute Total Remaining FPJP
            total_fpjp_amount = sum(fpjp.total_amount for fpjp in rec.fpjp_ids if fpjp.state not in ['rejected', 'return', 'cancel'])
            total_fpjp_reserved = sum(justif_fpjp_line.mapped('amount_currency'))
            rec.remaining_amount_fpjp = total_fpjp_reserved - total_fpjp_amount

            fpjp = self.env['account.fpjp'].search([('justification_id.budgetary_position_id', '=', rec.budgetary_position_id.id),
                                                    ('state', 'not in', ['rejected', 'return', 'cancel'])])

            # rec.budgetary_position_id.fpjp_reserve_amount += self.total_amount
            rec.budgetary_position_id.fpjp_reserve_amount = sum(fpjp.mapped('total_amount'))

    @api.depends('purchase_request_ids.estimated_cost', 'line_ids', 'purchase_request_ids.state')
    def _compute_remaining_amount(self):
        for rec in self:

            # Get Justif Line
            justif_pr_line = rec.line_ids.filtered_domain([('procurement_type_id.type', '=', 'procurement')])
            
            # Compute Total Remaining PR
            total_pr_reserved = sum(justif_pr_line.mapped('amount_currency'))
            pr_ids = self.env['purchase.request.line'].search([('justification_line_id', 'in', justif_pr_line.ids),
                                                                ('request_id.state', 'not in', ['rejected', 'canceled'])])
            total_pr_amount = sum(pr.estimated_cost for pr in pr_ids) or 0
            rec.remaining_amount = total_pr_reserved - total_pr_amount
            
    
    def _get_remaining_amount(self, operator, value):
        # Validate operator
        if operator not in ['=', '>', '<', '>=', '<=', '!=']:
            return []
            
        # SQL query to calculate remaining amount directly in database
        query = """
            WITH justification_budget AS (
                SELECT 
                    aj.id AS justification_id,
                    COALESCE(SUM(ajl.amount_currency), 0) AS budget_reserved
                FROM 
                    account_justification aj
                LEFT JOIN 
                    account_justification_line ajl ON ajl.justification_id = aj.id
                GROUP BY 
                    aj.id
            ),
            fpjp_amounts AS (
                SELECT 
                    af.justification_id,
                    af.id AS fpjp_id,
                    COALESCE(SUM(afl.amount_total), 0) AS fpjp_total
                FROM 
                    account_fpjp af
                LEFT JOIN
                    account_fpjp_line afl ON afl.account_fpjp_id = af.id
                WHERE 
                    af.state = 'approved'
                GROUP BY 
                    af.justification_id, af.id
            ),
            fpjp_totals AS (
                SELECT
                    justification_id,
                    SUM(fpjp_total) AS total_fpjp_amount
                FROM
                    fpjp_amounts
                GROUP BY
                    justification_id
            )
            
            SELECT 
                jb.justification_id
            FROM 
                justification_budget jb
            LEFT JOIN 
                fpjp_totals ft ON ft.justification_id = jb.justification_id
            WHERE 
                (jb.budget_reserved - COALESCE(ft.total_fpjp_amount, 0)) {0} %s
        """.format(operator)
        
        self.env.cr.execute(query, (float(value),))
        justification_ids = [row[0] for row in self.env.cr.fetchall()]
        
        return [('id', 'in', justification_ids)]
        
    
    # @api.depends('name','fpjp_ids.state')
    # @api.depends_context('with_remaining_amount')
    # def _compute_display_name(self):
    #     super()._compute_display_name()
    #     if self.env.context.get('with_remaining_amount'):
    #         for rec in self:
    #             rec.display_name = f"{rec.name} ({rec.remaining_amount})"

    def _compute_fpjp_count(self):
        for rec in self:
            rec.fpjp_count = len(rec.fpjp_ids)

    def action_create_fpjp(self):
        self.ensure_one()
        if self.state != 'approved':
            raise UserError(_('Only approved justifications can create FPJP.'))

        # Get product templates
        procurement_template = self.env.ref('account_budget_justif.product_template_procurement')
        non_procurement_template = self.env.ref('account_budget_justif.product_template_non_procurement')

        # Create FPJP header
        fpjp_vals = {
            'justification_id': self.id,
            'description': self.description,
            'source': 'justification',
            'fpjp_date': fields.Date.today(),
            'accounting_date': fields.Date.today(),
            'invoice_date': fields.Date.today(),
            'employee_id': self.employee_id.id,
            'requestor_id': self.requestor_id.id,
            'unit_id': self.unit_id.id,
            'group_id': self.group_id.id,
            'direktorat_id': self.directorate_id.id,
            'state': 'draft',
        }

        fpjp = self.env['account.fpjp'].create(fpjp_vals)

        # Create FPJP lines
        for line in self.line_ids:
            # Get appropriate product template and product
            product_template = procurement_template if line.procurement_type_id.is_procurement else non_procurement_template
            product = self.env['product.product'].search([('product_tmpl_id', '=', product_template.id)], limit=1)

            # Get expense account from product category or product template
            account = product.property_account_expense_id or product.categ_id.property_account_expense_categ_id
            if not account:
                raise UserError(_('No expense account defined on product %s or its category') % product.name)

            line_vals = {
                'account_fpjp_id': fpjp.id,
                'name': line.description,
                'justification_line_id': line.id,
                'currency_id': line.currency_id.id,
                'coa_id': account.id,  # Use the expense account from product
                'price_unit': line.amount_currency,
                'tax_ids': [(6, 0, line.tax_ids.ids)],
                'manual_currency_rate_active': line.justification_id.manual_currency_rate_active,
                'manual_currency_rate': line.currency_rate,
                'manual_currency_rate_unit': line.currency_rate,
            }
            self.env['account.fpjp.line'].create(line_vals)

        return {
            'type': 'ir.actions.act_window',
            'name': _('FPJP'),
            'res_model': 'account.fpjp',
            'res_id': fpjp.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def action_view_fpjp(self):
        self.ensure_one()
        action = {
            'type': 'ir.actions.act_window',
            'name': _('FPJP'),
            'res_model': 'account.fpjp',
            'view_mode': 'list,form',
            'domain': [('justification_id', '=', self.id)],
            'context': {'default_justification_id': self.id},
        }
        return action
    
    def action_cancel(self):
        res = super(AccountJustificationFPJP, self).action_cancel()
        for rec in self:
            fpjps = rec.fpjp_ids.filtered(
                lambda fpjp: fpjp.state not in ['cancel']
            )
            if fpjps:
                raise ValidationError(_("FPJP must be cancelled before canceling justification."))
        return res