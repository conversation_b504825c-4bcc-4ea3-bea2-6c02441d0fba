# -*- coding: utf-8 -*-
import logging

from odoo import models, fields, api

_logger = logging.getLogger(__name__)


class AccountFpjpLine(models.Model):
    _name = 'account.fpjp.line'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Account FPJP Line'

    def _default_uom_id(self):
        uom = self.env.ref('uom.product_uom_unit', raise_if_not_found=False)
        return uom

    account_fpjp_id = fields.Many2one('account.fpjp', string='Account Fpjp', ondelete='cascade')
    name = fields.Char('Description', required=True, tracking=True)
    no = fields.Integer('No')
    # justification_line_description_id = fields.Many2one('account.justification.line.description', string='Justification Line Description')
    justification_line_id = fields.Many2one('account.justification.line', string='Justification Line', domain=[('procurement_type', '=', 'non_procurement')])
    justification_line_desc = fields.Char(string='Justification Line Description')
    justification_id = fields.Many2one('account.justification', string='Justification Line Description',related='account_fpjp_id.justification_id', store=True)
    coa_id = fields.Many2one('account.account', string='Account')
    category_id = fields.Many2one('fpjp.line.category', string='Line Category')
    bill_line_ids = fields.One2many('account.move.line', 'fpjp_line_id', string='Vendor Bill')

    date = fields.Date('Date', related='account_fpjp_id.fpjp_date', store=True)
    manual_currency_rate_active = fields.Boolean('Apply Manual Exchange')
    
    manual_currency_rate = fields.Float('Rate', default=1.0, tracking=True)
    manual_currency_rate_unit = fields.Float('Rate Unit', tracking=True, digits=(12,12))
    currency_id = fields.Many2one('res.currency', string='Currency', required=True, default=lambda self: self.env.company.currency_id)
    company_currency_id = fields.Many2one('res.currency', string='Company Currency',default=lambda self: self.env.company.currency_id)

    quantity = fields.Float('Quantity',required=True)
    uom_id = fields.Many2one('uom.uom', string='UoM', default=_default_uom_id)
    price_unit = fields.Float('Price',required=True)
    tax_ids = fields.Many2many('account.tax', string='Tax')
    price_subtotal = fields.Monetary(compute='_compute_price_subtotal', string='Sub Total Currency', currency_field="currency_id")
    price_subtotal_base = fields.Monetary(compute='_compute_price_subtotal', string='Sub Total IDR', currency_field="company_currency_id")

    flag_asset_active = fields.Boolean('Flag Asset')
    type = fields.Selection([
        ('asset', 'Asset'),
        ('cip', 'CIP'),
        ('asset_service', 'Asset Service'),
    ], string='Asset Type')

    untaxed_amt = fields.Monetary('Untaxed Amt', compute='_compute_price_subtotal', currency_field="currency_id",store=True)
    tax_amt = fields.Monetary('Tax Amt', compute='_compute_price_subtotal', currency_field="currency_id",store=True)
    amount_currency = fields.Monetary('Amount Currency', compute='_compute_price_subtotal', currency_field="company_currency_id",store=True)
    amount_residual = fields.Monetary('Amount Residual', compute='_compute_price_subtotal', currency_field="company_currency_id",store=True)
    amount_total = fields.Monetary('Total', compute='_compute_price_subtotal', currency_field="company_currency_id",store=True)
    is_asset = fields.Boolean('Is Asset')
    is_cip = fields.Boolean('Is CIP')

    @api.onchange('justification_line_id')
    def onchange_justification_line(self):
        if self.justification_line_id:
            self.justification_line_desc = self.justification_line_id.description

    @api.onchange('currency_id')
    def _onchange_currency_id(self):
        for line in self:
            if line.currency_id:
                if line.manual_currency_rate_active:
                    line.manual_currency_rate = line.manual_currency_rate if line.manual_currency_rate else 1.0
                else:
                    rate = line.currency_id._get_conversion_rate(
                        line.currency_id,
                        line.env.company.currency_id,
                        line.env.company,
                        line.date
                    )  # Gunakan rate dari Odoo berdasarkan tanggal
                    line.manual_currency_rate = rate

    @api.onchange('manual_currency_rate_active', 'manual_currency_rate')
    def _onchange_manual_currency_rate(self):
        """ Saat manual_currency_rate diubah, update manual_currency_rate_unit """
        for line in self:
            if line.manual_currency_rate_active:
                line.manual_currency_rate_unit = line.manual_currency_rate if line.manual_currency_rate else 1.0

    @api.onchange('manual_currency_rate_active', 'manual_currency_rate_unit')
    def _onchange_manual_currency_rate_unit(self):
        """ Saat manual_currency_rate_unit diubah, update manual_currency_rate """
        for line in self:
            if line.manual_currency_rate_active:
                line.manual_currency_rate = line.manual_currency_rate_unit if line.manual_currency_rate_unit else 1.0

    @api.depends('price_unit', 'quantity', 'tax_ids', 'currency_id', 'date', 'manual_currency_rate_active', 'manual_currency_rate', 'manual_currency_rate_unit')
    def _compute_price_subtotal(self):
        """ Hitung price subtotal, tax, amount_currency, dan amount_total """
        for line in self:
            price_subtotal = line.price_unit * line.quantity
            tax_amt = 0.0
            untaxed_amt = price_subtotal
            # Pilih kurs yang digunakan untuk amount_currency
            if line.manual_currency_rate_active:
                rate = line.manual_currency_rate
            else:
                rate = line.currency_id._get_conversion_rate(
                    line.currency_id,
                    line.env.company.currency_id,
                    line.env.company,
                    line.date
                )  # Gunakan rate dari Odoo berdasarkan tanggal

            # Hitung pajak menggunakan Odoo tax computation
            if line.tax_ids:
                taxes = line.tax_ids.compute_all(
                    price_subtotal,
                    currency=line.currency_id,
                    quantity=1,
                    product=None,
                    partner=None
                )
                tax_amt = sum(t['amount'] for t in taxes['taxes']) * rate
                untaxed_amt = taxes['total_excluded'] * rate
            else:
                untaxed_amt = price_subtotal * rate

            balance = sum(line.bill_line_ids.mapped('balance'))

            # Hitung amount_currency dan amount_total
            line.amount_currency = untaxed_amt
            line.amount_total = line.amount_currency + tax_amt
            line.price_subtotal = price_subtotal
            line.price_subtotal_base = price_subtotal * rate
            line.untaxed_amt = untaxed_amt
            line.tax_amt = tax_amt
            line.amount_residual = untaxed_amt - balance

    def _prepare_move_line_vals(self):
        """ Siapkan dictionary untuk move line """
        self.ensure_one()
        return {
            'name': self.name,
            'quantity': 1,
            'currency_id': self.currency_id.id,
            'fpjp_line_id': self.id,
            'price_unit': self.price_unit,
            'product_uom_id': self.uom_id.id,
            'quantity': self.quantity,
            'type': self.type,
            'tax_ids': [(6, 0, self.tax_ids.ids)],
            'account_id': self.coa_id.id,
        }
