from odoo import _, api, fields, models

class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'
    
    domain_purchase_request = fields.Binary(string='Purchase Request Domain ',compute='_compute_purchase_request_domain')

    @api.depends('purchase_request_id')
    def _compute_purchase_request_domain(self):
        for rec in self:
            rec.domain_purchase_request = [('state','=','approved'),('buyer_id.user_id', '=', rec.env.user.id)]

