<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data>
    
        <record id="inherit_purchase_order_form_inherit_ap_pr_po" model="ir.ui.view">
            <field name="name">purchase.order.view.form.ap.pr.po.inherit</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="ap_purchase_order.inherit_purchase_order_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='purchase_request_id']" position="after">
                    <field name="domain_purchase_request" invisible="1"/>
                </xpath>
                <xpath expr="//field[@name='purchase_request_id']" position="attributes">
                    <attribute name="domain">domain_purchase_request</attribute>
                </xpath>
            </field>
        </record>
    
    </data>
    

</odoo>
