<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- View stock.inventory.advance View List -->
    <record id="view_stock_inventory_advance_list" model="ir.ui.view">
        <field name="name">view.stock.inventory.advance.list</field>
        <field name="model">stock.inventory.advance</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="partner_id"/>
                <field name="create_uid"/>
                <field name="state" widget="badge" decoration-danger="state == 'rejected'" decoration-info="state == 'draft'" decoration-warning="state == 'to_approve'" decoration-success="state == 'done'"/>
            </list>
        </field>
    </record>

    <!-- View stock.inventory.advance form -->
    <record id="view_stock_inventory_advance_form" model="ir.ui.view">
        <field name="name">view.stock.inventory.advance.form</field>
        <field name="model">stock.inventory.advance</field>
        <field name="arch" type="xml">
            <form string="Stock Inventory Advance">
                <header>
                    <button name="action_confirm" string="Confirm" type="object" invisible="state != 'draft'" class="oe_highlight"/>
                    <button name="action_approve" string="Approve" type="object" invisible="state != 'to_approve'" class="oe_highlight"/>
                    <button name="action_reject" string="Reject" type="object" invisible="state != 'to_approve'"/>
                    <button name="action_set_to_draft" string="Return" type="object" invisible="state != 'to_approve'"/>
                    <field name="state" widget="statusbar" status_visible="draft,to_approve,done"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <label for="name"/>
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="type" force_save="1" readonly="1"/>
                            <field name="partner_id" string="Customer" id="customer" invisible="type != 'sale'" context="{'default_customer_rank':1}" domain="[('customer_rank','>',0)]" readonly="state != 'draft'"/>
                            <field name="partner_id" string="Vendor" id="vendor" invisible="type != 'purchase'" context="{'default_supplier_rank':1}" domain="[('supplier_rank','>',0)]" readonly="state != 'draft'"/>
                            <field name="create_uid"/>
                            <field name="accounting_date" invisible="not entry_id"/>
                            <field name="entry_id" invisible="not entry_id" force_save="1" readonly="1"/>
                            <field name="attachment" readonly="state != 'draft'"/>
                        </group>
                    </group>
                    <notebook>
                        <page name="product_page" string="Product">
                            <field name="line_ids" nolabel="1" readonly="state != 'draft'"> 
                                <list editable="bottom">
                                    <field name="product_id" domain="[('is_digital_inventory','=',True)]" required="1"/>
                                    <field name="name" required="1" />
                                    <field name="amount"/>
                                    <field name="inventory_type" column_invisible="1"/>
                                    <field name="tax_ids" widget="many2many_tags" domain="[('type_tax_use', '=', inventory_type)]"/>
                                    <field name="amount_after_tax"/>
                                </list>
                            </field>
                        </page>
                        <!-- <page name="approval_page" string="Approval">
                        </page> -->
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- View stock.inventory.advance search -->
    <record id="view_stock_inventory_advance_search" model="ir.ui.view">
        <field name="name">view.stock.inventory.advance.search</field>
        <field name="model">stock.inventory.advance</field>
        <field name="arch" type="xml">
            <search>
                <group expand="1" string="Group By">
                    <filter string="Name" name="name" domain="[]" context="{'group_by':'name'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action stock.inventory.advance -->
    <record id="action_stock_inventory_advance_purchase" model="ir.actions.act_window">
        <field name="name">Inventory Topup</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">stock.inventory.advance</field>
        <field name="view_mode">list,form</field>
        <field name="domain">[('type', '=', 'purchase')]</field>
        <field name="context">{'default_type': 'purchase'}</field>
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">
                There is no examples click here to add new Inventory Topup.
            </p>
        </field>
    </record>

    <record id="action_stock_inventory_advance_sale" model="ir.actions.act_window">
        <field name="name">Inventory Paid Invoice</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">stock.inventory.advance</field>
        <field name="view_mode">list,form</field>
        <field name="domain">[('type', '=', 'sale')]</field>
        <field name="context">{'default_type': 'sale'}</field>
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">
                There is no examples click here to add new Inventory Paid Invoice.
            </p>
        </field>
    </record>

</odoo>
