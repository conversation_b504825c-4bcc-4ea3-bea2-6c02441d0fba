# -*- coding: utf-8 -*-

from odoo import fields, models


class AccountMoveApproval(models.Model):
    _name = 'account.move.approval'
    _inherit = 'approval.history.mixin'
    _description = 'Account Move Approval'

    move_id = fields.Many2one('account.move', string='Account Move', ondelete='cascade')

    employee_ids = fields.Many2many(
        'hr.employee', 'move_approval_employee_rel', string='Employees'
    )
    reassign_employee_ids = fields.Many2many(
        'hr.employee',
        'move_approval_reassign_employee_rel',
        string='Reassign Employees',
    )
    approval_employee_ids = fields.Many2many(
        'hr.employee', 'move_approval_approval_employee_rel', string='Employees'
    )
    hierarchy_department_ids = fields.Many2many(
        'hr.department',
        'account_move_approval_department_rel',
        string='Hierarchy Units',
    )


class AccountMoveApprovalDetail(models.Model):
    _name = 'account.move.approval.detail'
    _inherit = 'approval.history.detail.mixin'
    _description = 'Account Move Approval Details'

    move_id = fields.Many2one('account.move', 'Account Move', ondelete='cascade')


class AccountMoveReassign(models.Model):
    _name = 'account.move.reassign'
    _description = 'Account Move Reassign'

    move_id = fields.Many2one('account.move', string='Account Move', ondelete='cascade')
    from_employee_id = fields.Many2one('hr.employee', string='From Employee')
    to_employee_id = fields.Many2one('hr.employee', string='To Employee')
