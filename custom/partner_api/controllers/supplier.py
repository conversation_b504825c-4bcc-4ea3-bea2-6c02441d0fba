import json
import requests
from odoo.tests import form
import werkzeug.wrappers

from odoo import api, models, fields
from odoo import http
from odoo.http import request, Response
import io
import xlsxwriter
from datetime import datetime
import pytz
from datetime import datetime
import pytz


class BankAPIController(http.Controller):
    
	def _prepare_error_response(self, message, status_code=401):
		return Response(
			json.dumps({
				'status': 'error',
				'message': message
			}),
			headers={'Content-Type': 'application/json'},
			status=status_code
		)

	def _validate_token(self, token):
		if not token:
			return False
			
		user_id = request.env['res.users'].sudo().search([('token', '=', token)], limit=1)
		
		if not user_id:
			return False
		
		# Check if token is expired
		tz_name = user_id.tz or 'UTC'
		tz = pytz.timezone(tz_name)
		now_user_tz = datetime.now(tz)
		now_user_tz = now_user_tz.strftime('%Y-%m-%d %H:%M:%S')
		
		if user_id.expired_token_date:
			expired_token_utc = user_id.expired_token_date.replace(tzinfo=pytz.utc)
			expired_token_tz = expired_token_utc.astimezone(tz)
			expired_token_tz = expired_token_tz.strftime('%Y-%m-%d %H:%M:%S')
			
			if str(expired_token_tz) < str(now_user_tz):
				return False
		
		return user_id    
    
	@http.route('/api/bank_master_partner', auth='public', methods=['GET'], csrf=False)
	def get_bank_master(self, **kw):
		try:
			headers = request.httprequest.headers
			token = str(headers.get('token', ''))
			user_id = self._validate_token(token)
			
			if not user_id:
				return self._prepare_error_response('Invalid or expired token. Please authenticate again.')

			bank_master = request.env['res.bank'].sudo().search([])
			result = [] 
			
			for bank in bank_master:
				bank_data = {
					'id': bank.id,
					'name': bank.name,
				}
				
				result.append(bank_data)
			
			return Response(
				json.dumps({
					'status': 'success',
					'data': result
				}),
				headers={'Content-Type': 'application/json'},
				status=200
			)
		except Exception as e:
			return Response(
				json.dumps({
					'status': 'error',
					'message': str(e)
				}),
				headers={'Content-Type': 'application/json'},
				status=500
			)   
      

class SupplierAPI(http.Controller):
    
	def _prepare_error_response(self, message, status_code=401):
		return Response(
			json.dumps({
				'status': 'error',
				'message': message
			}),
			headers={'Content-Type': 'application/json'},
			status=status_code
		)

	def _validate_token(self, token):
		if not token:
			return False
			
		user_id = request.env['res.users'].sudo().search([('token', '=', token)], limit=1)
		
		if not user_id:
			return False
		
		# Check if token is expired
		tz_name = user_id.tz or 'UTC'
		tz = pytz.timezone(tz_name)
		now_user_tz = datetime.now(tz)
		now_user_tz = now_user_tz.strftime('%Y-%m-%d %H:%M:%S')
		
		if user_id.expired_token_date:
			expired_token_utc = user_id.expired_token_date.replace(tzinfo=pytz.utc)
			expired_token_tz = expired_token_utc.astimezone(tz)
			expired_token_tz = expired_token_tz.strftime('%Y-%m-%d %H:%M:%S')
			
			if str(expired_token_tz) < str(now_user_tz):
				return False
		
		return user_id    

	@http.route('/api/update_supplier_master_get', auth='public', methods=['GET'], csrf=False)
	def get_supplier_master(self, **kw):
			try:
				headers = request.httprequest.headers
				token = str(headers.get('token', ''))
				user_id = self._validate_token(token)
				
				if not user_id:
					return self._prepare_error_response('Invalid or expired token. Please authenticate again.')
       
				res_partner_model = request.env['res.partner'].sudo()
				res_partner_ids = res_partner_model.search([("is_vendor", "=", True)])
				result = []

				for partner in res_partner_ids:
					update_supplier_masters = request.env['procurement.update.data'].sudo().search([("partner_id", "=", partner.id)])

					for update_sm in update_supplier_masters:
						supplier_data = {
							"header": {
								'id': update_sm.id,
								'name': update_sm.name,
								'general_info_checked': update_sm.general_info_checked,
								'company_name': update_sm.company_name,
								'company_address': update_sm.company_address,
								'bank_account_checked': update_sm.bank_account_checked,
								'tax_status_checked': update_sm.tax_status_checked,
								'tax_document': update_sm.tax_document,
								'tax_organization_type_id': update_sm.tax_organization_type.id if update_sm.tax_organization_type else False,
								'tax_organization_type_name': update_sm.tax_organization_type.name if update_sm.tax_organization_type else "",
								'npwp_number': update_sm.npwp_number,
								'ktp_number': update_sm.ktp_number,
								'payment_term_id': update_sm.payment_term_id.id if update_sm.payment_term_id else False,
								'payment_term_name': update_sm.payment_term_id.name if update_sm.payment_term_id else "",
								'sequence': update_sm.sequence,
								'reference_number': update_sm.reference_number,
							},
							"contact": [
								{
									'id': contact.id,
									'name': contact.name,
									'function': contact.function,
									'phone': contact.phone,
									'mobile': contact.mobile,
									'email': contact.email,
									'type': contact.type,
									'street': contact.street,
									'street2': contact.street2,
									'city': contact.city,
									'zip': contact.zip,
									'country_id': contact.country_id.id if contact.country_id else False,
									'country_name': contact.country_id.name if contact.country_id else "",
									'lang': contact.lang,
									'comment': contact.comment,
								} for contact in update_sm.contact_ids
							],
							'bank': [
								{
									'id': bank.id,
									'acc_number': bank.acc_number,
									'bank_id':	bank.bank_id.id,
         							'bank_id_name':	bank.bank_id.name,
									'account_holder': bank.account_holder,
									'is_sub_account': bank.is_sub_account,
									'main_partner_bank_id': bank.main_partner_bank_id.id if bank.main_partner_bank_id else False,
									'main_partner_bank_id_name': bank.main_partner_bank_id.name if bank.main_partner_bank_id else "",
									'main_account_number': bank.main_account_number,
									'branch_name': bank.branch_name,
									'branch_code': bank.branch_code,
									'swift_code': bank.swift_code,
									'account_number': bank.account_number,
									'tipe_rekening_bank': bank.tipe_rekening_bank.id,
									'tipe_rekening_bank_name': bank.tipe_rekening_bank.name,
									'currency_id': bank.currency_id.id if bank.currency_id else False,
									'currency_id_name': bank.currency_id.name if bank.currency_id else "",
									'allow_out_payment': bank.allow_out_payment
         
         
								} for bank in update_sm.bank_procurement_ids
							],
						}
						result.append(supplier_data)

				return Response(
					json.dumps({'status': 'success', 'data': result}),
					headers={'Content-Type': 'application/json'},
					status=200
				)

			except Exception as e:
				return Response(
					json.dumps({'status': 'error', 'message': str(e)}),
					headers={'Content-Type': 'application/json'},
					status=500
				)
    
	@http.route('/api/update_supplier_master_post', auth='public', methods=['POST'], csrf=False)
	def post_supplier_master(self, **kwargs):
		try:
			headers = request.httprequest.headers
			token = str(headers.get('token', ''))
			user_id = self._validate_token(token)

			if not user_id:
				return self._prepare_error_response('Invalid or expired token. Please authenticate again.')

			payload = json.loads(request.httprequest.data.decode('utf-8'))
			res_partner_model = request.env['res.partner'].sudo()
			partner_id = res_partner_model.browse([(payload.get('partner_id'))])

			# update_sm = request.env['procurement.update.data'].sudo().search([
			# 	('reference_number', '=', payload.get('reference_number'))
			# ], limit=1)
			if payload.get('id'):
				update_sm = request.env['procurement.update.data'].sudo().browse([payload.get('id')])
			else:
				update_sm = False

			values = {
				'name': partner_id.name,
				'general_info_checked': payload.get('general_info_checked'),
				'company_name': payload.get('company_name'),
				'company_address': payload.get('company_address'),
				'bank_account_checked': payload.get('bank_account_checked'),
				'tax_status_checked': payload.get('tax_status_checked'),
				'tax_document': payload.get('tax_document'),
				'tax_organization_type': payload.get('tax_organization_type_id'),
				'npwp_number': payload.get('npwp_number'),
				'ktp_number': payload.get('ktp_number'),
				'payment_term_id': payload.get('payment_term_id'),
				'sequence': payload.get('sequence'),
				'reference_number': 'New',
				'partner_id': payload.get('partner_id'),
				'term_payment_checked': payload.get('term_payment_checked'),
			}

			if update_sm:
				update_sm.sudo().write(values)
			else:
				update_sm = request.env['procurement.update.data'].sudo().create(values)


			update_sm.contact_ids.unlink()
			update_sm.bank_procurement_ids.unlink()
						
			for contact in payload.get('contact', []):
				request.env['procurement.contact'].sudo().create({
					'procurement_id': update_sm.id,
					'name': contact.get('name'),
					'function': contact.get('function'),
					'phone': contact.get('phone'),
					'mobile': contact.get('mobile'),
					'email': contact.get('email'),
					'type': contact.get('type'),
					'street': contact.get('street'),
					'street2': contact.get('street2'),
					'city': contact.get('city'),
					'zip': contact.get('zip'),
					'country_id': contact.get('country_id'),
					'lang': contact.get('lang'),
					'comment': contact.get('comment'),
				})

			for bank in payload.get('bank', []):
				request.env['procurement.bank'].sudo().create({
					'procurement_id': update_sm.id,
					'acc_number': bank.get('acc_number'),
					'bank_id': bank.get('bank_id'),
					'account_holder': bank.get('account_holder'),
					'is_sub_account': bank.get('is_sub_account'),
					'main_partner_bank_id': bank.get('main_partner_bank_id'),
					'main_account_number': bank.get('main_account_number'),
					'branch_name': bank.get('branch_name'),
					'branch_code': bank.get('branch_code'),
					'swift_code': bank.get('swift_code'),
					'account_number': bank.get('account_number'),
					'tipe_rekening_bank': bank.get('tipe_rekening_bank'),
					'currency_id': bank.get('currency_id'),
					'allow_out_payment': bank.get('allow_out_payment'),
				})


			return Response(
				json.dumps({
					'status': 'success',
					'message': 'Data supplier berhasil diproses.'
				}),
				headers={'Content-Type': 'application/json'},
				status=200
			)

		except Exception as e:
			return Response(
				json.dumps({
					'status': 'error',
					'message': str(e)
				}),
				headers={'Content-Type': 'application/json'},
				status=500
			)
			
    
	@http.route('/api/tax_organization', auth='public', methods=['GET'], csrf=False)
	def get_tax_organization(self, **kw):
     
		try:
			# Validate token
			headers = request.httprequest.headers
			token = str(headers.get('token', ''))
			user_id = self._validate_token(token)
			
			if not user_id:
				return self._prepare_error_response('Invalid or expired token. Please authenticate again.')

			tax_organization = request.env['tax.organization.type'].sudo().search([])
			result = []
			
			for tax in tax_organization:
				tax_data = {
					'id': tax.id,
					'name': tax.name,
				}
				
				result.append(tax_data)
			
			return Response(
				json.dumps({
					'status': 'success',
					'data': result
				}),
				headers={'Content-Type': 'application/json'},
				status=200
			)
		except Exception as e:
			return Response(
				json.dumps({
					'status': 'error',
					'message': str(e)
				}),
				headers={'Content-Type': 'application/json'},
				status=500
			)
   
	@http.route('/api/country_master', auth='public', methods=['GET'], csrf=False)
	def get_country_master(self, **kw):
		try:
			# Validate token
			headers = request.httprequest.headers
			token = str(headers.get('token', ''))
			user_id = self._validate_token(token)
			
			if not user_id:
				return self._prepare_error_response('Invalid or expired token. Please authenticate again.')

			country_master = request.env['res.country'].sudo().search([])
			result = []
			
			for country in country_master:
				country_data = {
					'id': country.id,
					'name': country.name,
				}
				
				result.append(country_data)
			
			return Response(
				json.dumps({
					'status': 'success',
					'data': result
				}),
				headers={'Content-Type': 'application/json'},
				status=200
			)
		except Exception as e:
			return Response(
				json.dumps({
					'status': 'error',
					'message': str(e)
				}),
				headers={'Content-Type': 'application/json'},
				status=500
			)
   
   
	@http.route('/api/bank_master', auth='public', methods=['GET'], csrf=False)
	def get_bank_master(self, **kw):
		try:
			headers = request.httprequest.headers
			token = str(headers.get('token', ''))
			user_id = self._validate_token(token)
			
			if not user_id:
				return self._prepare_error_response('Invalid or expired token. Please authenticate again.')

			bank_master = request.env['res.bank'].sudo().search([])
			result = [] 
			
			for bank in bank_master:
				bank_data = {
					'id': bank.id,
					'name': bank.name,
				}
				
				result.append(bank_data)
			
			return Response(
				json.dumps({
					'status': 'success',
					'data': result
				}),
				headers={'Content-Type': 'application/json'},
				status=200
			)
		except Exception as e:
			return Response(
				json.dumps({
					'status': 'error',
					'message': str(e)
				}),
				headers={'Content-Type': 'application/json'},
				status=500
			)
   
	@http.route('/api/contact_type', auth='public', methods=['GET'], csrf=False)
	def get_contact_type(self, **kw):
		try:
			headers = request.httprequest.headers
			token = str(headers.get('token', ''))
			user_id = self._validate_token(token)

			if not user_id:
				return self._prepare_error_response('Invalid or expired token. Please authenticate again.')

			contact_type_field = request.env['res.partner']._fields['type']
			selection = contact_type_field.selection

			result = [{'key': key, 'label': label} for key, label in selection]

			return Response(
				json.dumps({
					'status': 'success',
					'data': result
				}),
				headers={'Content-Type': 'application/json'},
				status=200
			)

		except Exception as e:
			return Response(
				json.dumps({
					'status': 'error',
					'message': str(e)
				}),
				headers={'Content-Type': 'application/json'},
				status=500
			)
   
   
	@http.route('/api/tags', auth='public', methods=['GET'], csrf=False)
	def get_bank_master(self, **kw):
		try:
			headers = request.httprequest.headers
			token = str(headers.get('token', ''))
			user_id = self._validate_token(token)
			
			if not user_id:
				return self._prepare_error_response('Invalid or expired token. Please authenticate again.')

			tags_category = request.env['res.partner.category'].sudo().search([])
			result = []
			
			for tags in tags_category:
				bank_data = {
					'id': tags.id,
					'name': tags.name,
				}
				
				result.append(bank_data)
			
			return Response(
				json.dumps({
					'status': 'success',
					'data': result
				}),
				headers={'Content-Type': 'application/json'},
				status=200
			)
		except Exception as e:
			return Response(
				json.dumps({
					'status': 'error',
					'message': str(e)
				}),
				headers={'Content-Type': 'application/json'},
				status=500
			)   

   
