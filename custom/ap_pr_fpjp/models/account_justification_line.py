from odoo import fields, models, api


class AccountJustificationLine(models.Model):
    _inherit = 'account.justification.line'

    @api.depends_context('uid')
    @api.depends('justification_id.fpjp_ids.total_amount', 'justification_id.fpjp_ids.state', 'amount_currency', 'justification_id.fpjp_ids.line_ids', 'purchase_request_line_ids')
    def _compute_remaining_amount(self):
        for rec in self:
            total_reserved = rec.amount_currency
            remaining_amount = 0
            if rec.procurement_type_id.type == 'procurement':
                pr_ids = self.env['purchase.request.line'].search([('justification_line_id', '=', rec.id),
                                                                ('request_id.state', 'not in', ['rejected', 'canceled'])])
                total_pr_amount = sum(pr.estimated_cost for pr in pr_ids) or 0
                
                remaining_amount = total_reserved - total_pr_amount
            else:
                fpjp_ids = self.env['account.fpjp.line'].search([('justification_line_id', '=', rec.id),
                                                                ('account_fpjp_id.state', 'not in', ['reject', 'return', 'cancel'])])
                total_fpjp_amount = sum(fpjp.amount_total for fpjp in fpjp_ids) or 0
                remaining_amount = total_reserved - total_fpjp_amount

            rec.remaining_amount = remaining_amount