
# -*- coding: utf-8 -*-
###############################################################################
#    License, author and contributors information in:                         #
#    __manifest__.py file at the root folder of this module.                  #
###############################################################################

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from odoo.tools import DEFAULT_SERVER_DATE_FORMAT as DATE_FORMAT, DEFAULT_SERVER_DATETIME_FORMAT as DATETIME_FORMAT
from datetime import datetime
from dateutil.relativedelta import *
from odoo.tools.float_utils import float_is_zero


class WehaPurchaseOrder(models.Model):
    _inherit = 'purchase.order'


    @api.depends('date_purchase')
    def _compute_validity_period(self):
        for record in self:
            # date_purchase_obj = datetime.strptime(str(record.date_purchase), DATE_FORMAT)
            if record.latest_delivery_date:
                record.validity_period = record.latest_delivery_date + relativedelta(hours=30)
            else:
                record.validity_period = False

    @api.depends('picking_ids', 'picking_ids.state_fppr')
    def _compute_receipt_status(self):
        for order in self:
            if not order.picking_ids or all(p.state_fppr == 'cancel' for p in order.picking_ids):
                order.receipt_status = False
            elif all(p.state_fppr in ['ap_invoice'] for p in order.picking_ids):
                order.receipt_status = 'full'
            elif any(p.state_fppr == 'pending_approval' for p in order.picking_ids):
                order.receipt_status = 'partial'
            else:
                order.receipt_status = 'pending'
        
        super(WehaPurchaseOrder, self)._compute_receipt_status()

    @api.depends('new_state', 'order_line.qty_to_invoice')
    def _get_invoiced(self):
        precision = self.env['decimal.precision'].precision_get('Product Unit of Measure')
        for order in self:
            if order.new_state not in ('open', 'done'):
                order.invoice_status = 'no'
                continue

            if any(
                not float_is_zero(line.qty_to_invoice, precision_digits=precision)
                for line in order.order_line.filtered(lambda l: not l.display_type)
            ):
                order.invoice_status = 'to invoice'
            elif (
                all(
                    float_is_zero(line.qty_to_invoice, precision_digits=precision)
                    for line in order.order_line.filtered(lambda l: not l.display_type)
                )
                and order.invoice_ids
            ):
                order.invoice_status = 'invoiced'
            else:
                order.invoice_status = 'no'

        super(WehaPurchaseOrder, self)._get_invoiced()
        
    invoice_status = fields.Selection([
        ('no', 'Nothing to Bill'),
        ('to invoice', 'Waiting Bills'),
        ('invoiced', 'Fully Billed'),
    ], string='Billing Status', compute='_get_invoiced', store=True, readonly=True, copy=False, default='no')
    receipt_status = fields.Selection([
        ('pending', 'Not Received'),
        ('partial', 'Partially Received'),
        ('full', 'Fully Received'),
    ], string='Receipt Status', compute='_compute_receipt_status', store=True,
       help="Red: Late\n\
            Orange: To process today\n\
            Green: On time")
    date_purchase = fields.Date(string='PO Date', default=fields.Date.context_today)
    validity_period = fields.Date(string='PO Validity Period', compute='_compute_validity_period' )
    delivery_location_id = fields.Many2one(
        comodel_name="stock.location",
        string="Delivery Location",
        help="Specify the delivery location for this transaction.",
        domain=[('usage','=', 'internal')],
        copy=False)
    

    # @api.onchange('date_purchase')
    # def _onchange_po_validity(self):
    #     if self.new_state == "open":
    #         date_val = self.date_purchase + relativedelta(days=30)
    #         self.validity_periode = date_val
    

    @api.onchange('bidding_rfq_id')
    def _onchange_rfq(self):
        for rec in self.bidding_rfq_id:
            self.partner_id = rec.partner_id.id
            contact_id = self.env['res.partner'].search([('parent_id', '=', rec.partner_id.id)], limit=1)
            self.partner_contact_id = contact_id.id
            self.email_contact = contact_id.email


    

