# -*- coding: utf-8 -*-

# from odoo import models, fields, api


# class api_master(models.Model):
#     _name = 'api_master.api_master'
#     _description = 'api_master.api_master'

#     name = fields.Char()
#     value = fields.Integer()
#     value2 = fields.Float(compute="_value_pc", store=True)
#     description = fields.Text()
#
#     @api.depends('value')
#     def _value_pc(self):
#         for record in self:
#             record.value2 = float(record.value) / 100

