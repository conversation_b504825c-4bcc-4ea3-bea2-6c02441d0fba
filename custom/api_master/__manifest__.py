# -*- coding: utf-8 -*-
{
    'name': "API Master",

    'summary': "API Module for accessing master data",

    'description': """
This module provides REST APIs to access master data from Odoo.
Currently supports access to payment terms data.
    """,

    'author': "AkuntPlus",
    'website': "https://www.akuntplus.com",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/15.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': 'API',
    'version': '1.0',

    # any module necessary for this one to work correctly
    'depends': ['base'],

    # always loaded
    'data': [
        # 'security/ir.model.access.csv',
        'views/views.xml',
        'views/templates.xml',
    ],
    # only loaded in demonstration mode
    'demo': [
        'demo/demo.xml',
    ],
}

