from odoo import api, fields, models


class BiddingRfqApproval(models.Model):
    _name = 'bidding.rfq.approval'
    _inherit = 'approval.history.mixin'
    _description = 'Bidding Rfq Approval'

    rfq_id = fields.Many2one('bidding.rfq', 'Bidding Rfq',
                               ondelete='cascade')
    reassign_employee_ids = fields.Many2many(
        'hr.employee',
        'employee_reassign_hierarchy_bidding_rfq_rel',
        'bidding_rfq_order_reassign_approval_id',
        'employee_id',
        string='Reassign Employees'
    )

    employee_ids = fields.Many2many(
        'hr.employee',
        'employee_approval_hierarchy_bidding_rfq_rel',  # tetap
        'bidding_rfq_order_approval_id',
        'employee_id',
        string='Employees'
    )

    approval_employee_ids = fields.Many2many(
        'hr.employee',
        'approval_employee_hierarchy_bidding_rfq_rel',
        'bidding_rfq_order_approval_approval_id',
        'approval_employee_id',
        string='Approval Employees'
    )
    

class BiddingRfqApprovalDetail(models.Model):
    _name = 'bidding.rfq.approval.detail'
    _inherit = 'approval.history.detail.mixin'
    _description = 'Bidding Rfq Approval'

    rfq_id = fields.Many2one('bidding.rfq', 'Bidding Rfq',
                               ondelete='cascade')
    

class BiddingRfqReassign(models.Model):
    _name = 'bidding.rfq.reassign'
    _description = 'Bidding Rfq Reassign'
    

    rfq_id = fields.Many2one('bidding.rfq', 'Bidding Rfq',
                               ondelete='cascade')
    from_employee_id = fields.Many2one('hr.employee', string='From Employee')
    to_employee_id = fields.Many2one('hr.employee', string='To Employee') 
