<odoo>
    <data>
        <record id="view_bidding_rfq_message_form" model="ir.ui.view">
            <field name="name">view.bidding.rfq.message.form</field>
            <field name="model">bidding.rfq.message</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <group>
                                <field name="employee_id" required="1"/>
                                <field name="date"/>
                                <field name="note"/>
                                <field name="state" required="1"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="view_bidding_rfq_message_tree" model="ir.ui.view">
            <field name="name">view.bidding.rfq.message.tree</field>
            <field name="model">bidding.rfq.message</field>
            <field name="arch" type="xml">
                <list editable="bottom">
                    <field name="employee_id" required="1"/>
                    <field name="date"/>
                    <field name="note"/>
                    <field name="state" required="1"/>
                </list>
            </field>
        </record>
    </data>
</odoo>
