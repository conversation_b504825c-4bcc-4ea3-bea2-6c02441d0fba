from logging.handlers import TimedRotatingFileHandler
from odoo import _, fields, models
import io
import os
from abc import ABC
import paramiko
import mt940
import logging


class ProtocolHandler(ABC):
    @staticmethod
    def get_handler(protocol, logger=None):
        if not logger:
            logger = logging.getLogger(__name__)
        
        if protocol == "sftp":
            return SFTPHandler(logger=logger)
        else:
            raise ValueError(_("Unsupported protocol: %s") % protocol)

    def connect(self, host, port, username, password):
        pass

    def close(self):
        pass

    def get_remote_files(self, path):
        pass

    def move_to_dir(self, file_name, source_path, dest_path, replace=True):
        pass


class LoggingException(Exception):
    def __init__(self, message):
        super().__init__(message)


class SFTPHandler(ProtocolHandler):
    sftp = None
    transport = None
    logger = None

    def __init__(self, logger):
        self.logger = logger

    def connect(self, host, port, username, password):
        # self.logger.info(f"Opening connection {host}:{port}")
        self.transport = paramiko.Transport((host, port))
        self.transport.connect(username=username, password=password)
        self.sftp = paramiko.SFTPClient.from_transport(self.transport)

    def close(self):
        # self.logger.info(f"Closing connection {self.transport.getpeername()}")
        if self.sftp:
            self.sftp.close()
        if self.transport:
            self.transport.close()
        if self.logger:
            for handler in self.logger.handlers[:]:
                if isinstance(handler, TimedRotatingFileHandler):
                    handler.close()
                    self.logger.removeHandler(handler)

    def get_remote_files(self, path):
        self.logger.info(f"Listing remote files in {path}")
        files = self.sftp.listdir(path)
        self.logger.info(f"Found {len(files)} {files}")
        return files

    def __create_dir(self, path, mode):
        self.sftp.mkdir(path, mode=mode)

    def move_to_dir(self, file_names, source_path, dest_path, replace=True):
        try:
            self.sftp.stat(dest_path)
        except FileNotFoundError:
            self.logger.info(f"Path not found, creating: {dest_path}")
            self.__create_dir(dest_path, mode=0o755)

        for file_name in file_names:
            source_file_path = f"{source_path.rstrip('/')}/{file_name}"
            dest_file_path = f"{dest_path.rstrip('/')}/{file_name}"
            if replace:
                try:
                    # self.logger.info(f"Removing existing file: {dest_file_path}")
                    self.sftp.remove(dest_file_path)
                except FileNotFoundError as e:
                    pass
                except Exception as e:
                    self.logger.error(f"{e}")

            self.logger.info(f"Moving remote file {source_file_path} -> {dest_file_path}")
            self.sftp.rename(source_file_path, dest_file_path)


class SftpConfig(models.Model):
    _name = "sftp.config"
    _description = "SFTP Configuration"

    PROTOCOL = "sftp"
    FILE_ENCODING = "utf-8"
    EXTENSIONS = [".txt"]
    LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'
    MSG_NO_FILE_FOUND = "No files found"

    name = fields.Char(required=True)
    host = fields.Char(required=True)
    port = fields.Integer(default=22)
    username = fields.Char(required=True)
    password = fields.Char(required=True)

    active = fields.Boolean(default=True)

    base_dir = fields.Char(
        string="Base Dir",
        required=True,
        help=("Root directory path on the remote FTP server"),
    )

    new_dir = fields.Char(string="New Dir", required=True, default="new", help=("New file will be downloaded from this directory"))
    success_dir = fields.Char(string="Success Dir", required=True, default="success", help=("Imported file will be moved to this directory"))
    failed_dir = fields.Char(string="Failed Dir", required=True, default="error",  help=("If the file is invalid or the import is failed, the file will be moved to this directory"))
    log_file = fields.Char(String="Log File", required=True, default="/tmp/mt940.log", help=("Path to the log file in local"))
    log_backup_count = fields.Integer(string="Backup Count (days)", required=True, default=7)

    # test connection button
    def test_connection(self):
        handler = None
        try:
            logger = self.__get_runtime_logger(self.log_file, self.log_backup_count)
            handler = ProtocolHandler.get_handler(self.PROTOCOL, logger)
            handler.connect(self.host, self.port, self.username, self.password)
            return self.__display_notification(
                "Test connection", "Test connection successful", "success"
            )
        except Exception as e:
            return self.__display_notification("Test connection", str(e), "danger")
        finally:
            if handler:
                handler.close()

    # test import button
    def test_import(self):
        logger = self.__get_runtime_logger(self.log_file, self.log_backup_count)
        logger.info("===== Start Test Import =====")

        files_success, files_failed, exceptions = self.import_file()

        if files_success:
            self.move_to_success(files_success)
        if files_failed:
            self.move_to_failed(files_failed)
        
        for handler in logger.handlers[:]:
            if isinstance(handler, TimedRotatingFileHandler):
                handler.close()
                logger.removeHandler(handler)

        if exceptions and files_success:
            return self.__display_notification(
                "Test import",
                f"{len(files_success)} file(s) imported. " + "\n".join(str(e) for e in exceptions),
                "warning",
            )
        elif exceptions:
            return self.__display_notification("Test import", "\n".join(str(e) for e in exceptions), "danger")
        else:
            return self.__display_notification("Test import", f"{len(files_success)} file(s) imported, {len(files_failed)} file(s) failed", "info",)

    # run by scheduler
    def scheduled_import(self):
        configs = self.env['sftp.config'].search([('active', '=', True)])
        for config in configs:
            logger = self.__get_runtime_logger(config.log_file, config.log_backup_count)
            logger.info("===== Start Scheduler =====")
            try:
                files_success, files_failed, exceptions = config.import_file()
                if not files_success and not files_failed:
                    logger.info(self.MSG_NO_FILE_FOUND)

                if files_success:
                    config.move_to_success(files_success)
                if files_failed:
                    config.move_to_failed(files_failed)
            except Exception as e:
                logger.error(e)
            finally:
                for handler in logger.handlers[:]:
                    if isinstance(handler, TimedRotatingFileHandler):
                        handler.close()
                        logger.removeHandler(handler)

    # download dan import file dari remote server
    def import_file(self):
        handler = None
        files_success = []
        files_failed = []
        exceptions = []

        try:
            logger = self.__get_runtime_logger(self.log_file, self.log_backup_count)
            handler = ProtocolHandler.get_handler(self.PROTOCOL, logger)
            handler.connect(self.host, self.port, self.username, self.password)
            remote_files = handler.get_remote_files(os.path.join(self.base_dir, self.new_dir))

            for file_name in remote_files:
                remote_file_path = (f"{self.base_dir.rstrip('/')}/{self.new_dir}/{file_name}")
                extension = os.path.splitext(remote_file_path)[1]
                
                # Jika extension bukan EXTENSIONS, anggap sebagai failed file
                if extension.lower() not in self.EXTENSIONS:
                    files_failed.append(file_name)
                    logger.error(f"File ignored: {remote_file_path}")
                    continue

                logger.info(f"Parsing {remote_file_path}")
                with handler.sftp.file(remote_file_path, "r") as remote_file:
                    try:
                        content = remote_file.read()
                        if not content or len(content) == 0:
                            logger.warning(f"File is empty: {remote_file_path}")
                            continue

                        text_io = io.StringIO(content.decode(self.FILE_ENCODING))
                        if not self.__is_valid_mt940(text_io):
                            raise ValueError(f"Invalid MT940 file {remote_file_path}")

                        account_id = self.__get_account_identification(text_io)
                        journal_id = self.env["account.journal"].search(
                            [("bank_account_id.account_number", "=", account_id)],
                            limit=1,
                        )

                        # Pindah ke direktori failed jika no rek tidak ditemukan
                        if not journal_id:
                            raise ValueError(f"Bank account number not found: {account_id}")

                        args = {
                            "file": text_io.getvalue(),
                            "extension": extension,
                            "statement_type": "MT940",
                            "bank_account_id": journal_id.id,
                        }

                        logger.info(f"Importing {remote_file_path}")
                        statements = self.env["bank.statement.import.mt940"].create_bni_statements(**args)
                        logger.info(f"{len(statements)} statements imported from {remote_file_path}")
                        files_success.append(file_name)
                    except Exception as e:
                        files_failed.append(file_name)
                        logger.error(e)
                        exceptions.append(e)
        except FileNotFoundError as e:
            # remote files not found
            logger.info(self.MSG_NO_FILE_FOUND)
            exceptions.append(e)
        except Exception as e:
            logger.error(e)
            exceptions.append(e)
            # raise e
        finally:
            if handler:
                handler.close()
        
        return files_success, files_failed, exceptions

    # pindahkan file yang telah diimport ke direktori success
    def move_to_success(self, file_names):
        handler = None
        try:
            logger = self.__get_runtime_logger(self.log_file, self.log_backup_count)
            handler = ProtocolHandler.get_handler(self.PROTOCOL, logger)
            handler.connect(self.host, self.port, self.username, self.password)
            source_path = os.path.join(self.base_dir, self.new_dir)
            dest_path = os.path.join(self.base_dir, self.success_dir)
            handler.move_to_dir(file_names, source_path, dest_path)
        except Exception as e:
            logger.error(e)
        finally:
            if handler:
                handler.close()

    # pindahkan file yang gagal diimport ke direktori failed
    def move_to_failed(self, file_names):
        handler = None
        try:
            logger = self.__get_runtime_logger(self.log_file, self.log_backup_count)
            handler = ProtocolHandler.get_handler(self.PROTOCOL, logger)
            handler.connect(self.host, self.port, self.username, self.password)
            source_path = os.path.join(self.base_dir, self.new_dir)
            dest_path = os.path.join(self.base_dir, self.failed_dir)
            handler.move_to_dir(file_names, source_path, dest_path)
        except Exception as e:
            logger.error(e)
        finally:
            if handler:
                handler.close()

    # display notification ke user (for test buttons only)
    def __display_notification(self, title, message, type):
        return {
            "type": "ir.actions.client",
            "tag": "display_notification",
            "params": {
                "title": title,
                "message": message,
                "type": type,
                "sticky": False,
            },
        }
    
    # display multiple notification ke user (for test buttons only)
    def __display_multiple_notification(self, title, messages, notif_type):
        bullet_html = "<ul>"
        for msg in messages:
            bullet_html += f"<li>{msg}</li>"
        bullet_html += "</ul>"
        
        return self.__display_notification(title, bullet_html, notif_type)

    # baca nomor rekening dari file MT940
    def __get_account_identification(self, text_io):
        try:
            text_io.seek(0)
            parsed = mt940.parse(text_io)
            return parsed.data.get("account_identification")
        except Exception as e:
            raise e
    
    # cek apakah file MT940 valid
    def __is_valid_mt940(self, text_io):
        try:
            text_io.seek(0)
            parsed = mt940.parse(text_io)
            if parsed.transactions or parsed.data.get('account_identification'):
                return True
            return False
        except Exception as e:
            return False
        
    def __get_runtime_logger(self, filename=None, backupCount=7):
        logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        logger.setLevel(logging.INFO)

        if any(isinstance(h, TimedRotatingFileHandler) and h.baseFilename == filename or \
               isinstance(h, logging.StreamHandler) for h in logger.handlers):
            return logger

        formatter = logging.Formatter(self.LOG_FORMAT)
        handler = None
        try:
            self.__validate_log_filename(filename)
            handler = TimedRotatingFileHandler(filename, when='midnight', interval=1, backupCount=backupCount)
            # handler = TimedRotatingFileHandler(filename, when='S', interval=10, backupCount=backupCount)
        except LoggingException as e:
            handler = logging.StreamHandler()
            print(f"Logger filename validation error: {e}. Using default console handler.")

        handler.setFormatter(formatter)
        logger.addHandler(handler)
        return logger
    
    # validasi file log
    def __validate_log_filename(self, filename):
        if not filename:
            raise LoggingException("Filename is empty")

        if not os.path.exists(filename):
            # Buat direktori jika belum ada
            os.makedirs(os.path.dirname(filename), exist_ok=True)
        else:
            # Cek write access ke file
            try:
                with open(filename, 'a'):
                    pass
            except Exception as e:
                raise LoggingException(f"Cannot write log file {filename}: {e}")
