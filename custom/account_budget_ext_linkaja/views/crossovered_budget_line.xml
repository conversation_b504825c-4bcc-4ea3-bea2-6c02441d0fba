<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="account_budget_base.view_crossovered_budget_line_search" model="ir.ui.view">
        <field name="name">account.budget.line.search</field>
        <field name="model">crossovered.budget.lines</field>
        <field name="arch" type="xml">
            <search string="List Budget User">
                <field name="general_budget_id"/>
                <field name="crossovered_budget_id"/>
                <field name="rkap_category_id"/>
                <field name="rkap_type_id"/>
                <field name="group_id"/>
                <filter name="filter_not_cancelled" string="Not Cancelled" domain="[('crossovered_budget_state','!=','cancel')]"/>
                <separator/>
                <filter string="Date" name="filter_date_from" date="date_from"/>
                <group expand="0" string="Group By">
                    <filter name="group_crossevered_budgdet_id" string="Budgets" domain="[]" context="{'group_by':'crossovered_budget_id'}"/>
                </group>
            </search>

        </field>
    </record>

    <record model="ir.ui.view" id="view_list_budget_user_tree">
        <field name="name">crossovered.budget.line.tree</field>
        <field name="model">crossovered.budget.lines</field>
        <field name="arch" type="xml">
            <list string="List Budget User" create="0" edit="0">
                <field name="currency_id" column_invisible="True"/>
                <field name="crossovered_budget_id" column_invisible="True"/>
                <field name="general_budget_id" />
                <field name="rkap_type_id" />
                <field name="rkap_category_id" />
                <field name="rkap_code" />
                <field name="group_id" />
                <field name="direktorat_id"/>
                <field name="date_from"  />
                <field name="date_to"  />
                <field name="planned_amount" string="RKAP Amount"/>
                <field name="justifikasi_reserve_amount"/>
                <button type="object" name="action_open_Justif_res_entries" string="Justif Entries"
                icon="fa-arrow-circle-o-right"/>
                <field name="fpjp_reserve_amount"/>
                <button type="object" name="action_open_FPJP_res_entries" string="FPJP Entries"
                icon="fa-arrow-circle-o-right"/>
                <field name="pr_reserve_amount"/>
                <button type="object" name="action_open_pr_res_entries" string="PR Entries"
                icon="fa-arrow-circle-o-right"/>
                <field name="po_reserved"/>
                <button type="object" name="action_open_po_res_entries" string="PO Entries"
                icon="fa-arrow-circle-o-right"/>
                <field name="vendor_bill_amount"/>
                <button type="object" name="action_open_bill_res_entries" string="PO Entries"
                icon="fa-arrow-circle-o-right"/>
                <field name="payment_amount"/>
                <button type="object" name="action_open_payment_res_entries" string="PO Entries"
                icon="fa-arrow-circle-o-right"/>
                <field name="relocation_in_amount"/>
                <button type="object" name="action_open_relocation_in" string="Relocation in"
                icon="fa-arrow-circle-o-right"/>
                <field name="relocation_out_amount"/>
                <button type="object" name="action_open_relocation_out" string="Relocation out"
                icon="fa-arrow-circle-o-right"/>                
                <field name="practical_amount"/>
                <field name="remaining_amount"/>
                <field name="achievement_amount"/>
                <field name="flag_lintas_tahun_active"/>
            </list>
        </field>
    </record>
    <record model="ir.ui.view" id="view_list_budget_user_form">
        <field name="name">crossovered.budget.line.form</field>
        <field name="model">crossovered.budget.lines</field>
        <field name="arch" type="xml">
            <form string="List Budget User" create="0" edit="0">
                <sheet>
                    <group col="4">
                        <field name="company_id" invisible="1"/>
                        <field name="currency_id" invisible="1"/>
                        <field name="crossovered_budget_state" invisible="1"/>
                        <field name="general_budget_id" />
                        <field name="rkap_type_id" />
                        <field name="rkap_category_id" />
                        <field name="rkap_code" />
                        <field name="group_id" />
                        <field name="direktorat_id"/>
                        <field name="date_from"  />
                        <field name="date_to"  />
                        <field name="planned_amount" string="RKAP Amount"/>
                        <field name="justifikasi_reserve_amount"/>
                        <field name="fpjp_reserve_amount"/>
                        <field name="pr_reserve_amount"/>
                        <field name="po_reserved"/>
                        <field name="vendor_bill_amount"/>
                        <field name="payment_amount"/>
                        <field name="relocation_in_amount"/>
                        <field name="relocation_out_amount"/>
                        <field name="practical_amount"/>
                        <field name="remaining_amount"/>
                        <field name="achievement_amount"/>
                        <field name="flag_lintas_tahun_active"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="act_account_analytic_account_cb_lines" model="ir.actions.act_window">
        <field name="name">List Budget User</field>
        <field name="res_model">crossovered.budget.lines</field>
        <field name="view_mode">list,form</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'list', 'view_id': ref('view_list_budget_user_tree')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('view_list_budget_user_form')})]"/>
        <field name="domain">[('crossovered_budget_id.state', '=', 'validate')]</field>
    </record>

    <menuitem parent="account_budget_control_ent.menu_budget"
              id="menu_act_list_budget_users_view"
              action="act_account_analytic_account_cb_lines" sequence="20"
              groups="account.group_account_readonly"/>

</odoo>
