# -*- coding: utf-8 -*-

from datetime import date, datetime

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
from datetime import timedelta
from dateutil.relativedelta import relativedelta
import logging
_logger = logging.getLogger(__name__)


class CrossOverredBudget(models.Model):
    _inherit = 'crossovered.budget'

    def _get_default_general_budget_ids(self):
        return self.env['account.budget.post'].search([]).ids

    description = fields.Char(string="Description")
    type_id = fields.Many2one('account.analytic.plan', 'RKAP Type')
    is_multiple_year = fields.Boolean(string="Multiple Year", store=True, related='period_id.is_multiple_year')
    employee_id = fields.Many2one('hr.employee', 'Responsible', default=lambda self: self.env.user.employee_id)
    user_id = fields.Many2one(string='Responsible User')
    year = fields.Char('Year', compute='_compute_year', store=True)
    employee_id = fields.Many2one('hr.employee', 'Submitter Name')
    general_budget_ids = fields.Many2many('account.budget.post', string='Budgetary Position', compute='_compute_general_budget_ids', default=_get_default_general_budget_ids)
    flag_lintas_tahun_active = fields.Boolean('Lintas Tahun', compute='_compute_lintas_tahun')
    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirm', 'Confirmed'),
        ('validate', 'Validated'),
        ('done', 'Close'),
        ('cancel', 'Cancelled')
    ])

    @api.depends('date_to')
    def _compute_year(self):
        for rec in self:
            rec.year = str(rec.date_to.year) if rec.date_to else -1

    @api.depends('period_id', 'crossovered_budget_line_add', 'crossovered_budget_line_add.general_budget_id')
    def _compute_general_budget_ids(self):
        for rec in self:
            list_budgetary_position = []
            for line in rec.crossovered_budget_line_add:
                list_budgetary_position.append(line.general_budget_id.id)
            if list_budgetary_position != []:
                general_budget_ids = self.env['account.budget.post'].search([('id', 'not in', list_budgetary_position)]).ids
            else:
                general_budget_ids = self.env['account.budget.post'].search([]).ids
            fix_general_budget_ids = []
            for gb in general_budget_ids:
                exist_data = self.env['crossovered.budget.lines'].search([('crossovered_budget_id.period_id', '=', self.period_id.id), ('general_budget_id', '=', gb), ('crossovered_budget_id.state', '!=', 'cancel')])
                if not exist_data:
                    fix_general_budget_ids.append(gb)
            rec.general_budget_ids = [(6, 0, fix_general_budget_ids)]

    def _compute_lintas_tahun(self):
        for rec in self:
            flag_lintas_tahun_active = False
            for line in rec.crossovered_budget_line_add:
                if line.flag_lintas_tahun_active:
                    flag_lintas_tahun_active = True
                    break
            rec.flag_lintas_tahun_active = flag_lintas_tahun_active

    @api.onchange('crossovered_budget_line_add')
    def onchange_general_budget_id(self):
        self._compute_general_budget_ids()


class CrossOverredBudgetLines(models.Model):
    _inherit = "crossovered.budget.lines"

    def _default_idr_currency(self):
        return self.env['res.currency'].search([('name', '=', 'IDR')], limit=1)

    budget_category_id = fields.Many2one('account.budget.category', 'Budget Category')
    rkap_code = fields.Char('RKAP Code')
    category_id = fields.Many2one('account.analytic.account', 'Analytic Account')
    rkap_category_id = fields.Many2one('account.rkap.category', 'RKAP Category')
    rkap_type_id = fields.Many2one('account.rkap.type', 'RKAP Type')
    type_id = fields.Many2one('account.analytic.plan', 'Account Analytic Plan')
    pr_reserve_amount = fields.Monetary(currency_field='idr_currency_id')
    po_reserve_amount = fields.Monetary(currency_field='idr_currency_id')
    remaining_amount = fields.Monetary(compute='_compute_remaining_amount', store=True, currency_field='idr_currency_id')
    justifikasi_reserve_amount = fields.Monetary('Justifikasi Reserve', currency_field='idr_currency_id')
    fpjp_reserve_amount = fields.Monetary('FPJP Reserve', currency_field='idr_currency_id')
    vendor_bill_amount = fields.Monetary('Vendor Bill', currency_field='idr_currency_id', compute='_compute_vendor_bill_amount')
    payment_amount = fields.Monetary('Payment', currency_field='idr_currency_id', compute="_compute_payment_amount")
    relocation_in_amount = fields.Monetary('Relocation In', currency_field='idr_currency_id')
    relocation_out_amount = fields.Monetary('Relocation Out', currency_field='idr_currency_id')
    achievement_amount = fields.Monetary('Achievement', currency_field='idr_currency_id')
    lt_planned_amount = fields.Monetary('NY Planned Amount')
    lt_pr_reserve_amount = fields.Monetary(string="NY PR Reserve", compute='_compute_total_reserve_remaining_lt')
    lt_po_reserve_amount = fields.Monetary(string="NY PO Reserve", compute='_compute_total_reserve_remaining_lt')
    lt_remaining_amount = fields.Monetary(string="NY Budget Remaining", compute='_compute_remaining_amount_lt')
    lt_justifikasi_reserve_amount = fields.Monetary('NY Justifikasi Reserve')
    lt_fpjp_reserve_amount = fields.Monetary('NY FPJP Reserve')
    lt_vendor_bill_amount = fields.Monetary('NY Vendor Bill')
    lt_payment_amount = fields.Monetary('NY Payment')
    lt_relocation_in_amount = fields.Monetary('NY Relocation In')
    lt_relocation_out_amount = fields.Monetary('NY Relocation Out')
    lt_achievement_amount = fields.Monetary('NY Achievement')
    lt_practical_amount = fields.Monetary(compute='_compute_practical_amount_lt', string='NY Practical Amount')
    practical_amount = fields.Monetary(currency_field='idr_currency_id')
    flag_lintas_tahun_active = fields.Boolean(default=False, string='Flag Lintas Tahun')
    periode = fields.Char('Periode', compute='_compute_periode')
    year_id = fields.Many2one('crossovered.budget.period.line', 'Year')
    rkap_name = fields.Char('RKAP Name')
    general_budget_id = fields.Many2one(string='RKAP Name', required=False)
    group_id = fields.Many2one('hr.department', string='Group', tracking=True, context={'hierarchical_naming': False})
    direktorat_id = fields.Many2one('hr.department', string='Direktorat',tracking=True, context={'hierarchical_naming': False} , compute='_compute_direktorat_id', store=True)
    analytic_account_id = fields.Many2one('account.analytic.account', 'Analytic Account', required=False)
    lt_month1 = fields.Float(string="Jan")
    lt_month2 = fields.Float(string="Feb")
    lt_month3 = fields.Float(string="Mar")
    lt_month4 = fields.Float(string="Apr")
    lt_month5 = fields.Float(string="May")
    lt_month6 = fields.Float(string="Jun")
    lt_month7 = fields.Float(string="Jul")
    lt_month8 = fields.Float(string="Aug")
    lt_month9 = fields.Float(string="Sep")
    lt_month10 = fields.Float(string="Oct")
    lt_month11 = fields.Float(string="Nov")
    lt_month12 = fields.Float(string="Dec")
    lt_emcumbren_month1 = fields.Float(string="Jan", compute='_compute_lt_emcumbren_month1')
    lt_emcumbren_month2 = fields.Float(string="Feb", compute='_compute_lt_emcumbren_month2')
    lt_emcumbren_month3 = fields.Float(string="Mar", compute='_compute_lt_emcumbren_month3')
    lt_emcumbren_month4 = fields.Float(string="Apr", compute='_compute_lt_emcumbren_month4')
    lt_emcumbren_month5 = fields.Float(string="May", compute='_compute_lt_emcumbren_month5')
    lt_emcumbren_month6 = fields.Float(string="Jun", compute='_compute_lt_emcumbren_month6')
    lt_emcumbren_month7 = fields.Float(string="Jul", compute='_compute_lt_emcumbren_month7')
    lt_emcumbren_month8 = fields.Float(string="Aug", compute='_compute_lt_emcumbren_month8')
    lt_emcumbren_month9 = fields.Float(string="Sep", compute='_compute_lt_emcumbren_month9')
    lt_emcumbren_month10 = fields.Float(string="Oct", compute='_compute_lt_emcumbren_month10')
    lt_emcumbren_month11 = fields.Float(string="Nov", compute='_compute_lt_emcumbren_month11')
    lt_emcumbren_month12 = fields.Float(string="Dec", compute='_compute_lt_emcumbren_month12')
    lt_actual_month1 = fields.Float(string="Jan", compute='_compute_lt_actual_month1')
    lt_actual_month2 = fields.Float(string="Feb", compute='_compute_lt_actual_month2')
    lt_actual_month3 = fields.Float(string="Mar", compute='_compute_lt_actual_month3')
    lt_actual_month4 = fields.Float(string="Apr", compute='_compute_lt_actual_month4')
    lt_actual_month5 = fields.Float(string="May", compute='_compute_lt_actual_month5')
    lt_actual_month6 = fields.Float(string="Jun", compute='_compute_lt_actual_month6')
    lt_actual_month7 = fields.Float(string="Jul", compute='_compute_lt_actual_month7')
    lt_actual_month8 = fields.Float(string="Aug", compute='_compute_lt_actual_month8')
    lt_actual_month9 = fields.Float(string="Sep", compute='_compute_lt_actual_month9')
    lt_actual_month10 = fields.Float(string="Oct", compute='_compute_lt_actual_month10')
    lt_actual_month11 = fields.Float(string="Nov", compute='_compute_lt_actual_month11')
    lt_actual_month12 = fields.Float(string="Dec", compute='_compute_lt_actual_month12')
    lt_date_from = fields.Date('Start Date')
    lt_date_to = fields.Date('End Date')
    manual_currency_rate_active = fields.Boolean(string="Apply Manual Exchange")
    manual_currency_rate = fields.Float(string="Rate", digits=(12, 4))
    rkap_amount = fields.Monetary('RKAP Amount', currency_field='idr_currency_id', compute='_compute_amount_currency')
    rkap_amount_currency = fields.Monetary('RKAP Amount Currency')
    lt_rkap_amount = fields.Monetary('NY RKAP Amount')
    planned_amount = fields.Monetary(compute='_compute_amount_currency', currency_field='idr_currency_id')
    planned_amount_currency = fields.Monetary('Planned Amount Currency', currency_field='currency_id')
    currency_id = fields.Many2one('res.currency', store=True, default=lambda self: self.env.user.company_id.currency_id.id, required=True)
    idr_currency_id = fields.Many2one('res.currency', 'IDR Currency', default=_default_idr_currency)
    po_reserved = fields.Monetary('Po Reserved', compute="_compute_po_reserved", currency_field='idr_currency_id')
    
    @api.depends('group_id')
    def _compute_direktorat_id(self):
        for record in self:
            if record.group_id:
                record.direktorat_id = record.group_id.parent_id
            else:
                record.direktorat_id = False
            
    @api.depends('rkap_amount_currency', 'planned_amount_currency', 'currency_id', 'company_id', 'planned_amount', 'manual_currency_rate_active', 'manual_currency_rate')
    def _compute_amount_currency(self):
        for rec in self:
            if rec.currency_id != rec.idr_currency_id:
                if rec.manual_currency_rate_active:
                    rec.rkap_amount = rec.rkap_amount_currency * rec.manual_currency_rate
                    rec.planned_amount = rec.planned_amount_currency * rec.manual_currency_rate
                else:
                    rec.rkap_amount = rec.currency_id._convert(rec.rkap_amount_currency, rec.idr_currency_id, rec.company_id, date=datetime.today())
                    rec.planned_amount = rec.currency_id._convert(rec.planned_amount_currency, rec.idr_currency_id, rec.company_id, date=datetime.today())
            else:
                rec.rkap_amount = rec.rkap_amount_currency
                rec.planned_amount = rec.planned_amount_currency

    @api.depends('planned_amount', 'relocation_in_amount', 'justifikasi_reserve_amount', 'relocation_out_amount')
    def _compute_remaining_amount(self):
        for rec in self:
            rec.remaining_amount = rec.planned_amount + rec.relocation_in_amount - (rec.justifikasi_reserve_amount + rec.relocation_out_amount)

    @api.depends('lt_planned_amount', 'lt_relocation_in_amount', 'lt_justifikasi_reserve_amount', 'lt_relocation_out_amount')
    def _compute_remaining_amount_lt(self):
        for rec in self:
            rec.lt_remaining_amount = rec.lt_planned_amount + rec.lt_relocation_in_amount - (rec.lt_justifikasi_reserve_amount + rec.lt_relocation_out_amount)

    @api.depends('fpjp_reserve_amount', 'po_reserve_amount')
    def _compute_practical_amount(self):
        for rec in self:
            rec.practical_amount = rec.fpjp_reserve_amount + rec.po_reserve_amount

    @api.depends('lt_fpjp_reserve_amount', 'lt_po_reserve_amount')
    def _compute_practical_amount_lt(self):
        for rec in self:
            rec.lt_practical_amount = rec.lt_fpjp_reserve_amount + rec.lt_po_reserve_amount

    @api.onchange('flag_lintas_tahun_active')
    def onchange_flag_lintas_tahun_active(self):
        if self.flag_lintas_tahun_active:
            self.lt_date_from = self.date_from + relativedelta(years=1)
            self.lt_date_to = self.date_to + relativedelta(years=1)

    @api.depends('date_from', 'date_to')
    def _compute_periode(self):
        for rec in self:
            date_from = rec.date_from.strftime('%m/%d/%Y') if rec.date_from else ''
            date_to = rec.date_to.strftime('%m/%d/%Y') if rec.date_to else ''
            rec.periode =date_from + ' - ' + date_to

    @api.onchange('general_budget_id', 'rkap_category_id')
    def onchange_general_budget_id(self):
        if self.general_budget_id and self.crossovered_budget_id:
            self.crossovered_budget_id._compute_general_budget_ids()
            self.rkap_code = self.general_budget_id.rkap_code
        if self.rkap_category_id:
            code = self.rkap_category_id.code
            directorate = ''
            department = self.env.user.employee_id.department_id
            while department:
                if department.department_type == '1_dir':
                    directorate = department.name if department else ''
                    break
                else:
                    department = department.parent_id
            # self.rkap_code = code + '.' + directorate + '.' + self.env.user.name + '.' + self.crossovered_budget_id.period_id.name[2:] + '.' + self.env['ir.sequence'].next_by_code('rkap.code')

    @api.model
    def create(self, vals):
        res = super(CrossOverredBudgetLines, self).create(vals)
        if res.general_budget_id.rkap_code == '' or not res.general_budget_id.rkap_code:
            res.general_budget_id.rkap_code = res.rkap_code
        res._onchange_months_set_rkap_amount()
        res._compute_amount_total()
        res._compute_amount_currency()
        # if res.flag_lintas_tahun_active:
        #     self.create({
        #         'crossovered_budget_id': res.crossovered_budget_id.id,
        #         'general_budget_id': res.general_budget_id.id,
        #         'rkap_code': res.rkap_code,
        #         'category_id': res.category_id.id,
        #         'type_id': res.type_id.id,
        #         'group_id': res.group_id.id,
        #         'date_from': res.lt_date_from,
        #         'date_to': res.lt_date_to,
        #         'month1': res.lt_month1,
        #         'month2': res.lt_month2,
        #         'month3': res.lt_month3,
        #         'month4': res.lt_month4,
        #         'month5': res.lt_month5,
        #         'month6': res.lt_month6,
        #         'month7': res.lt_month7,
        #         'month8': res.lt_month8,
        #         'month9': res.lt_month9,
        #         'month10': res.lt_month10,
        #         'month11': res.lt_month11,
        #         'month12': res.lt_month12,
        #         'planned_amount': res.planned_amount,
        #     })
        return res

    def write(self, vals):
        res = super(CrossOverredBudgetLines, self).write(vals)
        if self.general_budget_id.rkap_code == '' or not self.general_budget_id.rkap_code:
            self.general_budget_id.rkap_code = self.rkap_code
        return res

    @api.constrains('date_from', 'date_to')
    def _line_dates_between_budget_dates(self):
        for line in self:
            budget_date_from = line.crossovered_budget_id.date_from
            budget_date_to = line.crossovered_budget_id.date_to
            # if line.date_from:
            #     date_from = line.date_from
            #     if (budget_date_from and date_from < budget_date_from) or (budget_date_to and date_from > budget_date_to):
            #         raise ValidationError(_('"Start Date" of the budget line should be included in the Period of the budget'))
            # if line.date_to:
            #     date_to = line.date_to
            #     if (budget_date_from and date_to < budget_date_from) or (budget_date_to and date_to > budget_date_to):
            #         raise ValidationError(_('"End Date" of the budget line should be included in the Period of the budget'))

    def _compute_lt_emcumbren_month1(self):
        for record in self:
            po_lines = record.purchase_request_ids.purchase_lines.filtered(lambda r: r.date_order.month == 1 and r.state == 'purchase')
            lt_emcumbren_month= 0
            for po_line in po_lines:
                quantity_balance = po_line.product_qty - po_line.qty_received
                price_subtotal = quantity_balance * po_line.price_unit
                lt_emcumbren_month += price_subtotal
            record.lt_emcumbren_month1 = lt_emcumbren_month

    def _compute_lt_emcumbren_month2(self):
        for record in self:
            po_lines = record.purchase_request_ids.purchase_lines.filtered(lambda r: r.date_order.month == 2 and r.state == 'purchase')
            lt_emcumbren_month= 0
            for po_line in po_lines:
                quantity_balance = po_line.product_qty - po_line.qty_received
                price_subtotal = quantity_balance * po_line.price_unit
                lt_emcumbren_month += price_subtotal
            record.lt_emcumbren_month2 = lt_emcumbren_month

    def _compute_lt_emcumbren_month3(self):
        for record in self:
            po_lines = record.purchase_request_ids.purchase_lines.filtered(lambda r: r.date_order.month == 3 and r.state == 'purchase')
            lt_emcumbren_month= 0
            for po_line in po_lines:
                quantity_balance = po_line.product_qty - po_line.qty_received
                price_subtotal = quantity_balance * po_line.price_unit
                lt_emcumbren_month += price_subtotal
            record.lt_emcumbren_month3 = lt_emcumbren_month

    def _compute_lt_emcumbren_month4(self):
        for record in self:
            po_lines = record.purchase_request_ids.purchase_lines.filtered(lambda r: r.date_order.month == 4 and r.state == 'purchase')
            lt_emcumbren_month= 0
            for po_line in po_lines:
                quantity_balance = po_line.product_qty - po_line.qty_received
                price_subtotal = quantity_balance * po_line.price_unit
                lt_emcumbren_month += price_subtotal
            record.lt_emcumbren_month4 = lt_emcumbren_month

    def _compute_lt_emcumbren_month5(self):
        for record in self:
            po_lines = record.purchase_request_ids.purchase_lines.filtered(lambda r: r.date_order.month == 5 and r.state == 'purchase')
            lt_emcumbren_month= 0
            for po_line in po_lines:
                quantity_balance = po_line.product_qty - po_line.qty_received
                price_subtotal = quantity_balance * po_line.price_unit
                lt_emcumbren_month += price_subtotal
            record.lt_emcumbren_month5 = lt_emcumbren_month

    def _compute_lt_emcumbren_month6(self):
        for record in self:
            po_lines = record.purchase_request_ids.purchase_lines.filtered(lambda r: r.date_order.month == 6 and r.state == 'purchase')
            lt_emcumbren_month= 0
            for po_line in po_lines:
                quantity_balance = po_line.product_qty - po_line.qty_received
                price_subtotal = quantity_balance * po_line.price_unit
                lt_emcumbren_month += price_subtotal
            record.lt_emcumbren_month6 = lt_emcumbren_month

    def _compute_lt_emcumbren_month7(self):
        for record in self:
            po_lines = record.purchase_request_ids.purchase_lines.filtered(lambda r: r.date_order.month == 7 and r.state == 'purchase')
            lt_emcumbren_month= 0
            for po_line in po_lines:
                quantity_balance = po_line.product_qty - po_line.qty_received
                price_subtotal = quantity_balance * po_line.price_unit
                lt_emcumbren_month += price_subtotal
            record.lt_emcumbren_month7 = lt_emcumbren_month

    def _compute_lt_emcumbren_month8(self):
        for record in self:
            po_lines = record.purchase_request_ids.purchase_lines.filtered(lambda r: r.date_order.month == 8 and r.state == 'purchase')
            lt_emcumbren_month= 0
            for po_line in po_lines:
                quantity_balance = po_line.product_qty - po_line.qty_received
                price_subtotal = quantity_balance * po_line.price_unit
                lt_emcumbren_month += price_subtotal
            record.lt_emcumbren_month8 = lt_emcumbren_month

    def _compute_lt_emcumbren_month9(self):
        for record in self:
            po_lines = record.purchase_request_ids.purchase_lines.filtered(lambda r: r.date_order.month == 9 and r.state == 'purchase')
            lt_emcumbren_month= 0
            for po_line in po_lines:
                quantity_balance = po_line.product_qty - po_line.qty_received
                price_subtotal = quantity_balance * po_line.price_unit
                lt_emcumbren_month += price_subtotal
            record.lt_emcumbren_month9 = lt_emcumbren_month

    def _compute_lt_emcumbren_month10(self):
        for record in self:
            po_lines = record.purchase_request_ids.purchase_lines.filtered(lambda r: r.date_order.month == 10 and r.state == 'purchase')
            lt_emcumbren_month = 0
            for po_line in po_lines:
                quantity_balance = po_line.product_qty - po_line.qty_received
                price_subtotal = quantity_balance * po_line.price_unit
                lt_emcumbren_month += price_subtotal
            record.lt_emcumbren_month10 = lt_emcumbren_month

    def _compute_lt_emcumbren_month11(self):
        for record in self:
            po_lines = record.purchase_request_ids.purchase_lines.filtered(lambda r: r.date_order.month == 11 and r.state == 'purchase')
            lt_emcumbren_month = 0
            for po_line in po_lines:
                quantity_balance = po_line.product_qty - po_line.qty_received
                price_subtotal = quantity_balance * po_line.price_unit
                lt_emcumbren_month += price_subtotal
            record.lt_emcumbren_month11 = lt_emcumbren_month

    def _compute_lt_emcumbren_month12(self):
        for record in self:
            po_lines = record.purchase_request_ids.purchase_lines.filtered(lambda r: r.date_order.month == 12 and r.state == 'purchase')
            lt_emcumbren_month= 0
            for po_line in po_lines:
                quantity_balance = po_line.product_qty - po_line.qty_received
                price_subtotal = quantity_balance * po_line.price_unit
                lt_emcumbren_month += price_subtotal
            record.lt_emcumbren_month12 = lt_emcumbren_month

    def _compute_lt_actual_month1(self):
        for record in self:
            aml_obj = self.env['account.move.line'].search([
                ('account_id', 'in', record.general_budget_id.account_ids.ids),
                ('move_id.state', '=', 'posted')])
            aml_obj = aml_obj.filtered(lambda r:r.date.month==1)
            practical_amount = sum(aml_obj.mapped('credit')) - sum(aml_obj.mapped('debit'))
            practical_amount = abs(practical_amount)
            record.lt_actual_month1 = practical_amount
    
    def _compute_lt_actual_month2(self):
        for record in self:
            aml_obj = self.env['account.move.line'].search([
                ('account_id', 'in', record.general_budget_id.account_ids.ids),
                ('move_id.state', '=', 'posted')])
            aml_obj = aml_obj.filtered(lambda r:r.date.month==2)
            practical_amount = sum(aml_obj.mapped('credit')) - sum(aml_obj.mapped('debit'))
            practical_amount = abs(practical_amount)
            record.lt_actual_month2 = practical_amount
            
    def _compute_lt_actual_month3(self):
        for record in self:
            aml_obj = self.env['account.move.line'].search([
                ('account_id', 'in', record.general_budget_id.account_ids.ids),
                ('move_id.state', '=', 'posted')])
            aml_obj = aml_obj.filtered(lambda r:r.date.month==3)
            practical_amount = sum(aml_obj.mapped('credit')) - sum(aml_obj.mapped('debit'))
            practical_amount = abs(practical_amount)
            record.lt_actual_month3 = practical_amount
    
    def _compute_lt_actual_month4(self):
        for record in self:
            aml_obj = self.env['account.move.line'].search([
                ('account_id', 'in', record.general_budget_id.account_ids.ids),
                ('move_id.state', '=', 'posted')])
            aml_obj = aml_obj.filtered(lambda r:r.date.month==4)
            practical_amount = sum(aml_obj.mapped('credit')) - sum(aml_obj.mapped('debit'))
            practical_amount = abs(practical_amount)
            record.lt_actual_month4 = practical_amount
    
    def _compute_lt_actual_month5(self):
        for record in self:
            aml_obj = self.env['account.move.line'].search([
                ('account_id', 'in', record.general_budget_id.account_ids.ids),
                ('move_id.state', '=', 'posted')])
            aml_obj = aml_obj.filtered(lambda r:r.date.month==5)
            practical_amount = sum(aml_obj.mapped('credit')) - sum(aml_obj.mapped('debit'))
            practical_amount = abs(practical_amount)
            record.lt_actual_month5 = practical_amount
    
    def _compute_lt_actual_month6(self):
        for record in self:
            aml_obj = self.env['account.move.line'].search([
                ('account_id', 'in', record.general_budget_id.account_ids.ids),
                ('move_id.state', '=', 'posted')])
            aml_obj = aml_obj.filtered(lambda r:r.date.month==6)
            practical_amount = sum(aml_obj.mapped('credit')) - sum(aml_obj.mapped('debit'))
            practical_amount = abs(practical_amount)
            record.lt_actual_month6 = practical_amount
            
    def _compute_lt_actual_month7(self):
        for record in self:
            aml_obj = self.env['account.move.line'].search([
                ('account_id', 'in', record.general_budget_id.account_ids.ids),
                ('move_id.state', '=', 'posted')])
            aml_obj = aml_obj.filtered(lambda r:r.date.month==7)
            practical_amount = sum(aml_obj.mapped('credit')) - sum(aml_obj.mapped('debit'))
            practical_amount = abs(practical_amount)
            record.lt_actual_month7 = practical_amount
    
    def _compute_lt_actual_month8(self):
        for record in self:
            aml_obj = self.env['account.move.line'].search([
                ('account_id', 'in', record.general_budget_id.account_ids.ids),
                ('move_id.state', '=', 'posted')])
            aml_obj = aml_obj.filtered(lambda r:r.date.month==8)
            practical_amount = sum(aml_obj.mapped('credit')) - sum(aml_obj.mapped('debit'))
            practical_amount = abs(practical_amount)
            record.lt_actual_month8 = practical_amount
    
    def _compute_lt_actual_month9(self):
        for record in self:
            aml_obj = self.env['account.move.line'].search([
                ('account_id', 'in', record.general_budget_id.account_ids.ids),
                ('move_id.state', '=', 'posted')])
            aml_obj = aml_obj.filtered(lambda r:r.date.month==9)
            practical_amount = sum(aml_obj.mapped('credit')) - sum(aml_obj.mapped('debit'))
            practical_amount = abs(practical_amount)
            record.lt_actual_month9 = practical_amount
    
    def _compute_lt_actual_month10(self):
        for record in self:
            aml_obj = self.env['account.move.line'].search([
                ('account_id', 'in', record.general_budget_id.account_ids.ids),
                ('move_id.state', '=', 'posted')])
            aml_obj = aml_obj.filtered(lambda r:r.date.month==10)
            practical_amount = sum(aml_obj.mapped('credit')) - sum(aml_obj.mapped('debit'))
            practical_amount = abs(practical_amount)
            record.lt_actual_month10 = practical_amount
            
    def _compute_lt_actual_month11(self):
        for record in self:
            aml_obj = self.env['account.move.line'].search([
                ('account_id', 'in', record.general_budget_id.account_ids.ids),
                ('move_id.state', '=', 'posted')])
            aml_obj = aml_obj.filtered(lambda r:r.date.month==11)
            practical_amount = sum(aml_obj.mapped('credit')) - sum(aml_obj.mapped('debit'))
            practical_amount = abs(practical_amount)
            record.lt_actual_month11 = practical_amount
    
    def _compute_lt_actual_month12(self):
        for record in self:
            aml_obj = self.env['account.move.line'].search([
                ('account_id', 'in', record.general_budget_id.account_ids.ids),
                ('move_id.state', '=', 'posted')])
            aml_obj = aml_obj.filtered(lambda r:r.date.month==12)
            practical_amount = sum(aml_obj.mapped('credit')) - sum(aml_obj.mapped('debit'))
            practical_amount = abs(practical_amount)
            record.lt_actual_month12 = practical_amount

    def _compute_total_reserve_remaining_lt(self):
        for record in self:
            pr_lines = record.purchase_request_ids.filtered(
                lambda pr: pr.request_state in ['to_approve', 'approved', 'done'])
            pr_reserved_amount = 0.0
            for prs_line in pr_lines:
                if not prs_line.purchase_lines.ids:
                    pr_reserved_amount += prs_line.estimated_cost
                else:
                    for po_line in prs_line.purchase_lines:
                        if po_line.order_id.state in ['draft', 'cancel']:
                            estimated_amount = po_line.product_qty * po_line.price_unit
                            pr_reserved_amount += estimated_amount

            po_lines = record.purchase_request_ids.filtered(
                lambda pr: pr.request_state in ['approved', 'done']
            )
            po_reserved_amount = 0.0
            for pr_line in po_lines:
                for po_line in pr_line.purchase_lines:
                    price_subtotal = 0.0
                    if po_line.order_id.state == 'to approve':
                        price_subtotal = po_line.product_qty * po_line.price_unit
                    elif po_line.order_id.state == 'purchase':
                        quantity_balance = po_line.product_qty - po_line.qty_received
                        price_subtotal = quantity_balance * po_line.price_unit

                    po_reserved_amount += price_subtotal

            residual_amount = record.lt_planned_amount - pr_reserved_amount - po_reserved_amount + record.lt_practical_amount
            record.lt_pr_reserve_amount = pr_reserved_amount
            record.lt_po_reserve_amount = po_reserved_amount
            _logger.info(f"{record.name} - LT PR Reserve Amount: {record.lt_pr_reserve_amount}, LT PO Reserve Amount: {record.lt_po_reserve_amount}, LT Planned Amount: {record.lt_planned_amount}, LT Practical Amount: {record.lt_practical_amount}")
            record.lt_remaining_amount = residual_amount
    
    def _compute_po_reserved(self):
        for record in self:
            domain = []
            if record.general_budget_id:
                domain.extend([
                    ('rkap_id', '=', record.general_budget_id.id),
                    ('new_state', '!=', 'reject')
                ])
            record.po_reserved = sum(record.env['purchase.order'].search(domain).mapped('amount_total'))
    
    def _compute_vendor_bill_amount(self):
        for record in self:
            domain = [
                ('budgetary_position_id', '=', record.id), ('validation_state', '=', 'approve')
            ]
            record.vendor_bill_amount = sum(record.env['account.move'].search(domain).mapped('amount_total'))
    
    def _compute_payment_amount(self):
        for record in self:
            domain = [
                ('move_id.budgetary_position_id', '=', record.id),
                ('state', '=', 'approved')
            ]
            record.payment_amount = sum(record.env['account.payment.plan.line'].search(domain).mapped('amount_residual'))

    @api.onchange('rkap_amount_currency', 'lt_rkap_amount', 'manual_currency_rate_active', 'manual_currency_rate')
    def _onchange_months_set_planned_amount(self):
        self.planned_amount_currency = self.rkap_amount_currency

    @api.onchange('lt_rkap_amount')
    def _onchange_months_set_planned_amount_lt(self):
        self.lt_planned_amount = self.lt_rkap_amount

    @api.onchange(
        'month1', 'month2', 'month3', 'month4', 'month5', 'month6',
        'month7', 'month8', 'month9', 'month10', 'month11', 'month12', 'manual_currency_rate_active', 'manual_currency_rate',
        'lt_month1', 'lt_month2', 'lt_month3', 'lt_month4', 'lt_month5', 'lt_month6',
        'lt_month7', 'lt_month8', 'lt_month9', 'lt_month10', 'lt_month11', 'lt_month12', 'lt_rkap_amount',
        'flag_lintas_tahun_active'
    )
    def _onchange_months_set_rkap_amount(self):
        if not self.flag_lintas_tahun_active:
            self.rkap_amount_currency = self.month1 + self.month2 + self.month3 + self.month4 + \
                                         self.month5 + self.month6 + self.month7 + self.month8 + \
                                         self.month9 + self.month10 + self.month11 + self.month12
        else:
            self.rkap_amount_currency = self.month1 + self.month2 + self.month3 + self.month4 + \
                                        self.month5 + self.month6 + self.month7 + self.month8 + \
                                        self.month9 + self.month10 + self.month11 + self.month12 + \
                                        self.lt_month1 + self.lt_month2 + self.lt_month3 + self.lt_month4 + \
                                        self.lt_month5 + self.lt_month6 + self.lt_month7 + self.lt_month8 + \
                                        self.lt_month9 + self.lt_month10 + self.lt_month11 + self.lt_month12
        self.planned_amount_currency = self.rkap_amount_currency

    @api.onchange(
        'lt_month1', 'lt_month2', 'lt_month3', 'lt_month4', 'lt_month5', 'lt_month6',
        'lt_month7', 'lt_month8', 'lt_month9', 'lt_month10', 'lt_month11', 'lt_month12', 'lt_rkap_amount', 'manual_currency_rate_active', 'manual_currency_rate'
    )
    def _onchange_months_set_rkap_amount_lt(self):
        if self.flag_lintas_tahun_active:
            self.lt_rkap_amount = self.lt_month1 + self.lt_month2 + self.lt_month3 + self.lt_month4 + \
                                         self.lt_month5 + self.lt_month6 + self.lt_month7 + self.lt_month8 + \
                                         self.lt_month9 + self.lt_month10 + self.lt_month11 + self.lt_month12
        else:
            self.lt_rkap_amount = 0
        self.planned_amount_currency = self.rkap_amount_currency

    @api.onchange('rkap_amount_currency', 'lt_rkap_amount')
    @api.depends('rkap_amount_currency', 'lt_rkap_amount')
    def _compute_amount_total(self):
        for record in self:
            if not record.manual_currency_rate_active:
                record.amount_total_budget = record.rkap_amount_currency + record.lt_rkap_amount
            else:
                record.amount_total_budget = (record.rkap_amount_currency + record.lt_rkap_amount) * record.manual_currency_rate


    def action_open_pr_res_entries(self):
        self.ensure_one()
        
        domain = []
        
        if self.general_budget_id:
            domain.append(('justification_id.budgetary_position_id.general_budget_id', '=', self.general_budget_id.id))
        
        
        return {
            "name": _("Purchase Request"),
            "type": "ir.actions.act_window",
            "res_model": "purchase.request",
            'view_mode': 'list,form',
            'target': 'current',
            'domain': domain, 
            'context': {
                'default_budgetary_position_id': self.general_budget_id.id if self.general_budget_id else False,
                'search_default_filter_current': True,
            }
        }
        
    def action_open_po_res_entries(self):
        self.ensure_one()
        
        domain = []
        
        if self.general_budget_id:
            domain.append(('rkap_id', '=', self.general_budget_id.id))
        
        return {
            'name': _('Purchase Orders'),
            'type': 'ir.actions.act_window',
            'res_model': 'purchase.order',
            'view_mode': 'list,form',
            'target': 'current',
            'domain': domain, 
            'context': {
                'default_budgetary_position_id': self.general_budget_id.id if self.general_budget_id else False,
                'search_default_filter_current': True,
            }
        }
    
    def action_open_bill_res_entries(self):
        self.ensure_one()
        
        domain = [
                ('budgetary_position_id', '=', self.id)
            ]
        
        return {
            'name': _('Vendor Bills'),
            'type': 'ir.actions.act_window',
            'res_model': 'account.move',
            'view_mode': 'list,form',
            'target': 'current',
            'domain': domain, 
            'context': {
                'default_budgetary_position_id': self.id if self.id else False,
                'search_default_filter_current': True,
            }
        }

    def action_open_payment_res_entries(self):
        self.ensure_one()
        
        domain = [
                ('move_id.budgetary_position_id', '=', self.id),
                ('state', '=', 'approved')
            ]
        
        return {
            'name': _('Payment Line Filtered'),
            'type': 'ir.actions.act_window',
            'res_model': 'account.payment.plan.line',
            'view_mode': 'list',
            'target': 'current',
            'domain': [
                ('move_id.budgetary_position_id', '=', self.id),
                ('state', '=', 'approved')
            ],
            'context': {
                'search_default_filter_current': True,
            }
        }

        
    def action_open_Justif_res_entries(self):
        self.ensure_one()
        
        domain = []

        if self.general_budget_id:
            domain.extend([
                ('budgetary_position_id.general_budget_id', '=', self.general_budget_id.id),
                # ('amount_currency', '=', self.justifikasi_reserve_amount),
            ])

        return {
            'name': _('Justification Entries'),
            'type': 'ir.actions.act_window',
            'res_model': 'account.justification',
            'view_mode': 'list,form',
            'target': 'current',
            'domain': domain, 
            'context': {
                'default_budgetary_position_id': self.general_budget_id.id if self.general_budget_id else False,
                'search_default_filter_current': True,
            }
        }
        
    def action_open_FPJP_res_entries(self):
        self.ensure_one()
        
        domain = []
        
        if self.general_budget_id:
            domain.append(('justification_id.budgetary_position_id.general_budget_id', '=', self.general_budget_id.id))
        

        return {
            'name': _('Budget Analytic Entries'),
            'type': 'ir.actions.act_window',
            'res_model': 'account.fpjp',
            'view_mode': 'list,form',
            'target': 'current',
            'domain': domain,
            'context': {
                'default_budgetary_position_id': self.general_budget_id.id if self.general_budget_id else False,
                'search_default_filter_current': True,
            }
        }
        
        
    def action_open_relocation_in(self):
        self.ensure_one()
        
        domain = []
        
        if self.general_budget_id:
            relocations = self.env['account.relocation'].search([
                ('rkap_recipient_id', '=', self.general_budget_id.id)
            ])
            domain.append(('id', 'in', relocations.ids))
        
        return {
            'name': _('Budget Relocation'),
            'type': 'ir.actions.act_window',
            'res_model': 'account.relocation',
            'view_mode': 'list,form',
            'target': 'current',
            'domain': domain,
            'context': {
                'default_budgetary_position_id': self.general_budget_id.id if self.general_budget_id else False,
                'search_default_filter_current': True,
            }
        }
        
    def action_open_relocation_out(self):
        self.ensure_one()
        
        domain = []
        
        if self.general_budget_id:
            relocations = self.env['account.relocation.line'].search([
                ('general_budget_id', '=', self.general_budget_id.id)
            ])
            relocation_ids = relocations.mapped('justification_id').ids
            domain.append(('id', 'in', relocation_ids))
        
        return {
            'name': _('Budget Relocation'),
            'type': 'ir.actions.act_window',
            'res_model': 'account.relocation',
            'view_mode': 'list,form',
            'target': 'current',
            'domain': domain, 
            'context': {
                'default_budgetary_position_id': self.general_budget_id.id if self.general_budget_id else False,
                'search_default_filter_current': True,
            }
        }
            

        