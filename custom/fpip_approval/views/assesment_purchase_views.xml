<odoo>
    <data>
        <record id="assesment_purchase_loa_form" model="ir.ui.view">
            <field name="name">assesment.purchase.loa.form</field>
            <field name="model">assesment.purchase</field>
            <field name="inherit_id" ref="purchase_fpip.assesment_purchase_view_form"/>
            <field name="arch" type="xml">
                <!-- add button to do action -->
                <xpath expr="//header" position="inside">
                    <button name="button_action" type="object" string="Approve" class="btn btn-success" 
                        invisible="state != 'waiting' or not is_current_approver"
                        context="{'approve': True}" />
                    <button name="button_action" type="object" string="Reject" class="btn-danger"
                        invisible="state != 'waiting' or not is_current_approver"
                        context="{'reject': True}" />
                    <button name="button_action" type="object" string="Reassign" class="btn btn-success" 
                        invisible="state != 'waiting' or (not is_current_approver and not is_admin)"
                        context="{'reassign': True}" />
                </xpath>

                <xpath expr="//field[@name='employee_id']" position="after">
                    <field name="requestor_id" required="1" />
                </xpath>

                <!-- add approval_history_ids in new page inside notebook -->
                <xpath expr="//notebook" position="inside">
                    <page name="history" string="Approvals">
                        <field name="is_current_approver" invisible="1"/>
                        <field name="selected_approver_ids" widget="many2many_tags" invisible="1"/>
                        <field name="approval_history_ids" readonly="1" force_save='1' />
                    </page>
                    <page string="Approval Details" name="history_detail">
                        <field name="approval_history_detail_ids" >
                            <list editable="bottom" create="0" edit="0" delete="0">
                                <field name="level" readonly="1" force_save='1' />
                                <field name="employee_id" readonly="1" force_save='1' />
                                <field name="employee_state" readonly="1" force_save='1' />
                                <field name="employee_date" readonly="1" force_save='1' />
                                <field name="employee_note" readonly="1" force_save='1' />
                                <field name="reassign_employee_id" readonly="1" force_save='1' />
                                <field name="reassign_employee_state" readonly="1" force_save='1' />
                                <field name="reassign_employee_date" readonly="1" force_save='1' />
                                <field name="reassign_employee_note" readonly="1" force_save='1' />
                            </list>
                        </field>
                    </page>
                    <page name="approval_message" string="Message Details">
                        <field name="approval_message_ids" readonly="1" force_save='1' />
                    </page>
                </xpath>

                <!-- put hierarchy_id and current_user_id after currency_id -->
                <xpath expr="//field[@name='user_id']" position="after">
                    <field name="hierarchy_id" readonly="1" force_save='1' />
                </xpath>

                <!-- <xpath expr="//field[@name='requestor_id']" position="attributes">
					<attribute name="required">True</attribute>
				</xpath> -->

            </field>
        </record>

        <!-- <record id="assesment_purchase_view_search" model="ir.ui.view">
            <field name="name">assesment.purchase.search</field>
            <field name="model">assesment.purchase</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <search>
                    <separator/>
                    <filter name="my_approvals" string="My Approvals" domain="[('selected_approver_ids', 'in', [current_employee_id])]"/>
                    <separator/>
                </search>
            </field>
        </record> -->

        <record id="purchase_fpip.assesment_purchase_action" model="ir.actions.act_window">
            <field name="context">{'search_default_my_approvals': 1}</field>
            <field name="path">assesment_purchase</field>
        </record>

        <record id="assesment_purchase_view_search" model="ir.ui.view">
            <field name="name">assesment.purchase.view.search</field>
            <field name="model">assesment.purchase</field>
            <field name="arch" type="xml">
                <search string="Assesment Purchase">
                    <filter name="my_approvals" string="My Approvals" domain="[('selected_approver_ids.user_id', '=', uid)]" />
                </search>
            </field>
        </record>
    </data>
</odoo>
