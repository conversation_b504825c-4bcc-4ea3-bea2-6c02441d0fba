from odoo import api, fields, models


class AssesmentPurchaseApproval(models.Model):
    _name = 'assesment.purchase.approval'
    _inherit = 'approval.history.mixin'
    _description = 'Assesment Purchase Approval'

    assesment_purchase_id = fields.Many2one('assesment.purchase', 'Assesment Purchase',
                               ondelete='cascade')
    reassign_employee_ids = fields.Many2many(
        'hr.employee',
        'employee_reassign_hierarchy_assesment_purchase_rel',
        'assesment_purchase_reassign_approval_id',
        'employee_id',
        string='Reassign Employees'
    )


    employee_ids = fields.Many2many(
        'hr.employee',
        'employee_approval_hierarchy_assesment_purchase_rel',  # tetap
        'assesment_purchase_approval_id',
        'employee_id',
        string='Employees'
    )

    approval_employee_ids = fields.Many2many(
        'hr.employee',
        'approval_employee_hierarchy_assesment_purchase_rel',
        'assesment_purchase_approval_approval_id',
        'approval_employee_id',
        string='Approval Employees'
    )
    
class AssesmentPurchaseApprovalDetail(models.Model):
    _name = 'assesment.purchase.approval.detail'
    _inherit = 'approval.history.detail.mixin'
    _description = 'Assesment Purchase Approval'

    assesment_purchase_id = fields.Many2one('assesment.purchase', 'Assesment Purchase',
                               ondelete='cascade')

class AssesmentPurchaseReassign(models.Model):
    _name = 'assesment.purchase.reassign'
    _description = 'Assesment Purchase Reassign'
    

    assesment_purchase_id = fields.Many2one('assesment.purchase', 'Assesment Purchase',
                               ondelete='cascade')
    from_employee_id = fields.Many2one('hr.employee', string='From Employee')
    to_employee_id = fields.Many2one('hr.employee', string='To Employee') 
