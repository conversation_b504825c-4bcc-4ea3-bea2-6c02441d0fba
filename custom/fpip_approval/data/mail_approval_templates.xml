<odoo>
    <data>
        <record id="mail_assesment_purchase_approval" model="mail.template">
            <field name="name">FPIP: Request Approval</field>
            <field name="model_id" ref="purchase_fpip.model_assesment_purchase"/>
            <field name="subject">FPIP (Ref {{ object.name or 'n/a' }})</field>
            <field name="email_to">{{ ctx['mail_to'] }}</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            FPIP Approval:<br/><br/>
                            <table width="100%">
                                <tr>
                                    <td>Number</td>
                                    <td>: <t t-out="object.name or ''">-</t></td>
                                </tr>
                            </table>
                        </div>
                    </p>
                    <p style="margin: 5px 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            <p>Yth. Bapak/Ibu,

                                Mohon approval untuk FPIP partner <t t-out="object.partner_id.name" /> terkait PO <t t-out="object.purchase_id.name" />.
                            </p>
                            <!-- <table width="100%">
                                <tr>
                                    <td>Parameter</td>
                                    <td>Penjelasan</td>
                                </tr>

                                <t t-foreach="object.assesment_detail_line_ids" t-as="line">
                                    <tr>
                                        <td><t t-out="line.parameter"/></td>
                                        <td><t t-out="line.penjelasan"/></td>
                                    </tr>
                                </t>
                            </table> -->
                        </div>
                    </p>
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <a t-att-href="ctx['link']" >Silakan klik tautan berikut untuk melihat detail penilaian:</a> 
                    </p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>

        <record id="mail_assesment_purchase_info" model="mail.template">
            <field name="name">FPIP: Info</field>
            <field name="model_id" ref="purchase_fpip.model_assesment_purchase"/>
            <field name="subject">FPIP (Ref {{object.name or 'n/a' }})</field>
            <field name="email_to">{{ ctx['mail_to'] }}</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            <t t-out="ctx['message']" />
                        </div>
                    </p>
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <a t-att-href="ctx['link']" >Click here to see details</a> 
                    </p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>
    </data>
</odoo>
