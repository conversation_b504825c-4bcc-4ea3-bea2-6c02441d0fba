<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

         <!-- Formview Bidding-->
        <record id="ap_bidding_view_form" model="ir.ui.view">
            <field name="name">Bidding</field>
            <field name="model">bidding.bidding</field>
            <field name="arch" type="xml">
                <form string="Bidding">
                    <header>
                        <button name="action_confirm" string="Confirm" type="object" class="oe_highlight"
                                invisible="state != 'draft'"/>
                        <button name="action_start_bidding" string="Start Bidding" type="object" class="oe_highlight"
                                invisible="state not in ('confirm','close_bidding')"/>
                        <button name="action_close_bidding" string="Close Bidding" type="object" class="oe_highlight"
                                invisible="state != 'start_bidding'"/>
                        <!-- <button name="action_bidding_send" invisible="state != 'draft'" string="Send by Email" type="object" context="{'send_bidding':True}" class="oe_highlight" data-hotkey="g"/> -->
                        <button name="action_draft" string="Set to Draft" type="object" invisible="state in ('draft')"/>
                        <button name="action_cancel" string="Cancel" type="object" invisible="state in ('draft','cancel','close_bidding')"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,confirm,start_bidding,close_bidding,cancel"/>
                    </header>
                    <sheet>

                        <div class="oe_title">
                            <h1><field name="name" readonly="1"/></h1>
                        </div>
                        <group>
                            <group>
                                <field name="description" required="1" readonly="state != 'draft'"/>
                                <field name="bidding_type" required="1" readonly="state != 'draft'"/>
                                <field name="is_purchase_request" readonly="state != 'draft'"/>
                                <field name="purchase_request_id" required="is_purchase_request == True" invisible="is_purchase_request == False" readonly="state != 'draft'" domain="[('state', '=', 'approved'),('pr_type', '=', 'manual')]"/>
                                <field name="buyer_id" readonly="1" required="1"/>
                                <field name="is_add_product_negotiation" readonly="state != 'draft'" />
                            </group>
                            <group>
                                <field name="company_id" invisible="0" readonly="state != 'draft'"/>
                                <field name="transaction_date" required="1" readonly="state != 'draft'"/>
                                <field name="start_bidding_date" required="1" readonly="state != 'draft'"/>
                                <field name="deadline_date" required="1" readonly="state != 'draft'"/>
                                <field name="requestor_id" required="1" readonly="state != 'draft'"/>
                                <field name="unit_id" readonly="1" force_save="1"/>
                                <field name="group_id" readonly="1" force_save="1"/> 
                            </group>
                        </group>
                        <notebook>
                        <page string="Products">
                            <field name="line_ids" readonly="state != 'draft'">
                                <list editable="bottom">
                                    <field name="product_tmpl_id" required="1" context="{'search_default_filter_to_purchase': 1}"/>
                                    <field name="product_variant_id" required="0" domain="[('product_tmpl_id', '=', product_tmpl_id), ('is_product_variant', '=', True)]"/>
                                    <field name="description" required="1"/>
                                    <field name="quantity" required="1"/>
                                    <field name="product_uom_id" required="1"/>
                                    <field name="is_percentage" force_save="1"/>
                                    <field name="uom_type" required="0" column_invisible="1"/>
                                    <field name="currency_id" invisible="is_percentage == True" required="1"/>
                                    <field name="company_currency_id" readonly="1" column_invisible="1"/>
                                    <field name="company_id" readonly="1" column_invisible="1"/>
                                    <field name="unit_price" invisible="is_percentage == True" width="150px"/>
                                    <field name="total" readonly="1" invisible="is_percentage == True"/>
                                </list>
                                <form string="Products">
                                    <field name="product_tmpl_id" required="1"/>
                                    <field name="product_variant_id" required="0" domain="[('product_tmpl_id', '=', product_tmpl_id), ('is_product_variant', '=', True)]" />
                                    <field name="description" required="1"/>
                                    <group>
                                        <group>
                                            <field name="quantity" required="1"/>
                                            <field name="product_uom_id" required="1"/>
                                            <field name="uom_type" required="0" invisible="1"/>
                                            <field name="currency_id"/>
                                            <field name="company_currency_id" readonly="1" invisible="1"/>
                                            <field name="company_id" readonly="1" invisible="1"/>
                                            <field name="unit_price" required="1"/>
                                        </group>
                                        <group>
                                            <field name="total" readonly="1"/>
                                        </group>
                                    </group>
                                </form>
                            </field>
                             <group>
                                <field name="notes" placeholder="Notes..." nolabel="1"/>
                            </group>

                        </page>
                        <page string="Vendors">
                            <div style="float: right;">
                                <button name="button_submit_reassign_pic" string="Submit Re-Assign" type="object" class="oe_highlight"/>
                            </div>
                            <field name="bidding_vendor_ids" readonly="state != 'draft'">
                                <list editable="bottom">
                                    <field name="vendor_id" required="1" domain="[('supplier_rank', '!=', 0),('is_company', '=', True),('vendor_state','in',['prospective','spendauthorized'])]"/>
                                    <field name="pic_id" required="1" domain="[('parent_id', '=', vendor_id),('is_company','!=',True)]" force_save="1"/>
                                    <field name="pic_email" readonly="1" force_save="1"/>
                                    <field name="cc_email"/>
                                    <field name="reassign_pic_id" readonly="1" domain="[('parent_id', '=', vendor_id)]"/>
                                    <field name="reassign_pic_email" readonly="1"/>
                                    <field name="company_id" readonly="1" column_invisible="1"/>
                                    <!-- <field name="email_notification" required="0"/> -->
                                    <button name="action_send_email" string="Email Notification" type="object" class="oe_highlight"/>
                                </list>
                                <form string="Vendors">
                                    <field name="vendor_id" required="1" domain="[('supplier_rank', '!=', 0),('parent_id', '=', False)]"/>
                                    <group>
                                        <group>
                                           <field name="pic_id" readonly="1" domain="[('parent_id', '=', vendor_id)]" force_save="1"/>
                                           <field name="pic_email" readonly="1" force_save="1"/>
                                           <field name="cc_email"/>
                                           <field name="reassign_pic_id" readonly="1" domain="[('parent_id', '=', vendor_id)]"/>
                                           <field name="reassign_pic_email" readonly="1"/>
                                        </group>
                                        <group>
                                            <field name="company_id" readonly="1" invisible="1"/>
                                            <!-- <field name="email_notification" required="0"/> -->
                                            <button name="action_send_email" string="Email Notification" type="object" class="oe_highlight"/>
                                        </group>
                                    </group>
                                </form>
                            </field>
                        </page>
                        <page string="Attachments">
                            <field name="bidding_attachment_ids" readonly="state != 'draft'">
                                <list editable="bottom">
                                    <field name="attachment" widget="binary" filename="filename"/>
                                    <field name="filename" column_invisible="1"/>
                                    <field name="document_name" required="1"/>
                                    <field name="document_type" required="1"/>
                                    <field name="company_id" readonly="1" column_invisible="1"/>
                                </list>
                                <form string="Attachments">
                                    <group>
                                        <group>
                                           <field name="attachment" widget="binary" filename="filename"/>
                                           <field name="filename" invisible="1"/>
                                           <field name="document_name" required="1"/>
                                        </group>
                                        <group>
                                            <field name="document_type" required="1"/>
                                            <field name="company_id" readonly="1" invisible="1"/>
                                        </group>
                                    </group>
                                </form>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
                </form>
            </field>
        </record>

         <!-- Listview Bidding-->
        <record id="ap_bidding_view_list" model="ir.ui.view">
            <field name="name">Bidding</field>
            <field name="model">bidding.bidding</field>
            <field name="arch" type="xml">
                <list string="Bidding" decoration-info="state == 'draft'"
                      decoration-muted="state == 'cancel'">
                    <field name="name"/>
                    <field name="description"/>
                    <field name="bidding_type"/>
                    <field name="buyer_id"/>
                    <field name="transaction_date"/>
                    <field name="start_bidding_date"/>
                    <field name="deadline_date"/>
                    <field name="requestor_id"/>
                    <field name="unit_id"/>
                    <field name="group_id"/> 
                    <field name="state"/>
                </list>
            </field>
        </record>

         <!-- Search Bidding-->
        <record id="ap_bidding_view_search" model="ir.ui.view">
            <field name="name">bidding.bidding.search</field>
            <field name="model">bidding.bidding</field>
            <field name="arch" type="xml">
                <search string="Search Bidding">
                    <separator string="General" />
                    <field name="name"/>
                    <field name="bidding_type"/>
                    <field name="state"/>

                    <separator string="Dates" />
                    <field name="transaction_date"/>
                    <field name="start_bidding_date"/>
                    <field name="deadline_date"/>

                    <separator string="Request Details" />
                    <field name="requestor_id"/>
                    <field name="unit_id"/>
                    <field name="group_id"/>

                    <separator string="Filters" />
                    <filter name="state_draft" string="Draft" domain="[('state', '=', 'draft')]" />
                    <filter name="state_confirm" string="Confirmed" domain="[('state', '=', 'confirm')]" />
                    <filter name="state_start" string="Start Bidding" domain="[('state', '=', 'start_bidding')]" />
                    <filter name="state_close" string="Close Bidding" domain="[('state', '=', 'close_bidding')]" />
                    <filter name="state_cancel" string="Cancelled" domain="[('state', '=', 'cancel')]" />

                    <separator string="Grouping" />
                    <group expand="0" string="Group By">
                        <filter name="group_by_bidding_type" string="Bidding Type" domain="[]" context="{'group_by': 'bidding_type'}"/>
                        <filter name="group_by_requested_by" string="Requested By" domain="[]" context="{'group_by': 'requestor_id'}"/>
                        <filter name="group_by_unit" string="Unit" domain="[]" context="{'group_by': 'unit_id'}"/>
                        <filter name="group_by_group" string="Group" domain="[]" context="{'group_by': 'group_id'}"/>
                        <filter name="group_by_state" string="Status" domain="[]" context="{'group_by': 'state'}"/>
                    </group>
                </search>
            </field>
        </record>


        <!-- Action Bidding-->
    <record id="action_bidding" model="ir.actions.act_window">
        <field name="name">Bidding</field>
        <field name="res_model">bidding.bidding</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="ap_bidding_view_search"/>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'list', 'view_id': ref('ap_bidding_view_list')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('ap_bidding_view_form')})]"/>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
              Click to add a new bidding.
          </p>
        </field>
    </record>


    </data>
</odoo>