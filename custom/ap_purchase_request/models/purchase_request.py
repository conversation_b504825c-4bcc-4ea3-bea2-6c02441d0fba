import logging
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

from odoo import SUPERUSER_ID, api, fields, models, _
from odoo.exceptions import ValidationError, UserError
from odoo.tools.misc import formatLang

_STATES = [
    ("draft", "Draft"),
    ("to_approve", "To be approved"),
    ("assign_buyer", "Assign Buyer"),
    ("approved", "Approved"),
    ("rejected", "Rejected"),
    ("canceled", "Canceled"),
    ("done", "Done"),
]

class PurchaseRequest(models.Model):
    _inherit = "purchase.request"

    def _get_department_by_type(self, dept_type):
        employee = self.env['hr.employee'].search([('user_id', '=', self.env.user.id)], limit=1)
        department = employee.department_id
        while department:
            if department.department_type == dept_type:
                return department.id
            department = department.parent_id
        return False

    def _get_pic_id(self):
        employee = self.env['hr.employee'].search([('user_id', '=', self.env.user.id)], limit=1)
        employee_id = employee.id
        return employee_id

    def _get_requestor_id(self):
        employee = self.env['hr.employee'].search([('user_id', '=', self.env.user.id)], limit=1)
        employee_id = employee.id
        return employee_id

    @api.onchange('justification_id')
    def onchange_justification(self):

        line_vals = [(5, 0, 0)]  # Clear
        # for line in self.justification_id.line_ids:

        #     if line.procurement_stype_id.type == 'procurement':
        #         line_vals.append((0, 0, {
        #             'justification_line_id': line.id,
        #             'justif_line_fund_available': line.amount_currency,
        #         }))

        # self.line_ids = line_val



    active = fields.Boolean(
        string="Active",
        help="Indicates whether this record is active. If disabled, the record will not appear in lists but will remain in the system.",
        copy=False, default=True, index=True, tracking=True)
    state = fields.Selection(
        selection=_STATES,
        string="Status",
        index=True,
        tracking=True,
        required=True,
        copy=False,
        default="draft",
    )

    pr_type = fields.Selection([
        ('rfq', 'RFQ'),
        ('rfp', 'MPA'),
        ('manual', 'Manual'),
    ], string='PR Type', index=True, copy=False,
       help="Jenis Purchase Request yang digunakan, misalnya RFQ, MPA, atau Manual.")
    agreement_id = fields.Many2one(
        comodel_name="bidding.agreement",
        string="MPA Number",
        index=True, copy=False,
        help="Referensi ke dokumen MPA (Master Purchase Agreement) jika ada.")
    rfq_id = fields.Many2one(
        comodel_name="bidding.rfq",
        string="RFQ Number",
        index=True, copy=False,
        help="Referensi ke dokumen RFQ (Request for Quotation) yang terkait.")
    dpl_id = fields.Many2one(
        comodel_name="purchase.dpl",
        string="DPL",
        default=None, 
        index=True, copy=False,
        help="Referensi ke DPL (Daftar Penyedia Langsung) jika diperlukan.")
    is_dpl = fields.Boolean(string='Is DPL', copy=False,
                            help="Centang jika PR ini termasuk DPL.")
    pr_category_id = fields.Many2one(
        comodel_name="purchase.request.category",
        string="PR Category",
        index=True, copy=False,
        help="Kategori dari PR ini, biasanya sesuai dengan jenis barang atau layanan.")
    pic_id = fields.Many2one(
        comodel_name="hr.employee",
        string="PR PIC",
        default=_get_pic_id,
        copy=False,
        help="User yang bertanggung jawab sebagai PIC (Person in Charge) pada PR ini.")
    requestor_id = fields.Many2one(
        comodel_name="hr.employee",
        string="Requested by",
        default=_get_requestor_id,
        copy=False,
        help="User yang request pada PR ini.")
    directorate_id = fields.Many2one(
        comodel_name="hr.department",
        string="Directorate",
        index=True, copy=False,
        help="Direktorat tempat karyawan pengaju PR berada.",
        context={'hierarchical_naming': False},
        default=lambda self: self._get_department_by_type('1_dir'))
    hr_group_id = fields.Many2one(
        comodel_name="hr.department",
        string="Group",
        index=True, copy=False,
        help="Grup divisi tempat pengaju PR.",
        context={'hierarchical_naming': False},
        default=lambda self: self._get_department_by_type('3_group'))
    unit_id = fields.Many2one(
        comodel_name="hr.department",
        string="Unit",
        index=True, copy=False,
        help="Unit kerja tempat pengaju PR.",
        context={'hierarchical_naming': False},
        default=lambda self: self._get_department_by_type('4_unit'))
    buyer_id = fields.Many2one(
        comodel_name='hr.employee',
        string="Buyer",
        copy=False,
        help="Buyer yang ditugaskan untuk menangani PR ini.")
    assigne_buyer_date = fields.Date(string='Assigne Buyer Date', copy=False,
                                     help="Tanggal buyer ditugaskan untuk PR ini.")

    date_start = fields.Date(string='Start Delivery Date', copy=False,
                                      help="Tanggal mulai pengiriman barang/jasa.",
                                      compute='_compute_date_range', store=True)

    date_end = fields.Date(string='End Delivery Date', copy=False,
                                    help="Tanggal selesai pengiriman barang/jasa.",
                                    compute='_compute_date_range', store=True)
    justification_id = fields.Many2one(
        comodel_name="account.justification",
        string="Justifikasi Number",
        index=True, copy=False,
        help="Referensi nomor justifikasi yang digunakan sebagai dasar PR.")
    rkap_id = fields.Many2one(
        comodel_name="account.budget.post",
        string="RKAP",
        index=True, copy=False,
        help="Referensi ke RKAP (Rencana Kerja dan Anggaran Perusahaan).")
    rkap_code = fields.Char(string='Kode RKAP', copy=False,
                            help="Kode yang digunakan pada RKAP untuk kegiatan ini.")
    rkap_type_id = fields.Many2one(
        comodel_name="account.rkap.type",
        string="RKAP Type",
        index=True, copy=False,
        help="Tipe anggaran pada RKAP.")
    rkap_category_id = fields.Many2one(
        comodel_name="account.rkap.category",
        string="RKAP Category",
        index=True, copy=False,
        help="Kategori anggaran pada RKAP.")
    accrue_expense = fields.Boolean(string='Accrue Expense', copy=False,
                                    help="Centang jika biaya akan diakui secara akrual.")
    project_id = fields.Many2one(comodel_name='phase.project.cip', string='Project', copy=False,
                          help="Nama atau kode proyek yang terkait dengan PR ini.")
    attachment_ids = fields.Many2many(
        comodel_name='ir.attachment',
        string="Attachments",
        copy=False,
        help="Lampiran dokumen tambahan untuk PR ini.")
    total_manajemen_fee = fields.Monetary(
        string="Total Management Fee",
        currency_field='currency_id',
        compute="_compute_total_manajemen_fee",
        store=True,
        help="Jumlah total biaya manajemen dari baris yang ditandai sebagai manajemen fee.",
        copy=False)
    employee_id = fields.Many2one('hr.employee', string='Submitter Name', default=lambda self: self.env.user.employee_id)  
    is_project_readonly = fields.Boolean(string="Project Readonly", compute="_compute_project_readonly", store=True)

    def button_rejected(self):
        res = super().button_rejected()
        for record in self:
            if record.justification_id:
                justif = record.env['account.justification'].search([('id', '=', record.justification_id.id)], limit=1)
                justif.write({'amount_justif': justif.remaining_amount})
        return res
        
    product_agreement_ids = fields.Many2many('product.product', string='Product Agreement', compute="_compute_product_agreement_ids")
    product_tmpl_agreement_ids = fields.Many2many('product.template', string='Product Agreement', compute="_compute_product_agreement_ids")

    @api.onchange('line_ids')
    def _onchange_line_ids(self):
        for line_id in self.line_ids:        
            if line_id.estimated_cost > 0 and line_id.justification_line_id:
                line_id.justification_line_id.remaining_amount =  line_id.justif_line_fund_available - line_id.estimated_cost

    @api.depends('agreement_id')
    def _compute_product_agreement_ids(self):
        for rec in self :
            rec.product_agreement_ids = rec.agreement_id.line_ids.filtered(lambda x: x.is_active).mapped('product_variant_id').ids
            rec.product_tmpl_agreement_ids = rec.agreement_id.line_ids.filtered(lambda x: x.is_active).mapped('product_tmpl_id').ids

    @api.depends('line_ids', 'line_ids.product_tmpl_id.is_cip', 'line_ids.product_id.is_cip')
    def _compute_project_readonly(self):
        for rec in self:
            lines = rec.line_ids.filtered(lambda x: x.product_tmpl_id.is_cip or x.product_id.is_cip)
            if lines:
                rec.is_project_readonly = True
            else:
                rec.is_project_readonly = False


    @api.depends("line_ids", "line_ids.is_manajemen_fee", "line_ids.estimated_cost")
    def _compute_total_manajemen_fee(self):
        for record in self:
            record.total_manajemen_fee = sum(
                line.estimated_cost for line in record.line_ids if line.is_manajemen_fee
            )
            for line in record.line_ids:
                if line.is_percentage:
                    line._onchange_total_percentage()

    @api.onchange('is_dpl')
    def _onchange_is_dpl(self):
        if not self.is_dpl:
            self.dpl_id = False

    @api.onchange('dpl_id','justification_id')
    def _onchange_dpl_id(self):
        if not self.dpl_id:
            self.is_dpl = False
        else:
            self.is_dpl = True
            
    @api.onchange('justification_id')
    def onchange_justification_dpl(self):
        self.is_dpl = self.justification_id.is_dpl
        if self.justification_id.purchase_dpl_ids:
            dpl_sorted = self.justification_id.purchase_dpl_ids.sorted(key=lambda d: d.create_date)
            self.dpl_id = dpl_sorted[0]
        else:
            self.dpl_id = False

    @api.depends('line_ids.start_date', 'line_ids.end_date')
    def _compute_date_range(self):
        for rec in self:
            start_dates = [d for d in rec.line_ids.mapped('start_date') if d]
            end_dates = [d for d in rec.line_ids.mapped('end_date') if d]
            rec.date_start = min(start_dates) if start_dates else False
            rec.date_end = max(end_dates) if end_dates else False

    @api.onchange('requestor_id')
    def _onchange_requested_by(self):
        self.requested_by = self.requestor_id.user_id.id
        self.unit_id = False
        self.hr_group_id = False
        department = self.requestor_id.department_id
        if department:
            if department.department_type == '4_unit':
                self.unit_id = department.id
                self.hr_group_id = department.parent_id.id if department.parent_id else False
                self.directorate_id = department.parent_id.parent_id.id if department.parent_id.parent_id else False
            elif department.department_type == '3_group':
                self.hr_group_id = department.id
                self.directorate_id = department.parent_id.id if department.parent_id else False
            elif department.department_type == '1_dir':
                self.directorate_id = department.id

    @api.onchange('pr_type')
    def _onchange_pr_type(self):
        if self.pr_type == 'rfq':
            self.agreement_id = False
        elif self.pr_type == 'rfp':
            self.rfq_id = False
        else:
            self.agreement_id = False
            self.rfq_id = False

    @api.onchange('agreement_id')
    def onchange_mpa_data(self):
        self.description = self.agreement_id.bidding_description

        # line_vals = [(5, 0, 0)]
        # for line in self.agreement_id.line_ids.filtered(lambda x: x.is_active): 
        #     latest_rate = self.env['res.currency.rate'].search([
        #         ('currency_id', '=', line.currency_id.id)
        #     ], order='name desc', limit=1) 
            
        #     currency_rate = latest_rate.inverse_company_rate if latest_rate else 1
        #     line_vals.append((0, 0, {
        #         'product_tmpl_id': line.product_tmpl_id.id,
        #         'product_id': line.product_variant_id.id,
        #         'product_category_id': line.product_tmpl_id.categ_id.id,
        #         'product_uom_id': line.product_uom_id.id,
        #         'name': line.description,
        #         'new_currency_id': line.currency_id.id,
        #         'currency_rate': currency_rate,
        #         'price_unit': line.unit_price,
        #         'product_qty': line.qty,
        #         'is_percentage': bool(line.product_uom_id.name == 'Percentage')
        #     }))

        # self.line_ids = line_vals

    def button_approved(self):
        print('pangggillllll')
        if not self.buyer_id:
            raise ValidationError('Required buyer before approve!')
        res = super(PurchaseRequest, self).button_approved()
        return res

    @api.onchange('rfq_id')
    def onchange_rfq_data(self):
        self.description = self.rfq_id.description

        line_vals = [(5, 0, 0)]
        for line in self.rfq_id.order_line.filtered(lambda x: x.is_active):
            latest_rate = self.env['res.currency.rate'].search([
                ('currency_id', '=', line.currency_id.id)
            ], order='name desc', limit=1) 
            
            currency_rate = latest_rate.inverse_company_rate if latest_rate else 1
            line_vals.append((0, 0, {
                'product_tmpl_id': line.product_tmpl_id.id,
                'product_id': line.product_variant_id.id,
                'product_category_id': line.product_tmpl_id.categ_id.id,
                'product_uom_id': line.product_uom_id.id,
                'name': line.description,
                'new_currency_id': line.currency_id.id,
                'currency_rate': currency_rate,
                'price_unit': line.price_unit,
                'product_qty': line.product_qty,
                'is_percentage': bool(line.product_uom_id.name == 'Percentage')
            }))

        self.line_ids = line_vals

    @api.onchange('justification_id')
    def _onchange_rkap(self):
        self.rkap_id = self.justification_id.budgetary_position_id.general_budget_id.id
        self.rkap_code = self.justification_id.budgetary_position_id.rkap_code
        self.rkap_type_id = self.justification_id.budgetary_position_id.rkap_type_id.id
        self.rkap_category_id = self.justification_id.budgetary_position_id.rkap_category_id.id

    # @api.onchange('justification_id')
    # def _onchange_justification_line(self):
    #     new_lines = [(5, 0, 0)]
    #     if self.justification_id:
    #         for line in self.justification_id.line_ids:
    #             if line.procurement_type_id.name != 'FPJP':
    #                 new_line_vals = {
    #                     'justification_line_id': line.id,
    #                     'name': line.description,
    #                     'justif_line_fund_available': line.amount_currency,
    #                 }
    #                 new_lines.append((0, 0, new_line_vals))
    #         self.line_ids = new_lines
    #     else:
    #         self.line_ids = new_lines


    def get_sequence(self, name=False, obj=False, pref=False, context=None):
        sequence_id = self.env['ir.sequence'].search([
            ('name', '=', name),
            ('code', '=', obj),
            ('implementation', '=', 'standard'),
            ('prefix', '=', pref)
        ], limit=1)

        if not sequence_id:
            sequence_id = self.env['ir.sequence'].sudo().create({
                'name': name,
                'code': obj,
                'implementation': 'standard',
                'prefix': pref,
                'padding': 4 
            })

        return sequence_id.next_by_id()

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if vals.get('name', 'New') == 'New':
                date_start = vals.get('date_start') or fields.Datetime.now()
                dt = fields.Datetime.to_datetime(date_start)
                month = dt.month
                year = dt.year

                seq_prefix = 'PR/TEC/'
                seq_number = self.get_sequence(
                    name='Purchase Request',
                    obj='purchase.request',
                    pref=seq_prefix
                )
                vals['name'] = f"{seq_number}/{month}/{year}"
            if vals.get('pr_type') == 'rfq':
                rfq_id = self.env['bidding.rfq'].browse(vals['rfq_id'])
                rfq_id.write({'purchase_request_exist': True})
                print(rfq_id.purchase_request_exist, 'fifififi')
            if vals.get('pr_type') == 'rfp':
                agreement_id = self.env['bidding.agreement'].browse(vals['agreement_id'])
                agreement_id.write({'purchase_request_exist': True})
                print(agreement_id.purchase_request_exist, 'fufufufu')
        res = super(PurchaseRequest, self).create(vals_list)
        return res


    def action_submit(self):
        for record in self:
            if record.rfq_id:
                other_pr = self.env['purchase.request'].search([('rfq_id', '=', record.rfq_id.id), ('id', '!=', self.id), ('state', 'not in', ['rejected', 'canceled'])], limit=1)
                if other_pr:
                    raise ValidationError(f'RFQ already used by PR {other_pr.name}!')
            # elif record.agreement_id:
            #     other_pr = self.env['purchase.request'].search([('agreement_id', '=', record.agreement_id.id), ('id', '!=', self.id), ('state', 'not in', ['rejected', 'canceled'])], limit=1)
            #     if other_pr:
            #         raise ValidationError(f'MPA already used by PR {other_pr.name}!')

            state_label = dict(self._fields['state'].selection).get(self.state, '')
            if record.state != 'draft':
                raise UserError(_("Data ini sudah dalam status '%s'. Harap muat ulang halaman untuk melihat status yang diperbarui.") % state_label)
            
            if not record.line_ids:
                raise UserError(_("Data produk belum diisi."))

            if record.line_ids.filtered(lambda l: l.justification_line_id == False or l.start_date == False or l.end_date == False):
                raise UserError(_("Data produk tidak lengkap. Harap periksa data produk terlebih dahulu."))

            zero_price_lines = record.line_ids.filtered(lambda l: l.product_qty == 0)
            if record.pr_type != 'manual' and zero_price_lines:
                raise UserError(_("Terdapat baris dengan quantity 0."))
            record.state = 'to_approve'

    def action_cancel_wizard(self):
        self.ensure_one()
        return {
            'name': 'Cancel Purchase Request',
            'type': 'ir.actions.act_window',
            'res_model': 'purchase.request.cancel.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_request_id': self.id,
            }
        }

    def action_cancel(self):
        for record in self:
            record.state = 'canceled'
            if record.pr_type == 'rfq':
                record.rfq_id.write({'purchase_request_exist': False})
            if record.pr_type == 'rfp':
                record.agreement_id.write({'purchase_request_exist': False})

    def action_asign_buyer(self):
        for record in self:
            today = fields.Date.context_today(self)
            state_label = dict(self._fields['state'].selection).get(self.state, '')
            if record.state != 'to_approve':
                raise UserError(_("Data ini sudah dalam status '%s'. Harap muat ulang halaman untuk melihat status yang diperbarui.") % state_label)
            record.assigne_buyer_date = today
            record.state = 'assign_buyer'

    def action_withdraw_approval(self):
        for record in self:
            record.state = 'draft'

    def button_reassign_pic(self):
        self.ensure_one()
        view_id = self.env.ref('ap_purchase_request.purchase_request_reassign_pic_form_view').id

        return {
            'type': 'ir.actions.act_window',
            'name': _('Re-Assign PIC'),
            'view_mode': 'form',
            'res_model': 'purchase.request.reassign.pic',
            'target': 'new',
            'views': [[view_id, 'form']],
            'context': {
                'default_purchase_request_id': self.id,
                'default_current_pic_id': self.pic_id.id,
            }
        }

    @api.constrains('estimated_cost', 'dpl_id', 'is_dpl')
    def _check_amount_available_justif_dpl(self):
        for record in self:
            if record.is_dpl and record.dpl_id:
                existing_cost = 0.0
                if record._origin and record._origin.id:
                    existing = record.browse(record._origin.id)
                    if existing.dpl_id == record.dpl_id:
                        existing_cost = existing.estimated_cost

                adjusted_available = record.dpl_id.available_dpl + existing_cost

                if record.estimated_cost > adjusted_available:
                    # Collect existing purchase requests and calculate total efficiently
                    pr_records_info = []
                    total_used = 0.0
                    for pr in record.dpl_id.purchase_request_ids:
                        if pr.state != 'canceled' and pr.id != record.id:
                            total_used += pr.estimated_cost
                            pr_records_info.append(f"- {pr.name}: {pr.estimated_cost:,.2f} ({pr.state})")
                    
                    error_msg = f"""Total estimated cost ({record.estimated_cost:,.2f}) cannot exceed the available DPL budget ({adjusted_available:,.2f}).

DPL Information:
- DPL Number: {record.dpl_id.name}
- DPL Total Amount: {record.dpl_id.amount:,.2f}
- Total Used by PR: {total_used:,.2f}
- Available Budget: {adjusted_available:,.2f}
- Current Request: {record.estimated_cost:,.2f}

Existing Purchase Requests:
{chr(10).join(pr_records_info) if pr_records_info else "- No existing purchase requests"}"""
                    
                    raise ValidationError(error_msg)


    @api.onchange('line_ids', 'estimated_cost', 'is_dpl', 'dpl_id')
    def _onchange_check_amount_available_justif_dpl(self):
        if self.is_dpl and self.dpl_id:
            # Ambil nilai yang sudah ada (jika record sudah tersimpan)
            existing_cost = 0.0
            if self._origin and self._origin.id:
                existing = self.browse(self._origin.id)
                if existing.dpl_id == self.dpl_id:
                    existing_cost = existing.estimated_cost

            # Hitung available DPL yang disesuaikan (seolah mengembalikan dulu existing_cost)
            adjusted_available = self.dpl_id.available_dpl + existing_cost

            # print('Adjusted available:', adjusted_available)
            # print('New estimated cost:', self.estimated_cost)

            if self.estimated_cost > adjusted_available:
                return {
                    'warning': {
                        'title': "Warning",
                        'message': "Total cannot exceed the available DPL.",
                    }
                }

    @api.onchange('is_dpl', 'dpl_id', 'dpl_id.amount', 'estimated_cost')
    def onchange_estimated_cost_validation(self):
        if self.is_dpl and self.dpl_id:
            if self.estimated_cost > self.dpl_id.amount:
                raise ValidationError('Total Estimated Cost cannot exceed amount DPL.')

    def write(self, vals):
        if vals.get('rfq_type'):
            if vals['rfq_type'] == 'manual':
                if self.pr_type == 'rfq':
                    self.rfq_id.write({'purchase_request_exist': False})
                if self.pr_type == 'rfp':
                    self.agreement_id.write({'purchase_request_exist': False})
            if vals['rfq_type'] == 'rfq':
                if self.pr_type == 'rfp':
                    self.agreement_id.write({'purchase_request_exist': False})
                    rfq_id = self.env['bidding.rfq'].browse(vals['rfq_id'])
                    rfq_id.write({'purchase_request_exist': True})
            if vals['rfq_type'] == 'rfp':
                if self.pr_type == 'rfq':
                    self.rfq_id.write({'purchase_request_exist': False})
                    agreement_id = self.env['bidding.agreement'].browse(vals['agreement_id'])
                    agreement_id.write({'purchase_request_exist': True})
        
        print(self.agreement_id.purchase_request_exist, 'mpaaaaa')
        print(self.rfq_id.purchase_request_exist, 'rfqqqqqqq')
        res = super(PurchaseRequest, self).write(vals)
        return res

    def unlink(self):
        if self.pr_type == 'rfq':
            self.rfq_id.write({'purchase_request_exist': True})
        if self.pr_type == 'rfp':
            self.agreement_id.write({'purchase_request_exist': True})
        res = super(PurchaseRequest, self).unlink()
        return res

    def button_draft(self):
        res = super(PurchaseRequest, self).button_draft()
        if self.pr_type == 'rfq':
            self.rfq_id.write({'purchase_request_exist': False})
        if self.pr_type == 'rfp':
            self.agreement_id.write({'purchase_request_exist': False})
        # if self.pr_type == 'rfq':
        #     existing_pr = self.search([('pr_type', '=', 'rfq'), ('rfq_id', '=', self.rfq_id.id), ('state', 'not in', ['rejected', 'canceled'])])
        #     for pr in existing_pr:
        #         raise ValidationError('Tidak bisa set to draft karena ada data Purchase Request %s dengan RFQ Number %s yang masih berstatus %s' % (pr.name, pr.rfq_id.name, dict(pr.fields_get(['state'])['state']['selection']).get(pr.state)))
        # if self.pr_type == 'rfp':
        #     existing_pr = self.search([('pr_type', '=', 'rfp'), ('agreement_id', '=', self.agreement_id.id), ('state', 'not in', ['rejected', 'canceled'])])
        #     for pr in existing_pr:
        #         raise ValidationError('Tidak bisa set to draft karena ada data Purchase Request %s dengan RFQ Number %s yang masih berstatus %s' % (pr.name, pr.rfq_id.name, dict(pr.fields_get(['state'])['state']['selection']).get(pr.state)))
        return res




