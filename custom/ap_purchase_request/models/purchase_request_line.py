import logging
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

from odoo import SUPERUSER_ID, api, fields, models, _
from odoo.exceptions import ValidationError, UserError

class PurchaseRequestLine(models.Model):
    _inherit = "purchase.request.line"

    def _get_default_currency_rate(self):
        latest_rate = self.env['res.currency.rate'].search([
            ('currency_id', '=', self.env.user.company_id.currency_id.id)
        ], order='name desc', limit=1)
        return latest_rate.inverse_company_rate if latest_rate else 1

    justification_line_ids = fields.Many2many(
        comodel_name="account.justification.line", 
        string="Justifikasi Lines",
        compute="_compute_justification_line_ids",
        store=False)
    justification_line_id = fields.Many2one(
        comodel_name="account.justification.line",
        string="Justifikasi Line",
        index=True, copy=False,
        domain="[('id', 'in', justification_line_ids)]",
        help="Referensi justifikasi line yang digunakan sebagai dasar PR.")
    pr_type = fields.Selection([
        ('rfq', 'RFQ'),
        ('rfp', 'MPA'),
        ('manual', 'Manual'),
    ], string='PR Type', copy=False, related="request_id.pr_type",
       help="Jenis Purchase Request yang digunakan, misalnya RFQ, MPA, atau Manual.")
    product_tmpl_agreement_id = fields.Many2one('product.template', string='Product Template Agreement')
    product_agreement_id = fields.Many2one('product.product', string='Product Agreement')
    product_tmpl_id = fields.Many2one(
        comodel_name="product.template", 
        string="Product",
        help="The product template associated with this purchase request line.")
    product_category_id = fields.Many2one(
        comodel_name="product.category", 
        string="Product Category",
        help="The product category associated with this product.")
    new_currency_id = fields.Many2one(
        comodel_name="res.currency", 
        string="Currency",
        default=lambda self: self.env.user.company_id.currency_id.id,
        help="The currency in which the unit price is defined.")
    currency_rate = fields.Float(
        string='Rate', default=_get_default_currency_rate)
    price_unit = fields.Monetary(
        string='Unit Price', required=True, digits='Product Price', currency_field="new_currency_id")
    subtotal_currency = fields.Monetary(
        currency_field="new_currency_id",
        compute="_compute_subtotal_currency", store=True,
        help="Subtotal currency of Purchase Request Line",
    )
    justif_line_fund_available = fields.Monetary(
        string="Justif Line Fund Available",
        default=0.0,
        help="Justif Line Fund Available of Purchase Request Line",
    )
    estimated_cost = fields.Monetary(
        currency_field="currency_id",
        compute="_compute_subtotal_idr", store=True,
        help="Estimated cost of Purchase Request Line, not propagated to PO.",
    )
    start_date = fields.Date(string='Start Delivery Date', copy=False,
                                      help="Tanggal mulai pengiriman barang/jasa.")

    end_date = fields.Date(string='End Delivery Date', copy=False,
                                    help="Tanggal selesai pengiriman barang/jasa.")
    is_manajemen_fee = fields.Boolean(string='Management Fee', copy=False,
                                    help="Centang jika termasuk manajemen fee.")
    is_percentage = fields.Boolean(
        string="Is Percentage",
    )
    purchase_state = fields.Selection(
        compute="_compute_purchase_state",
        string="Purchase Status",
        selection=lambda self: self.env["purchase.order"]._fields["new_state"].selection,
        store=False,
    )
    domain_currency = fields.Binary(string='Currency Filter',compute='_compute_domain_currency')

    unit_price_source = fields.Float(string='Unit Price Source', compute='_compute_unit_price_source', store=True)

    remaining_amount = fields.Monetary('Remaining Amount', related="justification_line_id.remaining_amount")    

    @api.depends('request_id.state', 'product_qty')
    def _compute_purchased_qty(self):
        for line in self:
            if line.request_id.state in ("approved", "done"):
                line.purchased_qty = line.product_qty
            else:
                line.purchased_qty = 0.0

    @api.depends('request_id.agreement_id', 'request_id.rfq_id')
    def _compute_unit_price_source(self):
        for rec in self:
            unit_price_source = 0
            if rec.request_id.agreement_id:
                mpa = rec.request_id.agreement_id.line_ids.filtered(lambda x: x.product_variant_id == rec.product_id)
                if mpa:
                    unit_price_source = mpa[0].unit_price
            elif rec.request_id.rfq_id:
                rfq = rec.request_id.rfq_id.order_line.filtered(lambda x: x.product_variant_id == rec.product_id)
                if rfq:
                    unit_price_source = rfq[0].price_unit
                    
            rec.unit_price_source = unit_price_source

    @api.depends('currency_id')
    def _compute_domain_currency(self):
        for record in self:
            company_currency = self.env.company.currency_id
            budget_currency = self.env['res.currency'].search([('is_budget', '=', True)])
            record.domain_currency = [('id', 'in', [budget_currency.id,company_currency.id])]


    @api.depends('request_id.dpl_id', 'request_id.justification_id', 'request_id.is_dpl')
    def _compute_justification_line_ids(self):
        for record in self:
            domain = [
                ('procurement_type', '=', 'procurement'),
                ('justification_id', '=', record.request_id.justification_id.id)
            ]
            if record.request_id.is_dpl:
                domain.append(('procurement_type_id.is_dpl', '=', True))
            record.justification_line_ids = self.env['account.justification.line'].search(domain) 
            # if record.request_id.is_dpl:
            #     # Jika request_id.dpl_id terisi, hanya ambil justification_line_id yang berkaitan dengan procurement_type_id.is_dpl == True
            #     record.justification_line_ids = self.env['account.justification.line'].search([
            #         ('procurement_type_id.is_dpl', '=', True),
            #         ('procurement_type', '=', 'procurement'),
            #         ('justification_id', '=', record.request_id.justification_id.id)
            #     ])
            # else:
            #     # Jika request_id.dpl_id kosong, ambil justification_line_id berdasarkan request_id.justification_id
            #     record.justification_line_ids = self.env['account.justification.line'].search([
            #         ('procurement_type_id.is_dpl', '!=', True),
            #         ('procurement_type', '=', 'procurement'),
            #         ('justification_id', '=', record.request_id.justification_id.id)
            #     ])

    @api.onchange('product_tmpl_id')
    def _onchange_description(self):
        self.name = self.product_tmpl_id.display_name

    @api.onchange('product_uom_id')
    def _onchange_is_percentage(self):
        for rec in self:
            uom_name = (rec.product_uom_id.name or '').replace(' ', '').lower()
            rec.is_percentage = uom_name == 'percentage'
            if not rec.is_percentage:
                rec.is_manajemen_fee = False

    @api.onchange('is_percentage')
    def _onchange_uom_percentage(self):
        uom_id = self.env['uom.uom'].search([('name','ilike','percentage')],limit=1)
        if self.is_percentage and uom_id:
            self.product_uom_id = uom_id.id

    @api.onchange('product_tmpl_id')
    def onchange_category_product(self):
        self.product_category_id = self.product_tmpl_id.categ_id.id


    @api.onchange('justification_line_id')
    def onchange_justif_line_fund_available(self):
        if self.justification_line_id:
            self.new_currency_id = self.justification_line_id.currency_id.id
            amount = self.justification_line_id.amount_currency - self.justification_line_id.remaining_amount
            self.justif_line_fund_available = self.justification_line_id.amount_currency - amount

    @api.onchange('new_currency_id')
    def _onchange_currency_id(self):
        if self.new_currency_id:
            latest_rate = self.env['res.currency.rate'].search([
                ('currency_id', '=', self.new_currency_id.id)
            ], order='name desc', limit=1) 
            
            self.currency_rate = latest_rate.inverse_company_rate if latest_rate else 1

    @api.onchange("product_id")
    def onchange_product_qty(self):
        if self.product_id:
            self.product_qty = 1
            self._get_agreement_product()

    def _get_agreement_product(self):
        if self.request_id.agreement_id:
            line = False
            if self.product_id:
                line = self.request_id.agreement_id.line_ids.filtered(lambda x: x.is_active and x.product_variant_id == self.product_id)
            else:
                line = self.request_id.agreement_id.line_ids.filtered(lambda x: x.is_active and x.product_tmpl_id == self.product_tmpl_id)
            if line : 
                line = line[0]
                latest_rate = self.env['res.currency.rate'].search([
                    ('currency_id', '=', line.currency_id.id)
                ], order='name desc', limit=1) 
                
                currency_rate = latest_rate.inverse_company_rate if latest_rate else 1
                self.write({
                        'product_category_id': line.product_tmpl_id.categ_id.id,
                    'product_uom_id': line.product_uom_id.id,
                    'name': line.description,
                    'new_currency_id': line.currency_id.id,
                    'currency_rate': currency_rate,
                    'price_unit': line.unit_price,
                    'product_qty': line.qty,
                    'is_percentage': bool(line.product_uom_id.name == 'Percentage')
                })

    @api.onchange('product_agreement_id', 'product_tmpl_agreement_id')
    def _onchange_product_agreement(self):
        if self.product_tmpl_agreement_id :
            self.product_tmpl_id = self.product_tmpl_agreement_id.id
            self.product_uom_id = self.product_tmpl_id.uom_po_id.id
            self._get_agreement_product()

            
        if self.product_agreement_id :
            self.product_id = self.product_agreement_id.id
            self._get_agreement_product()

    @api.onchange("product_tmpl_id")
    def onchange_uom_product_tmpl_id(self):
        if self.product_tmpl_id:
            self.product_uom_id = self.product_tmpl_id.uom_po_id.id

    @api.depends("price_unit","product_qty","new_currency_id")
    def _compute_subtotal_currency(self):
        for line in self:
            line.subtotal_currency = line.price_unit * line.product_qty

    @api.depends("price_unit","product_qty","new_currency_id")
    def _compute_subtotal_idr(self):
        for line in self:
            subtotal_currency = line.price_unit * line.product_qty
            line.estimated_cost = subtotal_currency * line.currency_rate

    @api.onchange('is_percentage', 'product_uom_id', 'request_id.total_manajemen_fee', 'product_qty')
    def _onchange_total_percentage(self):
        if self.is_percentage and self.request_id.total_manajemen_fee:
            manajemen_fee = (self.product_qty / 100.0) * self.request_id.total_manajemen_fee
            if self.product_qty != 0:
                self.price_unit = manajemen_fee / self.product_qty

    @api.onchange('price_unit', 'product_qty', 'new_currency_id')
    def _onchange_check_estimated_cost(self):
        for line in self:
            if not line.is_percentage:
                if line.estimated_cost > line.justif_line_fund_available:
                    raise UserError(_("Estimated Cost cannot exceed the Fund Available!"))

    # @api.depends("purchase_lines.state", "purchase_lines.order_id.state")
    # def _compute_purchase_state(self):
    #     for rec in self:
    #         purchase_lines = self.env['purchase.order.line'].search([('purchase_request_line_id', '=', rec.id)])
    #         temp_purchase_state = False
    #         if any(po_line.order_id.new_state == "done" for po_line in purchase_lines):
    #             temp_purchase_state = "done"
    #         elif all(po_line.order_id.new_state == "closed" for po_line in purchase_lines):
    #             temp_purchase_state = "done"
    #         elif any(po_line.order_id.new_state == "draft" for po_line in purchase_lines):
    #             temp_purchase_state = "open"
    #         elif any(po_line.order_id.new_state == "pending_approval" for po_line in purchase_lines):
    #             temp_purchase_state = "open"
    #         elif any(po_line.order_id.new_state == "pending_acknowledge" for po_line in purchase_lines):
    #             temp_purchase_state = "open"
    #         elif any(po_line.order_id.new_state == "open" for po_line in purchase_lines):
    #             temp_purchase_state = "open"
    #         elif any(po_line.order_id.new_state == "return" for po_line in purchase_lines):
    #             temp_purchase_state = "return"
    #         elif any(po_line.order_id.new_state == "canceled" for po_line in purchase_lines):
    #             temp_purchase_state = "canceled"
    #         elif any(
    #             po_line.order_id.new_state == "pending_approval" for po_line in purchase_lines
    #         ):
    #             temp_purchase_state = "pending_approval"
    #         elif any(po_line.order_id.new_state == "pending_acknowledge" for po_line in purchase_lines):
    #             temp_purchase_state = "pending_acknowledge"
    #         elif all(
    #             po_line.order_id.new_state in ("draft", "canceled")
    #             for po_line in purchase_lines
    #         ):
    #             temp_purchase_state = "draft"
    #         rec.purchase_state = temp_purchase_state
            
            
    def _compute_purchase_state(self):
        for rec in self:
            if rec.request_id and rec.request_id.state == 'done':
                rec.purchase_state = "done"
            else:
                rec.purchase_state = False

            




