from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)

class PurchaseRequestCancelWizard(models.TransientModel):
    _name = 'purchase.request.cancel.wizard'
    _description = 'Cancel Purchase Request Wizard'

    reason = fields.Text(string='Text', default='<PERSON><PERSON><PERSON>h kamu yakin ingin cancel data purchase request ini ?')
    request_id = fields.Many2one('purchase.request', string='Purchase Request')

    def action_confirm_cancel(self):
        self.ensure_one()
        if self.request_id.justification_id:
            justif = self.env['account.justification'].search([('id', '=', self.request_id.justification_id.id)], limit=1)
            justif.write({'amount_justif': justif.remaining_amount})
        self.request_id.action_cancel()
