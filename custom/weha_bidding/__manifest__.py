# -*- coding: utf-8 -*-
#############################################################################
#
#    You can modify it under the terms of the GNU LESSER
#    GENERAL PUBLIC LICENSE (LGPL v3), Version 3.
#
#    This program is distributed in the hope that it will be useful,
#    but WITHOUT ANY WARRANTY; without even the implied warranty of
#    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#    GNU LESSER GENERAL PUBLIC LICENSE (LGPL v3) for more details.
#
#    You should have received a copy of the GNU LESSER GENERAL PUBLIC LICENSE
#    (LGPL v3) along with this program.
#    If not, see <http://www.gnu.org/licenses/>.
#
#############################################################################
{
    'name': 'Weha Inherit Bidding',
    'version': '18.0.1.0.0',
    'category': 'Purchase',
    'author': 'WEHA',
    'company': 'WEHA',
    'maintainer': 'WEHA',
    'depends': ['base', 'ap_bidding', 'ap_bidding_rfq','ap_bidding_agreement','ap_bidding_negotiation','account_fpjp'],
    'data': [
        # 'security/ir.model.access.csv',
        'views/inherit_bidding_rfq_view.xml',
        'views/inherit_bidding_negotiation_view.xml',
        'views/inherit_bidding_view.xml',
        'data/email_data.xml'
    ],
    'assets': {
    },
    'license': 'LGPL-3',
    'installable': True,
    'auto_install': False,
    'application': False,
}
