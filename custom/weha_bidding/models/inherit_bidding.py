from odoo import _, api, fields, models
from odoo.exceptions import ValidationError
from markupsafe import escape, Markup



class BiddingBidding(models.Model):
    _inherit = 'bidding.bidding'


    def action_confirm(self):
        # Call the original method first
        result = super(BiddingBidding, self).action_confirm()
        
        # Send confirmation email to each vendor
        for record in self:
            # Get the email template
            template = self.env.ref('weha_bidding.weha_bidding_confirm_template')
            
            # Send email to each vendor
            for vendor in record.bidding_vendor_ids:
                if vendor.reassign_pic_email:
                    # Create a context with vendor information for reassign PIC
                    ctx = {
                        'vendor_name': vendor.vendor_id.name,
                    }
                    
                    # Send the email with the reassigned PIC context but mark as outgoing only (not sent immediately)
                    template.with_context(ctx).send_mail(
                        record.id, 
                        force_send=True,
                        email_values={
                            'email_to': vendor.reassign_pic_email,
                            'email_cc': vendor.cc_email or '',
                        },
                    )
                elif vendor.pic_email:
                    # Create a context with vendor information
                    ctx = {
                        'vendor_name': vendor.vendor_id.name,
                    }
                    
                    # Send the email with the vendor context but mark as outgoing only (not sent immediately)
                    template.with_context(ctx).send_mail(
                        record.id, 
                        force_send=True,
                        email_values={
                            'email_to': vendor.pic_email,
                            'email_cc': vendor.cc_email or '',
                        },
                    )
                else:
                    raise ValidationError("You can't send email")
        
        return result


    def send_notification_email(self):
        for len in self.bidding_vendor_ids:

            line = ""
            for rec in self.line_ids:
                line += f"""
                            <tr>
                            <td>{rec.product_tmpl_id.name}</td>
                            <td>{rec.product_variant_id.name}</td>
                            <td>{rec.description}</td>
                            <td>{rec.quantity}</td>
                            <td>{rec.product_uom_id.name}</td>
                            <td>{rec.currency_id.name}</td>
                            <td>{rec.unit_price}</td>
                            <td>{rec.total}</td>
                            </tr>
                """

            template = f"""
                <div style="margin: 0px; padding: 0px;">
                    <table border="0" width="100%" cellpadding="0" bgcolor="#ededed" style="padding: 20px; background-color: #ededed; border-collapse:separate;">
                        <tbody>
                            <!-- HEADER -->
                            <tr>
                                <td align="center" style="min-width: 890px;">
                                <table width="890" border="0" cellpadding="0" bgcolor="#875A7B" style="min-width: 890px; background-color: rgb(135,90,123); padding: 20px; border-collapse:separate;">
                                    <tr>
                                    <td valign="middle">
                                        <span style="font-size:20px; color:white; font-weight: bold;">
                                        Bidding Start - {self.description}
                                        </span>
                                    </td>
                                    <td valign="middle" align="right">
                                        <img src="/logo.png?company={self.company_id.id}" style="padding: 0px; margin: 0px; height: auto; width: 80px;" alt="${self.company_id.name}" />
                                    </td>
                                    </tr>
                                </table>
                                </td>
                            </tr>

                            <!-- CONTENT -->
                            <tr>
                                <td align="center" style="min-width: 890px;">
                                <table width="890" border="0" cellpadding="0" bgcolor="#ffffff" style="min-width: 890px; background-color: rgb(255, 255, 255); padding: 20px; border-collapse:separate;">
                                    <tbody>
                                    <td valign="top" style="font-family:Arial,Helvetica,sans-serif; color: #555; font-size: 14px;">
                                        <p style="margin: 0px; padding: 0px; font-size: 13px;">
                                        Dear {len.vendor_id.name},<br /><br />
                                        We are pleased to inform you that the bidding has been started <br />
                                        for {self.description} with following information:<br /><br />
                                        Project Name: {self.description}<br />
                                        Reference: {self.name}<br />
                        
                                        <br /><br />
                                        You are requested to review the bidding documents and submit your proposal through the Linkaja’s <br />
                                        Vendor Portal before the specified deadline. To access the bidding documents and submit your offer, <br />
                                        please log in to the Linkaja’s Vendor Portal <a href="{self.company_id.vp_website}">{self.company_id.vp_website}
                                        </a> using your credentials. <br />
                                        If you encounter any difficulties accessing the platform, feel free to contact us at {self.company_id.email} / {self.company_id.mobile}.<br />
                                        Thank you!<br /><br />

                                        Sincerely, <br />
                                        {self.company_id.name}

                                        <br />
                                        <br />
                                        </p>
                                    </td>
                                    </tbody>
                                </table>
                                </td>
                            </tr>

                            <!-- FOOTER -->
                            <tr>
                                <td align="center" style="min-width: 890px;">
                                <table width="890" border="0" cellpadding="0" bgcolor="#875A7B" style="min-width: 890px; background-color: rgb(135,90,123); padding: 20px; border-collapse:separate;">
                                    <tr>
                                    <td valign="middle" align="left" style="color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;">
                                        {self.company_id.name}
                                        <br />
                                        {self.company_id.phone or ''}
                                    </td>
                                    <td valign="middle" align="right" style="color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;">
                                        <t t-if="{self.company_id.email}">
                                        <a href="mailto:${self.company_id.email}" style="text-decoration:none; color: white;">
                                            {self.company_id.email}
                                        </a>
                                        </t>

                                        <br />
                                        <t t-if="{self.company_id.website}">
                                        <a t-attf-href="{self.company_id.website}" style="text-decoration:none; color: white;">
                                            {self.company_id.website}
                                        </a>
                                        </t>
                                    </td>
                                    </tr>
                                </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            """

            # comment sementara untuk hapus email template start bidding
            # Start Date: {self.start_bidding_date}<br />
            # Deadline: {self.deadline_date}
            # <br /><br />

            # <table width="890" border="1">
            # <tr>
            # <th>Product</th>
            # <th>Product Variant</th>
            # <th>Description</th>
            # <th>Qty</th>
            # <th>UoM</th>
            # <th>Currency</th>
            # <th>Unit Price</th>
            # <th>Total</th>
            # </tr>

            # {line}              
            # </table>

            if not len.reassign_pic_id:
                mail_values = {
                            "subject": f"Bidding Start - {self.description}",
                            "body_html": template,
                            "email_to": len.pic_email,
                            "email_cc": len.cc_email or ''
                        }
            else:
                mail_values = {
                            "subject": f"Bidding Re-Assign - {self.description}",
                            "body_html": template,
                            "email_to": len.reassign_pic_email,
                            "email_cc": len.cc_email or ''
                        }
            self.env["mail.mail"].sudo().create(mail_values).send()
            self.message_post(
                body = Markup(template),
                subject=f"Bidding Notification Sent to {len.vendor_id.name}",
                message_type="comment",
                subtype_xmlid="mail.mt_note"
            )


    def action_start_bidding(self):
        for record in self:
            state_label = dict(self._fields['state'].selection).get(self.state, '')
            # if record.state not in ['confirm', 'close_bidding']:
            #     raise UserError(_("Data ini sudah dalam status '%s'. Harap muat ulang halaman untuk melihat status yang diperbarui.") % state_label)
            record.state = 'start_bidding'
            self.send_notification_email()


class BiddingLine(models.Model):
    _inherit = 'bidding.line'

    domain_currency = fields.Binary(string='Currency Filter',compute='_compute_domain_currency')

    @api.depends('currency_id')
    def _compute_domain_currency(self):
        for record in self:
            company_currency = self.env.company.currency_id
            budget_currency = self.env['res.currency'].search([('is_budget', '=', True)])
            record.domain_currency = [('id', 'in', [budget_currency.id,company_currency.id])]