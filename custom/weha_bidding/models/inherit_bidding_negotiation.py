
# -*- coding: utf-8 -*-
###############################################################################
#    License, author and contributors information in:                         #
#    __manifest__.py file at the root folder of this module.                  #
###############################################################################

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError


import logging
_logger = logging.getLogger(__name__)


class WehaBiddingNegotiation(models.Model):
    _inherit = 'bidding.negotiation'

    def action_submit(self):
        for record in self:
            state_label = dict(self._fields['state'].selection).get(self.state, '')
            if record.state != 'draft':
                raise UserError(_("Data ini sudah dalam status '%s'. Harap muat ulang halaman untuk melihat status yang diperbarui.") % state_label)
            
            if not record.line_ids:
                raise UserError(_("Data produk belum diisi."))

            # zero_price_lines = record.line_ids.filtered(lambda l: l.unit_price == 0)
            # for line in zero_price_lines:
            #     if not line.is_percentage:
            #         raise UserError(_("Terdapat baris dengan harga satuan 0. Mohon periksa dan isi harga satuan sebelum melanjutkan."))

            zero_qty_lines = record.line_ids.filtered(lambda l: l.quantity == 0)
            if zero_qty_lines:
                raise UserError(_("Terdapat baris dengan quantity 0. Mohon periksa dan isi quantity sebelum melanjutkan."))

            not_variant = record.line_ids.filtered(lambda l: not l.product_variant_id and not l.propose_variant)
            if not_variant:
                raise UserError("Setiap baris harus memiliki 'Product Variant' atau 'Propose Variant' yang diisi.")

            record.state = 'submit'
            record.submitted_date = fields.Datetime.now()
            # record.action_check_status_vendor()

    
    def action_submit_rfq(self):
        self.ensure_one()

        order_lines = []

        for line in self.line_ids.filtered(lambda x: x.is_award_line):
            if line.propose_variant:
                check_line = line.product_tmpl_id.product_line_ids.filtered(lambda x: x.name == line.propose_variant)
                if not check_line:
                    # Create a product_product_variant
                    self.env['product.product.variant'].create({
                        'product_tmpl_variant_id': line.product_tmpl_id.id,
                        'name': line.propose_variant,
                    })
                    line.product_tmpl_id.with_context(from_nego=True).create_variant()
                    # self.env.cr.execute("""
                    #     INSERT INTO product_product_variant (product_tmpl_variant_id, name, create_date, write_date)
                    #     VALUES (%s, %s, NOW(), NOW())
                    #     RETURNING id
                    # """, (line.product_tmpl_id.id, line.propose_variant))
                    # variant_id = self.env.cr.fetchone()[0]

        for line in self.line_ids.filtered(lambda x: x.is_award_line):
            if line.propose_variant:
                check_line = line.product_tmpl_id.product_line_ids.filtered(lambda x: x.name == line.propose_variant)
                if not check_line:
                    # Create a product_product_variant
                    self.env['product.product.variant'].create({
                        'product_tmpl_variant_id': line.product_tmpl_id.id,
                        'name': line.propose_variant,
                    })
                    line.product_tmpl_id.with_context(from_nego=True).create_variant()

            new_variant = self.env['product.product'].search([('variant_id.name', '=', line.propose_variant), ('product_tmpl_id', '=', line.product_tmpl_id.id)], limit=1)

            # is_product = 0
            # if line.product_variant_id.id and line.propose_variant_id.id:
            #     is_product = line.propose_variant_id.id
            # elif not line.product_variant_id.id and line.propose_variant_id.id:
            #     is_product = line.propose_variant_id.id
            # elif line.product_variant_id.id and not line.propose_variant_id.id:
            #     is_product = line.product_variant_id.id
            # elif not line.product_variant_id.id and not line.propose_variant_id.id:
            #     is_product = 0
            

            product_qty = 1
            if line.is_percentage == True:
                product_qty = line.quantity

            order_lines.append((0, 0, {
                'product_tmpl_id': line.product_tmpl_id.id,
                'product_variant_id': new_variant.id if new_variant else line.product_variant_id.id,
                'description': line.description,
                'product_qty': product_qty,
                'product_uom_id': line.product_uom_id.id,
                'price_unit': line.unit_price,
                'price_subtotal': line.total,
                'currency_id': line.currency_id.id
            }))

        vals = {}
        vals.update({'negotiation_id': self.id})
        vals.update({'bidding_id': self.bidding_id.id})
        vals.update({'partner_id': self.partner_id.id})
        vals.update({'description': self.bidding_description})
        vals.update({'requestor_id': self.requestor_id.id})
        vals.update({'unit_id': self.unit_id.id})
        vals.update({'buyer_id': self.buyer_id.id})
        vals.update({'group_id': self.group_id.id})
        vals.update({'payment_term_id': self.payment_term_id.id})
        vals.update({'vendor_status': self.vendor_status})
        vals.update({'due_diligence_status': self.due_diligence_status})
        vals.update({'order_line': order_lines})

        result = self.env['bidding.rfq'].create(vals)

        if not result:
            raise ValidationError("Error Create RFQ Programmatically.")
        else:
            return {
                'name': _('RFQ'),
                'view_mode': 'form',
                'views': [(False, 'form')],
                'view_id': self.env.ref('ap_bidding_rfq.action_bidding_rfq').id,
                'res_model': 'bidding.rfq',
                'type': 'ir.actions.act_window',
                'res_id': result.id,
            }

