
# -*- coding: utf-8 -*-
###############################################################################
#    License, author and contributors information in:                         #
#    __manifest__.py file at the root folder of this module.                  #
###############################################################################

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError




class WehaBiddingAgreement(models.Model):
    _inherit = 'bidding.agreement'

    
    bidding_rfq_id = fields.Many2one(
        string='Bidding RFQ',
        comodel_name='bidding.rfq',
        ondelete='set null',
    )
    
    def write(self, vals):
        result = super(WehaBiddingAgreement, self).write(vals)
        if 'bidding_rfq_id' in vals:
            # Get the new bidding_rfq_id
            new_rfq_id = vals.get('bidding_rfq_id')
            if new_rfq_id:
                # Set the new RFQ state to done
                rfq = self.env['bidding.rfq'].browse(new_rfq_id)
                if rfq.state == 'approve':
                    rfq.state = 'done'
            
            # Set the old RFQ state back to approve if it was in done state
            if self.bidding_rfq_id and self.bidding_rfq_id.id != new_rfq_id and self.bidding_rfq_id.state == 'done':
                self.bidding_rfq_id.state = 'approve'
        
        return result

    

    
