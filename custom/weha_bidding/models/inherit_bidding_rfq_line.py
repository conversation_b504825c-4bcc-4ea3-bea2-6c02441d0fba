
# -*- coding: utf-8 -*-
###############################################################################
#    License, author and contributors information in:                         #
#    __manifest__.py file at the root folder of this module.                  #
###############################################################################

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError



class WehaBiddingRfqLine(models.Model):
    _inherit = 'bidding.rfq.line'

    
    product_tmpl_id = fields.Many2one(
        comodel_name="product.template", 
        string="Product",
        help="The product template associated with this agreement line.")
    product_variant_id = fields.Many2one(
        comodel_name="product.product", 
        string="Product Variant",
        help="The specific product variant linked to the selected product template.",
        domain="[('product_tmpl_id', '=', product_tmpl_id)]")
    is_active = fields.Boolean(string='Active',)
    

    

