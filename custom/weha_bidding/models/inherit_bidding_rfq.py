
# -*- coding: utf-8 -*-
###############################################################################
#    License, author and contributors information in:                         #
#    __manifest__.py file at the root folder of this module.                  #
###############################################################################

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError



class WehaBiddingRfq(models.Model):
    _inherit = 'bidding.rfq'


    mpa_ids = fields.One2many('bidding.agreement', 'bidding_rfq_id', string='MPA')
    is_po_exists = fields.Bo<PERSON>an(string='Is PO Exists')
    is_pr_exists = fields.Boolean(string='Is PR Exists')
    flagging_rfq_used = fields.Boolean('Flagging RFq Used', default=False, compute='_compute_flagging_rfq_used')

    def _compute_flagging_rfq_used(self):
        for rec in self:
            po = rec.env['purchase.order'].search([('bidding_rfq_id', '=', self.id)], limit=1)
            pr = rec.env['purchase.request'].search([('rfq_id', '=', self.id)], limit=1)
            if po or pr:
                rec.flagging_rfq_used = True
            else:
                rec.flagging_rfq_used = False

    def check_exist_data(self):
        for rec in self:
            rec.is_po_exists = False
            rec.is_pr_exists = False

    def unlink(self):
        for record in self:
            if record.mpa_ids:
                if any(mpa.state != 'draft' for mpa in record.mpa_ids):
                    raise ValidationError(_("You cannot delete this RFQ as it has related MPA(s) that are not in draft state."))
        return super(WehaBiddingRfq, self).unlink()


    def action_open_mpa(self):
        self.ensure_one()
        return {
            'name': _('MPA'),
            'view_mode': 'list,form',
            'res_model': 'bidding.agreement',
            'type': 'ir.actions.act_window',
            'domain': [('bidding_rfq_id', '=', self.id)],
        }

    def action_pending_approval(self):
        self.ensure_one()
        self.state = "pending_approval"

    def action_approval(self):
        self.ensure_one()
        self.state = "approve"

    def action_to_draft(self):
        self.ensure_one()
        self.state = "draft"

    def action_create_mpa(self):

        bidding_agreement = self.env['bidding.agreement'].search([('bidding_rfq_id', '=', self.id)])
        if len(bidding_agreement) != 0:
            self.state = 'done'
            raise ValidationError("MPA from RFQ already exists")
        else:
            order_lines = []
            for line in self.order_line:
                order_lines.append((0, 0, {
                    'product_tmpl_id': line.product_tmpl_id.id,
                    'product_variant_id': line.product_variant_id.id,
                    'description': line.description,
                    'currency_id': line.currency_id.id,
                    'qty': line.product_qty,
                    'product_uom_id': line.product_uom_id.id,
                    'is_percentage': line.is_percentage,
                    'unit_price': line.price_unit,
                    'is_active': line.is_active,
                }))
        
            vals = {}
            vals.update({'bidding_rfq_id': self.id})
            vals.update({'negotiation_id': self.negotiation_id.id})
            vals.update({'bidding_id': self.bidding_id.id})
            vals.update({'partner_id': self.partner_id.id})
            vals.update({'bidding_description': self.description})
            vals.update({'requestor_id': self.requestor_id.id})
            vals.update({'unit_id': self.unit_id.id})
            vals.update({'buyer_id': self.buyer_id.id})
            vals.update({'group_id': self.group_id.id})
            vals.update({'payment_term_id': self.payment_term_id.id})
            vals.update({'vendor_status': self.vendor_status})
            vals.update({'due_diligence_status': self.due_diligence_status})
            vals.update({'attachment_ids': self.attachment_ids.ids})
            vals.update({'line_ids': order_lines})

            result = self.env['bidding.agreement'].create(vals)

            if not result:
                raise ValidationError("Error Create MPA Programmatically.")
            else:
                self.state = 'done'
                return {
                    'name': _('MPA'),
                    'view_mode': 'form',
                    'views': [(False, 'form')],
                    'view_id': self.env.ref('ap_bidding_agreement.action_agreement').id,
                    'res_model': 'bidding.agreement',
                    'type': 'ir.actions.act_window',
                    'res_id': result.id,
                }
            


    payment_term_id = fields.Many2one(
        string='TOP',
        comodel_name='account.payment.term',
    )
    state = fields.Selection(
        selection=[('draft', "Draft"),
            ('submit', "Submited"),
            ('pending_approval', "Pending Approval"),
            ('approve', "Approve"),
            ('cancel', "Cancelled"),
            ('done', "Done"),
        ],
        string="Status",
        readonly=True, 
        copy=False, 
        index=True,
        default='draft')
    
    

    

