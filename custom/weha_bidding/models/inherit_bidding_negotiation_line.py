
# -*- coding: utf-8 -*-
###############################################################################
#    License, author and contributors information in:                         #
#    __manifest__.py file at the root folder of this module.                  #
###############################################################################

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError


import logging
_logger = logging.getLogger(__name__)


class WehaBiddingNegotiationLine(models.Model):
    _inherit = 'bidding.negotiation.line'

    
    propose_variant_id = fields.Many2one(
        comodel_name="product.product", 
        string="Propose Variant",
        help="The specific product variant linked to the selected product template.",
        domain="[('product_tmpl_id', '=', product_tmpl_id)]")
