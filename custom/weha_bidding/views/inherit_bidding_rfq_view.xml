<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>

        <record id="view_weha_bidding_rfq_form" model="ir.ui.view">
            <field name="name">view bidding.rfq inherited</field>
            <field name="model">bidding.rfq</field>
            <field name="inherit_id" ref="ap_bidding_rfq.view_bidding_rfq_form" />
            <field name="arch" type="xml">
                <xpath expr="//form/sheet" position="inside">
                    <field name="mpa_ids" invisible="1"/>
                </xpath>
                <xpath expr="//div[@name='button_box']" position="inside">
                    <button class="oe_stat_button" name="action_open_mpa" type="object" icon="fa-bars" invisible="not mpa_ids">
                        <div class="o_stat_info">
                            <span class="o_stat_text">
                                MPA
                            </span>
                        </div>
                    </button>
                </xpath>
                <xpath expr="//field[@name='purchase_request_id']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//span[hasclass('o_form_label')]" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//div[hasclass('o_address_format')]" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='buyer_id']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='partner_id']" position="after">
                    <field name="buyer_id" readonly="1" force_save="1"/>
                </xpath>
                <xpath expr="//field[@name='requestor_id']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='unit_id']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='group_id']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='attachment_ids']" position="after">
                    <field name="requestor_id" required="1" readonly="1" force_save="1"/>
                    <field name="unit_id" readonly="1" force_save="1"/>
                    <field name="group_id" readonly="1" force_save="1"/>
                    <field name="payment_term_id" readonly="state != 'draft'" />
                </xpath>

                <xpath expr="//field[@name='product_id']" position="attributes">
                    <attribute name="column_invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='product_category_id']" position="attributes">
                    <attribute name="column_invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='subtotal_currency']" position="attributes">
                    <attribute name="column_invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='currency_rate']" position="attributes">
                    <attribute name="column_invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='currency_id']" position="attributes">
                    <attribute name="column_invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='is_percentage']" position="attributes">
                    <attribute name="column_invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='is_manajemen_fee']" position="attributes">
                    <attribute name="column_invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='description']" position="attributes">
                    <attribute name="string">Bidding Description</attribute>
                    <attribute name="readonly">1</attribute>
                </xpath>

                <xpath expr="//field[@name='product_tmpl_id']" position="after">
                    <field name="product_variant_id" />
                </xpath>
                <xpath expr="//field[@name='price_subtotal']" position="after">
                    <field name="is_active" readonly="parent.state != 'draft'"/>
                </xpath>

                <xpath expr="//field[@name='state']" position="replace">
                    <field name="state" widget="statusbar" statusbar_visible="draft,submit,pending_approval,approve,done,cancel"/>
                </xpath>
                <xpath expr="//header" position="inside">
                    <button name="action_pending_approval" string="Convert to MPA" type="object" class="oe_highlight"
                        invisible="state not in ['submit'] or is_po_exists or is_pr_exists or flagging_rfq_used == True"/>
                    <button name="action_approval" string="Approval" type="object" class="oe_highlight"
                        invisible="1"/>
                    <button name="action_create_mpa" string="Submit MPA" type="object" class="oe_highlight"
                        invisible="state not in ['approve']"/>
                    <button name="action_to_draft" string="Set to Draft" type="object"
                        invisible="state not in ['cancel']"/>
                </xpath>   
                <!-- <xpath expr="//field[@name='order_line']" position="attributes">
                    <attribute name="readonly">0</attribute>
                </xpath> -->

                <xpath expr="//field[@name='product_tmpl_id']" position="attributes">
                    <attribute name="readonly">1</attribute>
                </xpath>
                <xpath expr="//field[@name='product_variant_id']" position="attributes">
                    <attribute name="readonly">1</attribute>
                </xpath>
                <xpath expr="//field[@name='description']" position="attributes">
                    <attribute name="readonly">1</attribute>
                </xpath>
                <xpath expr="//field[@name='product_qty']" position="attributes">
                    <attribute name="readonly">1</attribute>
                </xpath>
                <xpath expr="//field[@name='product_uom_id']" position="attributes">
                    <attribute name="readonly">1</attribute>
                </xpath>
                <xpath expr="//field[@name='price_unit']" position="attributes">
                    <attribute name="readonly">1</attribute>
                </xpath>
                <xpath expr="//field[@name='price_subtotal']" position="attributes">
                    <attribute name="column_invisible">1</attribute>
                </xpath>
            </field>
        </record>
        
    </data>
</odoo>