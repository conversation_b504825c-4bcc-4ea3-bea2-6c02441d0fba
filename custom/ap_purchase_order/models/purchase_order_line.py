# Part of Odoo. See LICENSE file for full copyright and licensing details.
from datetime import datetime, time
from dateutil.relativedelta import relativedelta
from pytz import UTC

from odoo import api, fields, models, _
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT, get_lang
from odoo.tools.float_utils import float_compare, float_round
from odoo.exceptions import UserError


class PurchaseOrderLine(models.Model):
    _inherit = "purchase.order.line"

    purchase_request_line_id = fields.Many2one(
        comodel_name="purchase.request.line", 
        string="PR Line")
    product_tmpl_id = fields.Many2one(
        comodel_name="product.template", 
        string="Product",
        help="The product template associated with this purchase request line.")
    product_category_id = fields.Many2one(
        comodel_name="product.category", 
        string="Product Category",
        help="The product category associated with this product.")
    new_currency_id = fields.Many2one(
        comodel_name="res.currency", 
        string="Currency",
        help="The currency in which the unit price is defined.")
    currency_rate = fields.Float(
        string='Rate')
    subtotal_currency = fields.Monetary(
        currency_field="new_currency_id",
        compute="_compute_subtotal_currency", store=True,
        help="Subtotal currency of Purchase Request Line",
    )
    latest_delivery_date = fields.Date(string='Latest Delivery Date', copy=False)
    is_manajemen_fee = fields.Boolean(string='Management Fee', copy=False,
                                    help="Centang jika termasuk manajemen fee.")
    description = fields.Text(
        string='Description', copy=False)

    is_company_currency = fields.Boolean(
        string='Is Company Currency',
        compute='_compute_is_company_currency',
        store=True
    )

    is_percentage = fields.Boolean(
        string="Is Percentage",
    )
    price_unit = fields.Float(
        string='Unit Price', required=True, digits='Product Price', readonly=False, store=True)
    domain_currency = fields.Binary(string='Currency Filter',compute='_compute_domain_currency')

    unit_price_source = fields.Float(string='Unit Price Source', compute='_compute_unit_price_source', store=True)

    @api.depends('order_id.agreement_id', 'order_id.bidding_rfq_id')
    def _compute_unit_price_source(self):
        for rec in self:
            unit_price_source = 0
            if rec.order_id.agreement_id:
                mpa = rec.order_id.agreement_id.line_ids.filtered(lambda x: x.product_variant_id == rec.product_id)
                if mpa:
                    unit_price_source = mpa[0].unit_price
            elif rec.order_id.bidding_rfq_id:
                rfq = rec.order_id.bidding_rfq_id.order_line.filtered(lambda x: x.product_variant_id == rec.product_id)
                if rfq:
                    unit_price_source = rfq[0].price_unit
                    
            rec.unit_price_source = unit_price_source

    @api.depends('currency_id')
    def _compute_domain_currency(self):
        for record in self:
            company_currency = self.env.company.currency_id
            budget_currency = self.env['res.currency'].search([('is_budget', '=', True)])
            record.domain_currency = [('id', 'in', [budget_currency.id,company_currency.id])]


    @api.onchange('product_uom')
    def _onchange_is_percentage(self):
        for rec in self:
            uom_name = (rec.product_uom.name or '').replace(' ', '').lower()
            rec.is_percentage = uom_name == 'percentage'
            if not rec.is_percentage:
                rec.is_manajemen_fee = False

    @api.depends('new_currency_id', 'company_currency_id')
    def _compute_is_company_currency(self):
        for rec in self:
            rec.is_company_currency = rec.new_currency_id == rec.company_currency_id

    @api.onchange('product_tmpl_id')
    def onchange_category_product(self):
        self.product_category_id = self.product_tmpl_id.categ_id.id

    @api.onchange('new_currency_id')
    def _onchange_currency_id(self):
        if self.new_currency_id:
            latest_rate = self.env['res.currency.rate'].search([
                ('currency_id', '=', self.new_currency_id.id)
            ], order='name desc', limit=1) 
            
            self.currency_rate = latest_rate.inverse_company_rate if latest_rate else 1

    @api.depends("price_unit","product_qty","new_currency_id")
    def _compute_subtotal_currency(self):
        for line in self:
            line.subtotal_currency = line.price_unit * line.product_qty

    def _prepare_base_line_for_taxes_computation(self):
        self.ensure_one()
        return self.env['account.tax']._prepare_base_line_for_taxes_computation(
            self,
            tax_ids=self.taxes_id,
            quantity=self.product_qty * self.currency_rate,
            partner_id=self.order_id.partner_id,
            currency_id=self.order_id.currency_id,
            rate=self.order_id.currency_rate,
        )

    def _compute_amount(self):
        res = super(PurchaseOrderLine, self)._compute_amount()
        for line in self:
            line.taxes_id = False
        return res
    
    # @api.onchange('is_percentage', 'product_uom', 'order_id.total_manajemen_fee', 'product_qty')
    # def _onchange_total_percentage(self):
    #     for line in self.order_id.order_line:
    #         if self.is_percentage:
    #             line.price_unit = 25
    #         else:
    #             line.price_unit = 50
        # if self.is_percentage and self.order_id.total_manajemen_fee:
        #     manajemen_fee = (self.product_qty / 100.0) * self.order_id.total_manajemen_fee
        #     if self.product_qty != 0:
        #         self.price_unit = manajemen_fee / self.product_qty

    @api.depends('product_qty', 'product_uom', 'company_id', 'order_id.partner_id')
    def _compute_price_unit_and_date_planned_and_name(self):
        res = super(PurchaseOrderLine, self)._compute_price_unit_and_date_planned_and_name()
        for line in self:
            if line.purchase_request_line_id:
                line.price_unit = line.purchase_request_line_id.price_unit
            elif not line.purchase_request_line_id:
                pr_line = line.order_id.purchase_request_id.line_ids.filtered(lambda x: x.product_id == line.product_id)
                if pr_line:
                    line.price_unit = pr_line[0].price_unit
            
            rfq_mpa_line = False
            if line.order_id.bidding_rfq_id:
                rfq_mpa_line = line.order_id.bidding_rfq_id.order_line.filtered(
                    lambda pt: pt.product_tmpl_id.id == line.product_tmpl_id.id and pt.is_active)
            if line.order_id.agreement_id:
                rfq_mpa_line = line.order_id.agreement_id.line_ids.filtered(
                    lambda pt: pt.product_tmpl_id.id == line.product_tmpl_id.id and pt.is_active)
            
            unit_price = False
            print('okkkkkkkk')
            if rfq_mpa_line and rfq_mpa_line[0]:
                if line.order_id.bidding_rfq_id:
                    unit_price = rfq_mpa_line[0].price_unit
                elif line.order_id.agreement_id:
                    unit_price = rfq_mpa_line[0].unit_price
                
                if unit_price:
                    print('kkkkkkkkkkkkkkkk', unit_price)
                    line.price_unit = unit_price

        return res

    # def write(self, vals):
    #     protected_fields = [
    #         'product_id', 'product_tmpl_id', 'description', 'new_currency_id', 
    #         'currency_rate', 'product_qty', 'product_uom', 'is_percentage', 
    #         'latest_delivery_date'
    #     ]
        
        # for field in protected_fields:
        #     if field in vals and self:
        #         # if any(line.purchase_request_line_id for line in self):
        #         raise UserError(_("You cannot modify column {} because this PO is from a Purchase Request.").format(field))
        
        # return super(PurchaseOrderLine, self).write(vals)
    



