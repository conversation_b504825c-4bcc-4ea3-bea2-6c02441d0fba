{
    'name': "Purchase Order Customization",
    'summary': "Modul ini mengkustomisasi dan memperluas fitur dari modul Purchase Order standar.",
    'description': """
Modul ini merupakan turunan (inherit) dari modul standar Purchase Order.

    """,
    'author': "Akuntplus",
    'category': 'Purchase',
    'version': '0.1',
    'license': 'LGPL-3',
    'depends': ['ap_bidding_agreement','purchase_fpip', 'ap_purchase_dpl', 'account_inherit', 'weha_bidding', 'ap_purchase_cancel_close'],
    'data': [
        'security/security.xml',
        'security/ir.model.access.csv',
        'data/cron.xml',
        'views/purchase_order_view.xml',
        'wizards/purchase_order_reassign_buyer_view.xml',
        'wizards/purchase_order_cancel_close_wizard.xml',
        'menu.xml',
    ],
    "installable": True,
    "auto_install": False,
    "application": True,
}
