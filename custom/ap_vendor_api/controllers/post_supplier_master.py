from odoo import http, SUPERUSER_ID
from odoo.http import request, Response
import json

class PostSupplierMaster(http.Controller):
    
    @http.route('/api/post-supplier-master', type='http', auth='public', methods=['POST'], csrf=False)
    def create_supplier_master(self, **kwargs):
        # try:
        headers = request.httprequest.headers
        token = headers.get('token')
        if not token:
            return Response(
                json.dumps({
                    'status': 'error',
                    'message': 'Required token!'
                }),
                content_type='application/json',
                status=500
            )
        
        elif token:
            token = str(headers.get('token', {}))
            user_id = request.env['res.users'].with_user(SUPERUSER_ID).search([('token', '=', token)], limit=1)
            if not user_id:
                return Response(
                    json.dumps({
                        'status': 'error',
                        'message': 'Invalid token!'
                    }),
                    content_type='application/json',
                    status=500
                )

        raw_body = request.httprequest.data
        if not raw_body:
            return Response(
                json.dumps({
                    'status': 'error',
                    'message': 'Required body for create supplier!'
                }),
                content_type='application/json',
                status=500
            )

        data = json.loads(raw_body)
        partner_id = data.get('partner_id') 
        name = data.get('name')
        if not name:
            return Response(
                json.dumps({
                    'status': 'error',
                    'message': 'Required Name!'
                }),
                content_type='application/json',
                status=404
            )
        
        street = data.get('street') or False
        street2 = data.get('street2') or False
        city = data.get('city') or False
        state_id = data.get('state_id') or False
        zip = data.get('zip') or False
        country_id = data.get('country_id') or False
        phone = data.get('phone') or False
        mobile = data.get('mobile') or False
        email = data.get('email') or False
        website = data.get('website') or False
        busines_category = data.get('busines_category') or False
        if not busines_category:
            return Response(
                json.dumps({
                    'status': 'error',
                    'message': 'Required field: busines_category!'
                }),
                content_type='application/json',
                status=404
            )
        partner_type = 'nonprocurement'
        supplier_type_id = data.get('supplier_type_id') or False
        if not supplier_type_id:
            return Response(
                json.dumps({
                    'status': 'error',
                    'message': 'Required field: supplier_type_id!'
                }),
                content_type='application/json',
                status=404
            )
        tax_document = data.get('tax_document') or 'ktp'
        tax_organization_type = data.get('tax_organization_type') or False
        if not tax_organization_type:
            return Response(
                json.dumps({
                    'status': 'error',
                    'message': 'Required field: tax_organization_type!'
                }),
                content_type='application/json',
                status=404
            )
        ktp_number = data.get('ktp_number') or False
        npwp_number = data.get('npwp_number') or False
        cor_number = data.get('cor_number') or False
        payment_term_id = data.get('payment_term_id') or False

        contacts = data.get('contacts')
        if not contacts:
            return Response(
                json.dumps({
                    'status': 'error',
                    'message': 'Required field: contacts!'
                }),
                content_type='application/json',
                status=404
            )
        
        banks = data.get('bank_ids')
        if not banks:
            return Response(
                json.dumps({
                    'status': 'error',
                    'message': 'Required field: banks!'
                }),
                content_type='application/json',
                status=404
            )

        vals = {
            'is_company': True,
            'supplier_rank': 1,
            'is_vendor': True,
            'name': name,
            'street': street,
            'street2': street2,
            'city': city,
            'state_id': state_id,
            'zip': zip,
            'country_id': country_id,
            'phone': phone,
            'mobile': mobile,
            'email': email,
            'website': website,
            'busines_category': busines_category,
            'partner_type': partner_type,
            'supplier_type_id': supplier_type_id,
            'tax_document': tax_document,
            'tax_organization_type': tax_organization_type,
            'ktp_number': ktp_number,
            'npwp_number': npwp_number,
            'cor_number': cor_number,
            'payment_term_id': payment_term_id,
            'state': 'draft'
        }
        
        # Jika partner_id ada, update data, kalau tidak, buat baru
        partner_model = request.env['res.partner'].with_user(user_id).sudo()
        if partner_id:
            partner = partner_model.browse(partner_id)
            if not partner.exists():
                return Response(
                    json.dumps({
                        'status': 'error',
                        'message': 'Invalid partner_id!'
                    }),
                    content_type='application/json',
                    status=404
                )
            partner.write(vals)
        else:
            partner = partner_model.create(vals)
        
        partner._onchange_tax_type()
        
        if partner_id:
            partner.child_ids.unlink()
            partner.bank_ids.unlink()
        
        # partner = request.env['res.partner'].with_user(user_id).sudo().create(vals)
        # partner._onchange_tax_type()

        for contact in contacts:
            type_contact = contact.get('type') or False
            first_name = contact.get('first_name') or False
            middle_name = contact.get('middle_name') or False
            last_name = contact.get('last_name') or False
            contact_street = contact.get('contact_street') or False
            contact_street2 = contact.get('contact_street2') or False
            contact_city = contact.get('contact_city') or False
            contact_state_id = contact.get('contact_state_id') or False
            contact_zip = contact.get('contact_zip') or False
            contact_country_id = contact.get('contact_country_id') or False
            contact_phone = contact.get('contact_phone') or False
            contact_mobile = contact.get('contact_mobile') or False
            contact_email = contact.get('contact_email') or False

            vals_contact = {
                'parent_id': partner.id,
                'type': type_contact,
                'name': first_name,
                'middle_name': middle_name,
                'last_name': last_name,
                'street': contact_street,
                'street2': contact_street2,
                'city': contact_city,
                'state_id': contact_state_id,
                'zip': contact_zip,
                'country_id': contact_country_id,
                'phone': contact_phone,
                'mobile': contact_mobile,
                'email': contact_email
            }
            partner_contract = request.env['res.partner'].with_user(user_id).sudo().create(vals_contact)

        for bank in banks:
            acc_number = bank.get('acc_number')
            bank_id = bank.get('bank_id')
            partner_id = partner.id
            branch_name = bank.get('branch_name')
            branch_code = bank.get('branch_code')
            swift_code = bank.get('swift_code')
            account_number = bank.get('account_number')
            tipe_rekening_bank = bank.get('tipe_rekening_bank')
            currency_id = bank.get('currency_id')

            vals_bank = {
                "acc_number": acc_number,
                "bank_id": bank_id,
                "partner_id": partner_id,
                "branch_name": branch_name,
                "branch_code": branch_code,
                "swift_code": swift_code,
                "account_number": account_number,
                "tipe_rekening_bank": tipe_rekening_bank,
                "currency_id": currency_id
            }
            
            #pengecekan account_number
            acc_number = bank.get('acc_number', '').strip()
            if not acc_number.isdigit():
                return Response(
                    json.dumps({
                        'status': 'error',
                        'message': f'acc_number "{acc_number}" must only contain digits.'
                    }),
                    content_type='application/json',
                    status=400
                )

            partner_bank = request.env['res.partner.bank'].with_user(user_id).sudo().create(vals_bank)
        
        return Response(
            json.dumps({
                'status': 'success',
                'message': 'Record created successfully',
                'partner_id': partner.id
            }),
            content_type='application/json',
            status=200
        )
            
        # except Exception as e:
        #     return Response(
        #         json.dumps({
        #             'status': 'error',
        #             'message': str(e)
        #         }),
        #         content_type='application/json',
        #         status=500
        #     )