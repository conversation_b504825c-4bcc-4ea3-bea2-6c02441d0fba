from odoo import models, fields, api, _
from odoo.exceptions import UserError

class VendorPortalInviteWizard(models.TransientModel):
    _inherit = 'partner.vp'

    mail_from = fields.Char(string='Mail From', default=lambda self: self.env.company.email_formatted)
    email_sender = fields.Char(string='Email Sender', default=lambda self: self.env.user.email_formatted)
    name_sender = fields.Char(string='Name Sender', default=lambda self: self.env.user.employee_id.name or self.env.user.name)
    contact_phone = fields.Char(string='Contact Phone')
    company_name = fields.Char(string='Company Name', default=lambda self: self.env.company.name)
    company_id = fields.Many2one(comodel_name='res.company', string='Company ID', default=lambda self: self.env.company)

    def _get_email_to_vp(self):
        return 'ap_partner_mail.email_to_vendor'

    def send_email_to_vp(self, email_to, activation_link, template_xml_id):
        """Send an email to the vendor using a predefined template."""
        mail_from = self.env.company.email_formatted or self.env.user.email_formatted

        try:
            template = self.env.ref(template_xml_id)
        except ValueError:
            raise UserError(_('Template email "%s" tidak ditemukan.') % template_xml_id)

        if not email_to:
            raise UserError(_('Alamat email tujuan tidak ditemukan.'))

        if template:
            template.with_context(
                mail_from=mail_from,
                mail_to=email_to,
                activation_link=activation_link,
                company=self.env.company.name,
                email_sender=self.env.user.email_formatted,
                name_sender=self.env.user.employee_id.name or self.env.user.name
            ).send_mail(self.id, force_send=True)

    def action_vp_email(self, partner_id):
        self.ensure_one()
        if not self.contact_email:
            raise UserError(_('Silakan isi Contact Email terlebih dahulu.'))

        email_to = "%s,%s" % (self.contact_email, self.partner_id.email)
        # activation_link = "{}create-password?email={}&partner_id={}".format(
        #     self.company_id.vp_website if self.company_id.vp_website.endswith('/') else self.company_id.vp_website + '/',
        #     self.contact_email,
        #     partner_id.id
        # )
        activation_link = "{}create-password?email={}&partner_pic_id={}&partner_id={}".format(
            self.env.company.vp_website if self.env.company.vp_website.endswith('/') else self.env.company.vp_website + '/',
            self.contact_email,
            partner_id.id,
            partner_id.parent_id.id
        )
        self.send_email_to_vp(email_to, activation_link, self._get_email_to_vp())

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Email Terkirim'),
                'message': _('Email berhasil dikirim ke %s.') % self.contact_email,
                'type': 'success',
                'sticky': False,
            }
        }

    def action_generate(self):
        res = super(VendorPortalInviteWizard, self).action_generate()
        """Update partner, create contact, and send email to vendor."""
        self.ensure_one()

        partner = self.partner_id

        # Update data partner
        partner.write({
            'name': self.name,
            'tax_organization_type': self.tax_organization_type.id,
            'busines_category': self.busines_category.id,
        })

        # Create contact for partner
        partner_id = self.env['res.partner'].create({
            'name': self.contact_name,
            'email': self.contact_email,
            'phone': self.contact_phone,
            'parent_id': partner.id,
            'street': partner.street,
            'street2': partner.street2,
            'city': partner.city,
            'state_id': partner.state_id.id,
            'zip': partner.zip,
            'country_id': partner.country_id.id,
            'lang': partner.lang,
            'user_id': partner.user_id.id,
            'type': 'contact',
        })

        # Kirim email
        self.action_vp_email(partner_id)

        return res
