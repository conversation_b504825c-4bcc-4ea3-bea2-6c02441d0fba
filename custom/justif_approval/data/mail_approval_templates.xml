<odoo>
    <data>
        <record id="mail_account_justification_approval" model="mail.template">
            <field name="name">Justification: Request Approval</field>
            <field name="model_id" ref="account_budget_justif.model_account_justification"/>
            <field name="subject">Justification (Ref {{ object.name or 'n/a' }})</field>
            <field name="email_to">{{ ctx['mail_to'] }}</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            Justification Approval:<br/><br/>
                            <table width="100%">
                                <tr>
                                    <td>Number</td>
                                    <td>: <t t-out="object.name or ''">-</t></td>
                                </tr>
                                <tr>
                                    <td><strong>Total Budget Reserved</strong></td>
                                    <td><strong>: <t t-out="'{:,.0f}'.format(object.budget_reserved) or ''">-</t></strong></td>
                                </tr>
                            </table>
                        </div>
                    </p>
                    <p style="margin: 5px 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            <table width="100%">
                                <tr>
                                    <td>Procurement Type</td>
                                    <td>Description</td>
                                    <td>Amount</td>
                                </tr>

                                <t t-foreach="object.line_ids" t-as="line">
                                    <tr>
                                        <td><t t-out="line.procurement_type_id.name"/></td>
                                        <td><t t-out="line.description"/></td>
                                        <td><t t-out="'{:,.0f}'.format(line.amount_currency)"/></td>
                                    </tr>
                                </t>
                            </table>
                        </div>
                    </p>
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <a t-att-href="ctx['link']" >Click here to see details</a> 
                    </p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>

        <record id="mail_account_justification_info" model="mail.template">
            <field name="name">Justification: Info</field>
            <field name="model_id" ref="account_budget_justif.model_account_justification"/>
            <field name="subject">Justification (Ref {{object.name or 'n/a' }})</field>
            <field name="email_to">{{ ctx['mail_to'] }}</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            <t t-out="ctx['message']" />
                        </div>
                    </p>
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <a t-att-href="ctx['link']" >Click here to see details</a> 
                    </p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>
    </data>
</odoo>
