from odoo import api, fields, models


class AccountJustificationApproval(models.Model):
    _name = 'account.justification.approval'
    _inherit = 'approval.history.mixin'
    _description = 'Account Justification Approval'

    justification_id = fields.Many2one('account.justification', 'Account Justification',
                               ondelete='cascade')
    

class AccountJustificationApprovalDetail(models.Model):
    _name = 'account.justification.approval.detail'
    _inherit = 'approval.history.detail.mixin'
    _description = 'Account Justification Approval'

    justification_id = fields.Many2one('account.justification', 'Account Justification',
                               ondelete='cascade')
    

class AccountJustificationApproval(models.Model):
    _name = 'account.justification.reassign'
    _description = 'Account Justification Reassign'
    

    justification_id = fields.Many2one('account.justification', 'Account Justification',
                               ondelete='cascade')
    from_employee_id = fields.Many2one('hr.employee', string='From Employee')
    to_employee_id = fields.Many2one('hr.employee', string='To Employee') 
