# -*- coding: utf-8 -*-

from odoo import models, fields, api


class vp_account_master(models.Model):
    _name = 'vp.account.master'
    _description = 'vp.account.master'

    name = fields.Char(string="Name")
    vp_email = fields.Char(string="Email", store=True)
    vp_password = fields.Char(string="Password")
    partner_id = fields.Many2one('res.partner', string='Partner')
    parent_id = fields.Many2one('res.partner', string='Parent Partner')
    
    @api.model
    def create(self, vals):        
        return super(vp_account_master, self).create(vals)
    
    def write(self, vals):
        return super(vp_account_master, self).write(vals)