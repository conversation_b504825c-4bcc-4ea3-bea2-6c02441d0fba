# -*- coding: utf-8 -*-

from odoo import models, fields, api


class res_partner(models.Model):
    _inherit = 'res.partner'
    _description = 'res.partner'

    # is_selfservice = fields.Boolean(string="Is Self-Service")
    vp_account_master_ids = fields.One2many(
        'vp.account.master',
        'partner_id',
        string='VP Account Master'
    )

    def write(self, vals):
        res = super(res_partner, self).write(vals)

        if "is_bidding" in vals:
            if vals.get("is_bidding", True):
                vals = {
                    'name': self._origin.name,
                    'vp_email': self._origin.email,
                    'partner_id': self._origin.id,
                    'parent_id': self._origin.parent_id.id
                }
                self.env['vp.account.master'].create(vals)
            elif not vals.get("is_bidding", True):
                pass
        return res