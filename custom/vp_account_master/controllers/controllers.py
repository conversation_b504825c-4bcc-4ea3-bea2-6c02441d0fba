# -*- coding: utf-8 -*-
# from odoo import http


# class VpAccountMaster(http.Controller):
#     @http.route('/vp_account_master/vp_account_master', auth='public')
#     def index(self, **kw):
#         return "Hello, world"

#     @http.route('/vp_account_master/vp_account_master/objects', auth='public')
#     def list(self, **kw):
#         return http.request.render('vp_account_master.listing', {
#             'root': '/vp_account_master/vp_account_master',
#             'objects': http.request.env['vp_account_master.vp_account_master'].search([]),
#         })

#     @http.route('/vp_account_master/vp_account_master/objects/<model("vp_account_master.vp_account_master"):obj>', auth='public')
#     def object(self, obj, **kw):
#         return http.request.render('vp_account_master.object', {
#             'object': obj
#         })

