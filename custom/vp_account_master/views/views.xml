<odoo>
  <data>
    <!-- explicit list view definition -->
<!--
    <record model="ir.ui.view" id="vp_account_master.list">
      <field name="name">vp_account_master list</field>
      <field name="model">vp_account_master.vp_account_master</field>
      <field name="arch" type="xml">
        <list>
          <field name="name"/>
          <field name="value"/>
          <field name="value2"/>
        </list>
      </field>
    </record>
-->

    <!-- actions opening views on models -->
<!--
    <record model="ir.actions.act_window" id="vp_account_master.action_window">
      <field name="name">vp_account_master window</field>
      <field name="res_model">vp_account_master.vp_account_master</field>
      <field name="view_mode">list,form</field>
    </record>
-->

    <!-- server action to the one above -->
<!--
    <record model="ir.actions.server" id="vp_account_master.action_server">
      <field name="name">vp_account_master server</field>
      <field name="model_id" ref="model_vp_account_master_vp_account_master"/>
      <field name="state">code</field>
      <field name="code">
        action = {
          "type": "ir.actions.act_window",
          "view_mode": "list,form",
          "res_model": model._name,
        }
      </field>
    </record>
-->

    <!-- Top menu item -->
<!--
    <menuitem name="vp_account_master" id="vp_account_master.menu_root"/>
-->
    <!-- menu categories -->
<!--
    <menuitem name="Menu 1" id="vp_account_master.menu_1" parent="vp_account_master.menu_root"/>
    <menuitem name="Menu 2" id="vp_account_master.menu_2" parent="vp_account_master.menu_root"/>
-->
    <!-- actions -->
<!--
    <menuitem name="List" id="vp_account_master.menu_1_list" parent="vp_account_master.menu_1"
              action="vp_account_master.action_window"/>
    <menuitem name="Server to list" id="vp_account_master" parent="vp_account_master.menu_2"
              action="vp_account_master.action_server"/>
-->
  </data>
</odoo>
