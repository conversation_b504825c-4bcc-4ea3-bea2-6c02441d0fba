<?xml version="1.0" encoding="utf-8"?>
<odoo>    
	<record id="view_partner_form_inherit_vp_account_master" model="ir.ui.view">
		<field name="name">res.partner.form.inherit.vp.account.master</field>
		<field name="model">res.partner</field>
		<field name="inherit_id" ref="base.view_partner_form"/>
		<field name="arch" type="xml">

			<!-- <xpath expr="//field[@name='tags']" position="after">
			    <field name="is_selfservice"/>
			</xpath>             -->
              <xpath expr="//page[@name='accounting']" position="after">
                <page string="VP Account" name="vp_account_master">
                    <field name="vp_account_master_ids" domain="[('parent_id', '=', id)]" context="{'default_parent_id': id}">
                        <list editable="bottom" create="true" string="VP Account List" >
                            <field name="name" column_invisible='1' />
                            <field name="vp_email"/>
                            <field name="vp_password" password="True"/>
                            <field name="partner_id" column_invisible='1' />
                            <field name="parent_id" column_invisible='1' />
                        </list>
                    </field>
                </page>
            </xpath>
		</field>
	</record>
</odoo>