# -*- coding: utf-8 -*-
{
    'name': "inherit_payment_plan",

    'summary': "Short (1 phrase/line) summary of the module's purpose",

    'description': """
Long description of module's purpose
    """,

    'author': "My Company",
    'website': "https://www.yourcompany.com",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/15.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': 'Uncategorized',
    'version': '0.1',

    # any module necessary for this one to work correctly
    'depends': ['base', 'account_payment_plan', 'ap_mt100_config', 'partner_linkaja'],

    # always loaded
    'data': [
        'security/ir.model.access.csv',
        'views/account_payment_plan_view.xml',
        'views/account_pay_groups_view.xml',
        'views/account_payment_view.xml',
    ],
    # only loaded in demonstration mode
    'demo': [
        'demo/demo.xml',
    ],
}

