<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data>
    
        <record id="inherit_view_account_payment_form_id_inherit_account" model="ir.ui.view">
            <field name="name">account.payment.view.form.inherit</field>
            <field name="model">account.payment</field>
            <field name="inherit_id" ref="account.view_account_payment_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='state']" position="replace">
                    <field name="state" widget="statusbar" statusbar_visible="draft,paid"/>
                    <field name="is_flag_pp" invisible="1"/>
<!--                    <field name="state" widget="statusbar" statusbar_visible="draft,in_process,paid" invisible="is_plan_failed != False"/>-->
<!--                    <field name="state" widget="statusbar" statusbar_visible="draft,plan_failed,paid" invisible="is_plan_failed != True"/>-->
                </xpath>
                <xpath expr="//field[@name='payment_type']" position="attributes">
                    <attribute name="readonly">state != 'draft' or payment_plan_id</attribute>
                </xpath>
                <button name="action_validate" position="attributes">
                    <attribute name="invisible">state != 'in_process' or is_flag_pp</attribute>
                </button>
            </field>
        </record>
    
    </data>
    

</odoo>
