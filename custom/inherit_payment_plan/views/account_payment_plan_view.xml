<odoo>
  <data>

    <record id="inherit_view_payment_plan_form_id_inherit_account_payment_plan" model="ir.ui.view">
      <field name="name">account.payment.plan.view.form.inherit</field>
      <field name="model">account.payment.plan</field>
      <field name="inherit_id" ref="account_payment_plan.view_payment_plan_form"/>
      <field name="arch" type="xml">
        <field name="current_user_id" position="before">
          <field name="requester_user_by_id" />
        </field>
        <field name="description" position="after">
          <field name="pay_groups_id" required="1"/>
          <field name="partner_bank_id" domain="[('partner_id', 'in', partner_ids)]" invisible="1"/>
          <field name="currency_id" />
        </field>
        <xpath expr="//field[@name='state']" position="attributes">
          <attribute name="statusbar_visible" >draft,approved</attribute>
        </xpath>
        <xpath expr="//button[@name='action_to_approve']" position="attributes">
          <attribute name="invisible" >1</attribute>
        </xpath>
        <xpath expr="//button[@name='action_reject']" position="attributes">
          <attribute name="invisible" >1</attribute>
        </xpath>
        <xpath expr="//button[@name='action_approve']" position="attributes">
          <attribute name="invisible" >state != 'draft'</attribute>
        </xpath>
        <xpath expr="//button[@name='action_draft']" position="attributes">
          <attribute name="invisible" >state != 'approved'</attribute>
        </xpath>
        <xpath expr="//field[@name='line_ids']/list/field[@name='currency_id']" position="before">
          <!-- <field name="pay_through_date" /> -->
          <!-- <field name="date_basis" /> -->
          <field name="business_units" />
          <!-- <field name="legal_entity" /> -->
          <field name="payment_method_line_id" />
          <!-- <field name="supplier_or_party" /> -->
        </xpath>
        <xpath expr="//field[@name='line_ids']/form/sheet/group/field[@name='currency_id']" position="before">
          <!-- <field name="pay_through_date" /> -->
          <!-- <field name="date_basis" /> -->
          <!-- <field name="pay_groups" /> -->
          <field name="business_units" />
          <!-- <field name="legal_entity" /> -->
          <field name="payment_method_line_id" />
          <!-- <field name="supplier_or_party" /> -->
        </xpath>
        <xpath expr="//field[@name='line_ids']/form/sheet/group/field[@name='currency_id']" position="attributes">
          <attribute name="invisible">1</attribute>
        </xpath>
        <xpath expr="//field[@name='partner_ids']" position="attributes">
          <attribute name="string" >Supplier or Party</attribute>
        </xpath>
        <xpath expr="//field[@name='selection']" position="attributes">
          <attribute name="string" >Search Supplier or Party</attribute>
        </xpath>
        <xpath expr="//field[@name='line_ids']/form/sheet/group/field[@name='amount_to_pay']" position="attributes">
          <attribute name="required" >0</attribute>
        </xpath>
        <xpath expr="//field[@name='line_ids']/form/sheet/group/field[@name='payment_date']" position="attributes">
          <attribute name="required" >1</attribute>
        </xpath>
        <xpath expr="//notebook" position="inside">
          <page name="approval_payment_plan" string="Approval">
            <field name="approval_payment_plan_ids" >
              <list editable="bottom">
                <field name="sequence" />
                <field name="approval_name" />
                <field name="approval_position" />
                <field name="approval_status" />
                <field name="approval_date" />
                <field name="approval_note" />
                <field name="reassign_to" />
                <field name="approval_status_dua" />
                <field name="approval_date_dua" />
                <field name="approval_note_dua" />
                <field name="approval_note_dua" />
              </list>
            </field>
          </page>
        </xpath>
        <xpath expr="//field[@name='date']" position="after">
          <field name="pay_through_days"/>
          <field name="pay_through_date"/>
        </xpath>
        <xpath expr="//field[@name='line_ids']/list/field[@name='payment_due_date']" position="attributes">
          <attribute name="column_invisible">1</attribute>
        </xpath>
        <xpath expr="//field[@name='line_ids']/form/sheet/group/field[@name='payment_due_date']" position="attributes">
          <attribute name="invisible">1</attribute>
        </xpath>
        <xpath expr="//field[@name='line_ids']/form/sheet/group/field[@name='remaining_amount']" position="attributes">
          <attribute name="readonly">1</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//field[@name='line_ids']/form/sheet/group/field[@name='amount_residual']" position="attributes">
          <attribute name="readonly">1</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//button[@name='action_reject']" position="after">
          <button name="push_manual_sftp" string="Push Manual SFTP" type="object" invisible="state not in ('error')"/>
          <button name="action_cancel" string="Cancel" type="object" invisible="state not in ('error')"/>
        </xpath>
        <xpath expr="//field[@name='line_ids']/list/field[@name='payment_method_line_id']" position="after">
          <field name="partner_bank_id"/>
        </xpath>
        <xpath expr="//field[@name='line_ids']/form/sheet/group/field[@name='payment_method_line_id']" position="after">
          <field name="partner_bank_id" domain="[('partner_id', '=', partner_id)]"/>
        </xpath>
        <xpath expr="//field[@name='line_ids']/form/sheet/group/field[@name='due_date']" position="attributes">
          <attribute name="string">Date Basic</attribute>
        </xpath>
        <xpath expr="//field[@name='line_ids']/list/field[@name='due_date']" position="attributes">
          <attribute name="string">Date Basic</attribute>
        </xpath>
        <xpath expr="//field[@name='line_ids']/form/sheet/group/field[@name='journal_id']" position="attributes">
          <attribute name="readonly">1</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//field[@name='partner_ids']" position="attributes">
          <attribute name="domain">[('parent_id', '=', False), ('vendor_state', '=', 'spendauthorized')]</attribute>
        </xpath>
          <div name="button_box" position="inside">
            <button class="oe_stat_button" name="get_journal_entries" string='Journal Entries' type="object" icon="fa-bars"/>
          </div>
      </field>
    </record>

    <record id="account_payment_plan.payment_plan_menu" model="ir.ui.menu">
      <field name="name">Payment Process Request</field>
    </record>

    <record id="account_payment_plan_line_view_list" model="ir.ui.view">
      <field name="name">account.payment.plan.line.view.list</field>
      <field name="model">account.payment.plan.line</field>
      <field name="arch" type="xml">
        <list string="Account Payment Plan Line" editable="bottom">
          <field name="move_id" optional="show"/>
          <field name="bill_date" optional="show"/>
          <field name="due_date" optional="show"/>
          <field name="partner_id" optional="show"/>
          <field name="payment_reference" optional="show"/>
          <field name="ref" optional="show"/>
          <field name="currency_id" optional="show"/>
          <field name="amount_residual" optional="show"/>
          <field name="amount_to_pay" optional="show"/>
          <field name="remaining_amount" optional="show"/>
          <field name="payment_due_date" optional="show"/>
          <field name="payment_date" optional="show"/>
<!--                                    <field name="payment_group" optional="show"/>-->
          <field name="is_process" optional="show"/>
          <field name="note" optional="show"/>
          <field name="cancelled" invisible="1"/>
          <field name="is_editable" invisible="1"/>
          <field name="state" optional="hide"/>
        </list>
      </field>
    </record>
    
  </data>
</odoo>
