# -*- coding: utf-8 -*-
# from odoo import http


# class InheritPaymentPlan(http.Controller):
#     @http.route('/inherit_payment_plan/inherit_payment_plan', auth='public')
#     def index(self, **kw):
#         return "Hello, world"

#     @http.route('/inherit_payment_plan/inherit_payment_plan/objects', auth='public')
#     def list(self, **kw):
#         return http.request.render('inherit_payment_plan.listing', {
#             'root': '/inherit_payment_plan/inherit_payment_plan',
#             'objects': http.request.env['inherit_payment_plan.inherit_payment_plan'].search([]),
#         })

#     @http.route('/inherit_payment_plan/inherit_payment_plan/objects/<model("inherit_payment_plan.inherit_payment_plan"):obj>', auth='public')
#     def object(self, obj, **kw):
#         return http.request.render('inherit_payment_plan.object', {
#             'object': obj
#         })

