from odoo import fields, api, models


class AccountMove(models.Model):
    _inherit = 'account.move'

    payment_plan_id = fields.Many2one('account.payment.plan', 'Payment Plan')

    # def _compute_payment_state(self):
    #     for rec in self:
    #         if (self._context.get('pass_validation') and rec.payment_state == 'paid') or rec.move_type == 'entry':
    #             pass
    #         else:
    #             res = super(AccountMove, self)._compute_payment_state()
    #             return res
