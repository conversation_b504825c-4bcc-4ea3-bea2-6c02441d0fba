from odoo import _, api, fields, models


class AccountPayGroups(models.Model):
    _name = "account.pay.groups"
    _description = "Account Pay Groups"
    _rec_name = "pay_groups"

    pay_groups = fields.Selection([
        ('in_house_bni', 'In House (BNI)'),
        ('others', 'Others'),
        ('non_mt100', 'Non MT100'),
        ('digi_pos_bni', 'DigiPos BNI'),
        ('digi_pos_others', 'DigiPos Others'),
        ('advertising_in_house_mandiri', 'Advertising In House (Mandiri)'),
        ('advertising_others_mandiri', 'Advertising Others (Mandiri)')
    ], string='Pay Groups', required=True)
    account_ids = fields.Many2many('account.account', string='Account')
    flag = fields.Selection([
        ('In House', 'In House (BNI)'),
        ('Others', 'Others'),
        ('DigiPos BNI', 'DigiPos BNI'),
        ('DigiPos Others', 'DigiPos Others'),
        ('Advertising Mandiri', 'Advertising In House (Mandiri)'),
        ('Advertising Others (Mandiri)', 'Advertising Others (Mandiri)')
    ], string='Flag')

