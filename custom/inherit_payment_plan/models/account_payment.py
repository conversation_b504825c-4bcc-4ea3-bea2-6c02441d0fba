from odoo import fields, api, models


class AccountPayment(models.Model):
    _inherit = 'account.payment'

    is_plan_faid = fields.Boolean('Is PLan Faid', default=False)
    is_plan_failed = fields.Boolean('Is PLan Failed', default=False)
    state = fields.Selection(selection_add = [
        ('draft', 'Draft'),
        ('plan_failed', 'Plain Failed')
    ], 
    ondelete={'plan_failed': 'set default'})
    is_flag_pp = fields.Boolean('Is Flag Payment Plan', compute='_compute_is_flag_pp', store=True)

    @api.constrains('payment_method_line_id')
    def _check_payment_method_line_id(self):
        pass

    @api.depends('payment_plan_id')
    def _compute_is_flag_pp(self):
        for rec in self:
            is_flag_pp = False
            if rec.payment_plan_id and rec.payment_plan_id.pay_groups_id and rec.payment_plan_id.pay_groups_id.flag:
                is_flag_pp = True
            rec.is_flag_pp = is_flag_pp