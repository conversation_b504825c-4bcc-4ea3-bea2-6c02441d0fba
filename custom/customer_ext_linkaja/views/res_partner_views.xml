<?xml version="1.0" encoding="utf-8"?>
<odoo>
    
    <!-- View res.partner tree -->
    <record id="view_partner_tree_customer" model="ir.ui.view">
        <field name="name">res.partner.list</field>
        <field name="model">res.partner</field>
        <field eval="8" name="priority"/>
        <field name="arch" type="xml">
            <list string="Contacts" sample="1" multi_edit="1">
                <field name="complete_name" string="Name"/>
                <field name="phone" class="o_force_ltr" optional="show"/>
                <field name="mobile" optional="hide"/>
                <field name="email" optional="show"/>
                <field name="user_id" optional="show" widget="many2one_avatar_user" domain="[('share', '=', False)]"/>
                <field name="street" optional="hide"/>
                <field name="city" optional="show"/>
                <field name="state_id" optional="hide" readonly="1"/>
                <field name="country_id" optional="show" readonly="1"/>
                <field name="state" />
                <field name="vat" optional="hide" readonly="1"/>
                <field name="category_id" optional="hide" widget="many2many_tags" options="{'color_field': 'color'}"/>
                <field name="company_id" groups="base.group_multi_company" readonly="1"/>
            </list>
        </field>
    </record>

    <!-- View res.partner form -->
    <record id="view_res_partner_form_inherit_customer" model="ir.ui.view">
        <field name="name">view.res.partner.form.inherit.customer</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='company_type']" position="before">
                <div class="o_row">
                    <label for="is_customer" string="Customer"/>
                    <field name="is_customer" string="Is Customer?"/>
                </div>
            </xpath>
            <xpath expr="//sheet" position="before">
                <header>
                    <field name="state" invisible="1" force_save='1' />
                    <button string="Submit" name="button_action_submit" type="object" class="oe_highlight" invisible="not context.get('customer_view') or (context.get('customer_view') and state != 'draft')"/>
                    <button string="Validate Tax" name="button_action_validate_tax" type="object" class="oe_highlight" groups="customer_ext_linkaja.group_user_tax" invisible="not context.get('customer_view') or (context.get('customer_view') and state != 'validate_tax')"/>
                    <button string="Assign Coa" name="button_action_assign_coa" type="object" class="oe_highlight" groups="customer_ext_linkaja.group_user_accounting" invisible="not context.get('customer_view') or (context.get('customer_view') and state != 'assign_coa')"/>
                    <button string="Edit Validate Tax" name="button_edit_validate_tax" type="object" class="oe_highlight" groups="customer_ext_linkaja.group_user_tax" invisible="not context.get('customer_view') or (context.get('customer_view') and state != 'active')"/>
                    <button string="Edit Assign Coa" name="button_edit_assign_coa" type="object" class="oe_highlight" groups="customer_ext_linkaja.group_user_accounting" invisible="not context.get('customer_view') or (context.get('customer_view') and state != 'active')"/>
                    <!-- <button string="Set Active" name="action_active" type="object" class="oe_highlight" invisible="state != 'assign_coa'"/> -->
                    <field name="state" force_save='1' widget="statusbar" invisible="not context.get('customer_view')"/>
                </header>
            </xpath>
            <xpath expr="//field[@name='category_id']" position="after">
                <field name="id_tku" readonly='1' force_save='1' invisible="not context.get('customer_view')" />
                <label for="id_pembeli" invisible="not context.get('customer_view')" />
                <div>
                    <field name="id_pembeli" class="oe_inline" invisible="not context.get('customer_view')" />
                    <field name="l10n_id_nik" invisible="not context.get('customer_view') or (context.get('customer_view') and id_pembeli != 'nik')" class="oe_inline" nolabel="1" required="is_customer == True and id_pembeli == 'nik'"/>
                    <field name="id_pembeli_passport" invisible="not context.get('customer_view') or (context.get('customer_view') and id_pembeli != 'passport')" class="oe_inline" nolabel="1" required="is_customer == True and id_pembeli == 'passport'"/>
                    <field name="id_pembeli_others" invisible="not context.get('customer_view') or (context.get('customer_view') and id_pembeli != 'others')" class="oe_inline" nolabel="1" required="is_customer == True and id_pembeli == 'others'"/>
                </div>
            </xpath>
        </field>
    </record>

    <!-- accounting_entries -->

    <record id="view_partner_property_form_inherit_customer_ext_linkaja" model="ir.ui.view">
        <field name="name">res.partner.property.view.form.inherit.customer.ext</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="account.view_partner_property_form"/>
        <field name="arch" type="xml">

            <xpath expr="//group[@name='accounting_entries']" position="inside">
                <field name="is_customer" invisible="1" />
                <field name="state" invisible="1" force_save='1' />
                <field name="ar_credit_note_id" invisible="not context.get('customer_view')" required="is_customer == True"/>
                <field name="ar_other_id" invisible="not context.get('customer_view')" required="is_customer == True" />
                <field name="employee_submit_id" invisible='1' />
            </xpath>
            
        </field>
    </record>

    <record id="account.res_partner_action_customer" model="ir.actions.act_window">
        <field name="context">{'search_default_customer': 1,'res_partner_search_mode': 'customer', 'default_is_company': True, 'default_customer_rank': 1, 'default_is_customer': True, 'customer_view': True}</field>
        <field name="domain">[('is_customer', '=', True)]</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'kanban', 'view_id': ref('base.res_partner_kanban_view')}),
            (0, 0, {'view_mode': 'list', 'view_id': ref('view_partner_tree_customer')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('base.view_partner_form')})]"/>
    </record>

    <record id="res_partner_view_search_customer" model="ir.ui.view">
        <field name="name">res.partner.search.inherit.customer</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="account.res_partner_view_search"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='customer']" position="attributes">
                <attribute name="domain">[('is_customer', '=', True)]</attribute>
            </xpath>
        </field>
    </record>

</odoo>