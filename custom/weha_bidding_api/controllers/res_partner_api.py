# -*- coding: utf-8 -*-
from odoo import http, SUPERUSER_ID
from odoo.http import request, Response
import json
from datetime import datetime
import pytz
import base64
import logging

_logger = logging.getLogger(__name__)

class WehaResPartnerApi(http.Controller):


    def create_attachment(self, user_id, resid, file_name, file_content, mimetype='application/octet-stream'):
        """Create attachment for this record."""
        attachment = request.env['ir.attachment'].with_user(user_id).sudo().create({
            'name': file_name,
            'type': 'binary',
            'datas': file_content,  # harus base64 encoded
            'res_model': "res.partner",
            'res_id': resid,
            'mimetype': mimetype,
        })
        return attachment


    @http.route('/api/v1/post_attachment_res_partner', auth='public', methods=['POST'], csrf=False)
    def create_attachment_res_partner(self, **post):
        try:
            # Validate token
            headers = request.httprequest.headers
            token = str(headers.get('token', ''))
            if not token:
                return Response(
                    json.dumps({
                        'status': 'error',
                        'message': 'Required token!'
                    }),
                    content_type='application/json',
                    status=500
                )
            user_id = request.env['res.users'].sudo().search([('token', '=', token)], limit=1)
            if not user_id:
                return Response(
                    json.dumps({
                        'status': 'error',
                        'message': 'Invalid or expired token. Please authenticate again.'
                    }),
                    content_type='application/json',
                    status=500
                )
            
            # Check if token is expired
            tz_name = user_id.tz or 'UTC'
            tz = pytz.timezone(tz_name)
            now_user_tz = datetime.now(tz)
            now_user_tz = now_user_tz.strftime('%Y-%m-%d %H:%M:%S')
            
            if user_id.expired_token_date:
                expired_token_utc = user_id.expired_token_date.replace(tzinfo=pytz.utc)
                expired_token_tz = expired_token_utc.astimezone(tz)
                expired_token_tz = expired_token_tz.strftime('%Y-%m-%d %H:%M:%S')
                
                if str(expired_token_tz) < str(now_user_tz):
                    return Response(
                        json.dumps({
                            'status': 'error',
                            'message': 'Invalid or expired token. Please authenticate again.'
                        }),
                        content_type='application/json',
                        status=500
                    )

            partner_attach_id = post['id'] or False if 'id' in post else False
            partner_id = post['partner_id'] or False if 'partner_id' in post else False
            req_document = post['req_document'] or False if 'req_document' in post else False
            availability = post['availability'] or False if 'availability' in post else False
            reason = post['reason'] or False if 'reason' in post else False
            file = post['file'] or False if 'file' in post else False

            _fields_includes_in_body = all([partner_attach_id, partner_id])

            if not _fields_includes_in_body:
                return Response(
                    json.dumps({
                        'status': 'error',
                        'message': 'Missing fields.'
                    }),
                    content_type='application/json',
                    status=500
                )

            # with open(file, 'rb') as f:
            partner_attachment_id = request.env['res.partner.attachment'].sudo().search([('id', '=', int(partner_attach_id)), ('partner_id', '=', int(partner_id))], limit=1)
            if not partner_attachment_id:
                return Response(
                    json.dumps({
                        'status': 'error',
                        'message': 'id not found!'
                    }),
                    content_type='application/json',
                    status=500
                )
            vals = {}
            if file:
                file_data = base64.b64encode(file.read()).decode('utf-8')
                ir_attachment_id = self.create_attachment(user_id, int(partner_id), req_document, file_data, 'application/pdf')
                _logger.info("ATTACHMENT")
                # _logger.info(ir_attachment_id)
                vals.update({'attachment': [(6, 0, [ir_attachment_id.id])]})
            if req_document:
                vals.update({'req_document': req_document})
            if availability:
                vals.update({'availability': availability})
            if reason:
                vals.update({'reason_unavailable': reason})

            partner_attachment_id.with_user(user_id).sudo().write(vals)
            results = request.env['res.partner.attachment'].sudo().search_read([('id', '=', int(partner_attach_id))])
            data = {
                'status': 'success',
                'data': results
            }
            headers = {'Content-Type': 'application/json'}
            return Response(json.dumps(data,indent=4, sort_keys=True, default=str), headers=headers)

        except Exception as e:
            return Response(
                json.dumps({
                    'status': 'error',
                    'message': str(e)
                }),
                headers={'Content-Type': 'application/json'},
                status=500
            )

