from odoo import fields, api, models, _
from odoo.exceptions import ValidationError, UserError
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from collections import defaultdict
from odoo.tools.translate import _


class StockPicking(models.Model):
    _inherit = 'stock.picking'

    def default_currency_id(self):
        currency = self.env['res.currency'].search([('name', '=', 'IDR')], limit=1)
        if currency:
            return currency.id
        else:
            return False

    def _get_default_accounting_period(self):
        accounting_period = self.env['sh.account.period'].search([('date_start', '<=', datetime.now().date()), ('date_end', '>=', datetime.now().date())], limit=1)
        if accounting_period:
            return accounting_period
        else:
            return False

    state_fppr = fields.Selection([
        ('draft', 'Draft'),
        ('pending_approval', 'Pending Approval'),
        ('ap_invoice', 'AP Invoice'),
        ('return', 'Return'),
        ('cancel', 'Cancel'),
    ], string='State', default="draft")
    purchase_id = fields.Many2one('purchase.order', string='Source Document')
    purchase_dua_id = fields.Many2one('purchase.order', string='Purchase Dua', tracking=True)
    partner_vendor_id = fields.Many2one('res.partner', string='Vendor', ondelete='set null', tracking=True)
    user_id = fields.Many2one('res.users', string='Buyer', tracking=True)
    buyer_employee_id = fields.Many2one('hr.employee', string='Buyer', related='purchase_dua_id.buyer_id')
    resquester_user_id = fields.Many2one('res.users', string='Requested By', tracking=True)
    created_user_id = fields.Many2one('res.users', string='Created By', tracking=True)
    validity_date = fields.Date('Validity Date', tracking=True)
    transaction_date = fields.Date('Transaction Date', default=fields.Date.context_today, readonly=True, tracking=True, )
    accounting_date = fields.Date('Accounting Date', readonly=True, tracking=True, compute='_compute_transaction_date' )
    invoice_date = fields.Date('Invoice Date', tracking=True)
    nomor_invoice = fields.Char('Nomor Invoice', tracking=True)
    invoice_move_id = fields.Many2one('account.move', string='Nomor Invoice', tracking=True, domain="[('move_type', '=', 'out_invoice'), ('state', '=', 'posted')]")
    currency_id = fields.Many2one('res.currency', string='currency', ondelete='set null', default=default_currency_id, tracking=True)
    invoice_amount = fields.Monetary('Invoice Amount', compute='_compute_amount_currency', store=True, tracking=True)
    attachment_nodin = fields.Binary('Attachment Nodin')
    name_attachment_nodin = fields.Char('Name ATtechment Nodin', tracking=True)
    attachment_invoice = fields.Binary('Attachment Invoice', tracking=True)
    name_attachment_invoice = fields.Char('Name ATtachment Invoice')
    cip_id = fields.Many2one('phase.project.cip', string='Project', ondelete='set null', tracking=True)
    nilai_akhir = fields.Float('Nilai Akhir', compute="_compute_nilai_akhir", store=True, tracking=True)
    description = fields.Char('Description', compute='_compute_description')
    total_bobot = fields.Float(compute='_compute_total_bobot', string='Total Bobot', store=True, tracking=True)
    is_mandatory_attachment_nodin = fields.Boolean('Is MAndatory ATtachment Nodin', default=False, compute='_compute_mandatory_attachment')
    accounting_period = fields.Date('Accounting Period', tracking=True)
    accounting_period_id = fields.Many2one('sh.account.period', string='Accounting Period', tracking=True, default=_get_default_accounting_period)
    name = fields.Char(tracking=True)
    partner_id = fields.Many2one(tracking=True)
    picking_type_id = fields.Many2one(tracking=True)
    location_id = fields.Many2one(tracking=True)
    location_dest_id = fields.Many2one(tracking=True)


    @api.constrains('invoice_date', 'transaction_date')
    def _check_invoice_date(self):
        for record in self:
            if record.invoice_date and record.transaction_date:
                if record.invoice_date < record.transaction_date:
                    raise ValidationError("Invoice Date tidak boleh lebih kecil dari Transaction Date.")
    

    def _default_assesment_purchase_lines(self):
        aspect_lines = self.env['assesment.aspect.line'].search([('aspect_id.name', '=', 'fppr')])
        return [(0, 0, {'assesment_aspect': aspect.name, 'persentasi_bobot': aspect.weight}) for aspect in aspect_lines]
    assesment_purchase_line_ids = fields.One2many('assesment.purchase.line', 'picking_id', string='Assesment Purchase Line', default=lambda self: self._default_assesment_purchase_lines())

    stock_move_line_ids = fields.One2many('stock.move', 'picking_id', string='Stock Move Line')
    approval_gr_line_ids = fields.One2many('approval.gr.line', 'picking_id', string='Approval GR Line')

    @api.depends('purchase_dua_id')
    def _compute_amount_currency(self):
        for rec in self:
            rec.invoice_amount = 0
            if rec.purchase_dua_id.invoice_ids:
                rec.invoice_amount = abs(sum(rec.purchase_dua_id.invoice_ids.mapped('amount_total_in_currency_signed')))
                
    @api.depends('transaction_date', 'validity_date')
    def _compute_mandatory_attachment(self):
        for rec in self:
            rec.is_mandatory_attachment_nodin = False
            if rec.transaction_date and rec.validity_date:
                if rec.transaction_date > rec.validity_date:
                    rec.is_mandatory_attachment_nodin = True
                else:
                    rec.is_mandatory_attachment_nodin = False

    @api.onchange('transaction_date')
    def _compute_transaction_date(self):
        if self.transaction_date:
            transaction_dt = fields.Date.from_string(self.transaction_date)  # Konversi ke datetime.date
            if transaction_dt.day > 25:
                next_month = transaction_dt.replace(day=1) + timedelta(days=32)
                self.accounting_date = next_month.replace(day=1)
            else:
                self.accounting_date = self.transaction_date
                
    
    _sql_constraints = [
        ("nomor_invoice_unique", "unique(nomor_invoice)", "Nomor Invoice must be unique"),
    ]
    
    @api.depends('nilai_akhir')
    def _compute_description(self):
        for rec in self:
            description = rec.env['assesment.score'].search([('start_scale', '<=', int(rec.nilai_akhir)), ('end_scale', '>=', int(rec.nilai_akhir))])
            if description:
                rec.description = description.definition
            elif not description and rec.nilai_akhir > 0:
                raise ValidationError(_("Value 'Description' is not set. Contact your administrator to complete the 'Master Final Score Definition."))
            else:
                rec.description = ''

    @api.depends('assesment_purchase_line_ids.bobot_nilai')
    def _compute_total_bobot(self):
        for rec in self:
            rec.total_bobot = sum(rec.assesment_purchase_line_ids.mapped('bobot_nilai'))
    
    @api.depends('total_bobot')
    def _compute_nilai_akhir(self):
        for rec in self:
            rec.nilai_akhir = (rec.total_bobot * 100) / 4

    @api.onchange('purchase_dua_id')
    def onchange_purchase_id(self):
        
        if not self.purchase_dua_id:
            return

        po = self.purchase_dua_id
        self.partner_vendor_id = po.partner_id.id
        self.user_id = po.user_id.id
        self.resquester_user_id = po.requester_user_id.id
        self.created_user_id = po.created_user_id.id
        self.validity_date = po.latest_delivery_date
        project = po.phase_project_id 
        self.cip_id  = project

        self.move_ids_without_package = [(5, 0, 0)]
        
        grouped_lines = {}
        i = 1

        values = []
        for line in po.order_line:
            if line.new_currency_id.is_budget or line.new_currency_id.name == 'USD':
                accounting_date = (self.transaction_date + relativedelta(months=1)).replace(day=1) if self.transaction_date.day > 25 else self.transaction_date
                usd_currency = self.env['res.currency'].search([('name', '=', 'USD')], limit=1)
                idr_currency = self.env['res.currency'].search([('name', '=', 'IDR')], limit=1)
                price_subtotal = usd_currency._convert(line.price_unit * line.product_qty, idr_currency, self.company_id, date=accounting_date)
                price_unit = usd_currency._convert(line.price_unit, idr_currency, self.company_id, date=accounting_date)
                subtotal_currency = line.subtotal_currency
                new_currency_id = line.new_currency_id
            else:
                price_subtotal = line.price_subtotal
                price_unit = line.price_unit
                subtotal_currency = line.subtotal_currency
                new_currency_id = line.new_currency_id
                
            product_id = line.product_id.id
            # if product_id in grouped_lines:
            #     grouped_lines[product_id]['product_uom_qty'] += line.product_qty
            #     grouped_lines[product_id]['quantity'] += line.product_qty
            #     grouped_lines[product_id]['ammount_currency'] += subtotal_currency
            # else:
            grouped_lines[i] = {
                'product_id': product_id,
                'product_tmpl_id': line.product_tmpl_id.id,
                'product_uom_qty': line.product_qty,
                'rkap_code': po.rkap_id.rkap_code,
                'quantity': line.product_qty,
                'unit_price': price_unit,
                'product_uom_id': line.product_uom.id,
                'name': line.name or line.product_id.display_name,
                'currency_id': new_currency_id,
                'ammount_currency': subtotal_currency,
                'purchase_order_line_id': line.id,
                'purchase_line_id': line.id,
                'location_id': self.location_id.id,
            }
            i += 1

        self.move_ids_without_package = [(0, 0, vals) for vals in grouped_lines.values()]    


    @api.constrains('move_ids_without_package', 'move_ids_without_package.quantity', 'move_ids_without_package.ammount_currency')
    def _constrains_move_ids_without_package(self):
        for record in self:
            move_lines = record.move_ids_without_package
            for move in move_lines:
                if move.purchase_order_line_id and move.ammount_currency > move.purchase_order_line_id.price_subtotal:
                    raise ValidationError(_(
                        "Amount currency %s tidak boleh lebih besar dari subtotal %s pada baris terkait."
                    ) % (move.ammount_currency, move.purchase_order_line_id.price_subtotal))
                elif move.purchase_order_line_id and move.quantity > move.purchase_order_line_id.product_qty:
                    raise ValidationError(_(
                        "Quantity %s tidak boleh lebih besar dari quantity %s pada baris terkait."
                    ) % (move.quantity, move.purchase_order_line_id.product_qty))

    @api.model
    def create(self, vals):
        picking_type = self.env['stock.picking.type'].browse(vals.get('picking_type_id'))
        if vals.get('purchase_dua_id') and picking_type.code == 'incoming':
            existing = self.env['stock.picking'].search([
                ('purchase_dua_id', '=', vals['purchase_dua_id']),
                ('picking_type_id', '=', vals['picking_type_id']),
            ])
            if existing:
                raise ValidationError("Purchase Order yang sama sudah digunakan di dokumen Receipt lain.")

        res = super(StockPicking, self).create(vals)

        if res.accounting_period_id.state == 'done':
            raise ValidationError('Accounting Period sudah closed')

        aspect_lines = self.env['assesment.aspect.line'].search([('aspect_id.name', '=', 'fppr')])
        if aspect_lines and not vals.get('assesment_purchase_line_ids'):
            res.assesment_purchase_line_ids = [(0, 0, {
                'assesment_aspect': aspect.name,
                'persentasi_bobot': aspect.weight
            }) for aspect in aspect_lines]

        return res

    def write(self, vals):
        res = super(StockPicking, self).write(vals)
        if self.accounting_period_id.state == 'done':
            raise ValidationError('Accounting Period sudah closed')
        return res


    def action_open_journal(self):
        return {
            'name': 'Journal',
            'type': 'ir.actions.act_window',
            'res_model': 'account.move',
            'view_mode': 'list,form',
            'domain': [('is_journal_gr', '=', True), ('picking_journal_gr_id', '=', self.id)],
            'context': {
                'create': False,
                'edit': False, 
                },
        }

    def action_open_journal_bill(self):
        return {
            'name': 'Vendor Bill',
            'type': 'ir.actions.act_window',
            'res_model': 'account.move',
            'view_mode': 'list,form',
            'domain': [('is_journal_bill', '=', True), ('picking_journal_bill_id', '=', self.id)],
            'context': {
                'create': False,
                # 'edit': False, 
                },
        }

    def action_button_submit(self):
        self.action_confirm()
        self.write({'state': 'assigned'})
        self.write({'state_fppr': 'pending_approval'})
        # self.write({'state_fppr': 'pending_approval', 'state': 'done'})
        for rec in self.assesment_purchase_line_ids:
            if not rec.score_penilaian:
                raise ValidationError(_("Please fill in the score for %s") % rec.assesment_aspect)
            
    def action_button_return_to_draft(self):
        self.write({'state_fppr': 'draft'})
        self.write({'state': 'draft'})
    
    def action_button_return_of_documents(self):
        self.write({'state_fppr': 'draft'})
        self.write({'state': 'draft'})

    def action_button_return_of_goods(self):
        self.write({'state_fppr': 'ap_invoice'})
        self.write({'state': 'done'})

    def action_button_cancel(self):
        self.write({'state_fppr': 'cancel'})
        self.write({'state': 'cancel'})

    def action_button_withdraw_approval(self):
        self.write({'state_fppr': 'pending_approval'})
        self.write({'state': 'assigned'})
    
    def action_approve(self):
        usd_currency = self.env['res.currency'].search([('name', '=', 'USD')], limit=1)
        idr_currency = self.env['res.currency'].search([('name', '=', 'IDR')], limit=1)
        po_lines = self.purchase_dua_id.order_line
        move_lines = self.move_ids_without_package
        category_map = {}
        credit_account_id = False
        bill_credit_account_id = self.partner_vendor_id.property_account_payable_id.id
        bill_debit_account_id = False
        for line in move_lines:
            category = line.product_id.categ_id
            if category not in category_map:
                category_map[category] = []
            category_map[category].append(line)
            if line.product_id.categ_id.property_stock_account_input_categ_id:
                credit_account_id = line.product_tmpl_id.categ_id.property_stock_account_input_categ_id.id
                bill_debit_account_id = line.product_tmpl_id.categ_id.property_stock_account_input_categ_id.id
        for category, lines in category_map.items():
            # Ambil journal dari property_stock_journal jika tersedia
            journal = category.property_stock_journal and category.property_stock_journal.id or False
            if not journal:
                raise UserError(f"Kategori produk '{category.name}' tidak memiliki 'property_stock_journal'.")

            line_ids = []
            for line in move_lines:
                if line.purchase_order_line_id.new_currency_id.is_budget:
                    accounting_date = (self.transaction_date + relativedelta(months=1)).replace(day=1) if self.transaction_date.day > 25 else self.transaction_date
                    rate = usd_currency._convert(line.purchase_order_line_id.price_unit * line.purchase_order_line_id.product_qty, idr_currency, self.company_id, date=accounting_date) / (line.purchase_order_line_id.price_unit * line.purchase_order_line_id.product_qty)
                else:
                    rate = 1
                line_ids.append((0, 0, {
                    'product_template_id': line.product_tmpl_id.id,
                    'description': line.product_tmpl_id.name,
                    'product_id': line.product_id.id,
                    'quantity': line.quantity,
                    'price_unit': line.purchase_order_line_id.price_unit * rate,
                    'new_unit_price': line.purchase_order_line_id.price_unit,
                    'name': line.name or line.product_id.display_name,
                    'currency_conversion_id': usd_currency.id if line.purchase_order_line_id and line.purchase_order_line_id.new_currency_id and line.purchase_order_line_id.new_currency_id.is_budget else line.purchase_order_line_id.new_currency_id.id,
                    'inverse_conv_rate': rate,
                    'manual_conversion': True if line.purchase_order_line_id and line.purchase_order_line_id.currency_rate > 1 else False,
                    # 'tax_ids': [(6, 0, line.tax_ids.ids)],
                }))
            journal_entry = self.env['account.move'].with_context(skip_check_journal_type=True).sudo().create({
                'move_type': 'in_receipt',
                'auto_post': 'no',
                # 'date': self.invoice_date,
                'partner_id': self.partner_vendor_id.id,
                'journal_id': journal,
                'invoice_date': self.transaction_date,
                'date': self.transaction_date,
                'currency_id': self.currency_id.id,
                'is_journal_gr': True,
                'picking_journal_gr_id': self.id,
                'name': self.name,
                'ref': self.name,
                'invoice_line_ids': line_ids,
            })
            for line in journal_entry.line_ids.filtered(lambda x: x.credit > 0):
                line.write({'account_id': credit_account_id})
        vendor_contact_id = self.env['res.partner'].search([('company_type', '=', 'contact'), ('parent_id', '=', self.partner_vendor_id.id)], limit=1)
        invoice_line_ids = []
        for line in move_lines:
            if line.purchase_order_line_id.new_currency_id.is_budget:
                accounting_date = (self.transaction_date + relativedelta(months=1)).replace(day=1) if self.transaction_date.day > 25 else self.transaction_date
                rate = usd_currency._convert(line.purchase_order_line_id.price_unit * line.purchase_order_line_id.product_qty, idr_currency, self.company_id, date=accounting_date) / (line.purchase_order_line_id.price_unit * line.purchase_order_line_id.product_qty)
            else:
                rate = 1
            invoice_line_ids.append((0, 0, {
                'product_template_id': line.product_tmpl_id.id,
                'description': line.product_tmpl_id.name,
                'product_id': line.product_id.id,
                'quantity': line.quantity,
                'price_unit': line.purchase_order_line_id.price_unit * rate,
                'new_unit_price': line.purchase_order_line_id.price_unit,
                'name': line.name or line.product_id.display_name,
                'currency_conversion_id': usd_currency.id if line.purchase_order_line_id and line.purchase_order_line_id.new_currency_id and line.purchase_order_line_id.new_currency_id.is_budget else line.purchase_order_line_id.new_currency_id.id,
                'inverse_conv_rate': rate,
                'manual_conversion': True if line.purchase_order_line_id and line.purchase_order_line_id.currency_rate > 1 else False,
                # 'tax_ids': [(6, 0, line.tax_ids.ids)],
            }))
        bill = self.env['account.move'].with_context(create_non_manual=True).create({
            'move_type': 'in_invoice',
            'auto_post': 'no',
            # 'date': self.invoice_date,
            'partner_id': self.partner_vendor_id.id,
            'vendor_contact_id': vendor_contact_id.id if vendor_contact_id else False,
            'journal_id': self.env['account.journal'].search([('type', '=', 'purchase')], limit=1).id,
            'invoice_date': self.transaction_date,
            'date': self.transaction_date,
            'currency_id': self.currency_id.id,
            'is_journal_bill': True,
            # 'payment_reference': self.name,
            'picking_id': self.id,
            'ref': self.name,
            # 'vendor_contact_id': self.purchase_dua_id.partner_contact_id.id,
            'contact_email': self.purchase_dua_id.email_contact,
            'unit_id': self.purchase_dua_id.unit_id.id,
            'group_department_id': self.purchase_dua_id.group_id.id,
            'budgetary_position_id': self.purchase_dua_id.purchase_request_id.justification_id.budgetary_position_id.id,
            'rkap_code': self.purchase_dua_id.rkap_code,
            'rkap_category_id': self.purchase_dua_id.rkap_category_id.id,
            'rkap_type_id': self.purchase_dua_id.rkap_type_id.id,
            'name': '/',
            'picking_journal_bill_id': self.id,
            'source': 'purchase',
            'purchase_order_id': self.purchase_dua_id.id,
            'invoice_line_ids': invoice_line_ids,
            'project_id': self.cip_id.id
        })
        for line in bill.line_ids.filtered(lambda x: x.debit > 0):
            line.with_context({'no_check': True}).write({
                'account_id': line.product_template_id.categ_id.property_stock_account_input_categ_id.id,
            })
        for line in bill.line_ids.filtered(lambda x: x.credit > 0):
            line.with_context({'no_check': True}).write({'account_id': bill_credit_account_id,})
        self.invoice_move_id = bill.id
        self.purchase_dua_id.bill_state = 'Billed'
        self.write({'state_fppr': 'ap_invoice', 'validity_date': datetime.today()}),
        self.write({'state': 'done'})
    
    def action_reject(self):
        self.write({'state_fppr': 'draft'})
    
    @api.onchange('location_id')
    def _onchange_location_id_set_to_moves(self):
        if self.picking_type_id.code == 'incoming':
            for move in self.move_ids_without_package:
                move.location_id = self.location_id.id

class StockMove(models.Model):
    _inherit = 'stock.move'

    rkap_code_id = fields.Many2one('account.budget.post', string='RKAP Code')
    rkap_code = fields.Char('RKAP Code')
    product_uom_id = fields.Many2one('uom.uom', string='Units')
    ammount_currency = fields.Monetary('Currency', readonly=False, currency_field='company_currency_id')
    currency_id = fields.Many2one('res.currency', string='currency', ondelete='set null', default=lambda self: self.env.company.currency_id)
    company_currency_id = fields.Many2one('res.currency', string='Company Currency', related='company_id.currency_id', store=True, readonly=True)
    unit_price = fields.Float('Unit Price')
    quantity = fields.Float('Quantity', compute='_compute_quantity', store=True)
    stock_location_internal_id = fields.Many2one('stock.location', string='Stock Location', domain=[('usage', '=', 'internal')])
    asset_location_id = fields.Many2one('asset.location', string='Asset Location')
    product_tmpl_id = fields.Many2one('product.template', string='Product Variant', related="", store=True)
    location_id = fields.Many2one('stock.location', required=True)
    purchase_order_line_id = fields.Many2one('purchase.order.line', 'Purchase Order Line')

    @api.depends('ammount_currency')
    def _compute_quantity(self):
        for rec in self:
            rec.quantity = 0
            if rec.ammount_currency and rec.purchase_order_line_id and rec.purchase_order_line_id.price_unit:
                try:
                    rec.quantity = rec.ammount_currency / rec.purchase_order_line_id.price_unit
                except ZeroDivisionError:
                    raise ValidationError(_("Unit Price cannot be zero."))
            if rec.ammount_currency == 0 and not self.env.context.get('from_so') and not self.env.context.get('create_siv'):
                raise ValidationError(_("Amount cannot be zero."))
            if rec.purchase_order_line_id and rec.ammount_currency > rec.purchase_order_line_id.price_subtotal:
                raise ValidationError(_(
                    "Amount currency %s tidak boleh lebih besar dari subtotal %s pada baris terkait."
                ) % (rec.ammount_currency, rec.purchase_order_line_id.price_subtotal))

    @api.onchange('ammount_currency')
    def _onchange_ammount_currency(self):
        for rec in self:
            if rec.purchase_order_line_id and rec.ammount_currency > rec.purchase_order_line_id.price_subtotal:
                raise ValidationError(_(
                    "Amount currency %s tidak boleh lebih besar dari subtotal %s pada baris terkait."
                ) % (rec.ammount_currency, rec.purchase_order_line_id.price_subtotal))

    @api.onchange('quantity')
    def _onchange_quantity(self):
        for rec in self:
            if rec.purchase_order_line_id:
                rec.ammount_currency = (rec.purchase_order_line_id.subtotal_currency / rec.purchase_order_line_id.product_qty) * rec.quantity if rec.purchase_order_line_id.product_qty != 0 else 0
            if rec.purchase_order_line_id and rec.quantity > rec.purchase_order_line_id.product_qty:
                raise ValidationError(_(
                    "Quantity %s tidak boleh lebih besar dari quantity %s pada baris terkait."
                ) % (rec.quantity, rec.purchase_order_line_id.product_qty))
    
    @api.onchange('stock_location_internal_id')
    def _onchange_location_id(self):
        for rec in self:
            if rec.stock_location_internal_id:
                rec.location_id = rec.stock_location_internal_id.id
            else:
                rec.location_id = False
            


class ApprovalGrLine(models.Model):
    _name = 'approval.gr.line'

    sequence = fields.Char('sequence')
    approval_name = fields.Char('Approval Name')
    approval_position = fields.Char('Approval Position')
    approval_status = fields.Char('Approval Status')
    approval_date = fields.Date('Approval Date')
    approval_note = fields.Char('Approval Note')
    reassign_to = fields.Char('Reassign To')
    approval_status_dua = fields.Char('Approval Status')
    approval_date_dua = fields.Date('Approval Date')
    approval_note_dua = fields.Char('Approval Note')
    picking_id = fields.Many2one('stock.picking', string='picking', ondelete='cascade')

    