<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Analytic Account Documentation</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            line-height: 1.6;
            background-color: #f5f6fa;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .oe_container {
            padding: 30px 15px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .oe_row {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            margin-bottom: 40px;
        }
        .oe_slogan {
            text-align: center;
            color: #673ab7;
            font-weight: 500;
            margin-bottom: 15px;
        }
        h2.oe_slogan {
            font-size: 2.2em;
        }
        h3.oe_slogan {
            font-size: 1.5em;
            color: #555;
        }
        h4.oe_slogan {
            font-size: 1.2em;
        }
        .oe_screenshot {
            text-align: center;
            margin: 20px 0;
        }
        .oe_screenshot img {
            max-width: 80%; /* تصغير حجم الصور */
            height: auto;
            border: 2px solid #e0e0e0; /* حدود أوضح */
            border-radius: 12px; /* زوايا دائرية أكثر وضوحًا */
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15); /* ظل أعمق */
            transition: transform 0.3s ease, box-shadow 0.3s ease; /* انتقال أكثر سلاسة */
        }
        .oe_screenshot img:hover {
            transform: scale(1.03); /* تكبير طفيف عند التحويم */
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2); /* ظل أكبر عند التحويم */
        }
        .image-caption {
            font-size: 0.9em;
            color: #666;
            margin-top: 10px;
        }
        .oe_span12 {
            width: 100%;
            padding: 0 15px;
        }
        p, li, ol {
            color: #555;
            font-size: 1.05em;
            text-align: left;
        }
        a {
            color: #673ab7;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        a:hover {
            color: #9575cd;
            text-decoration: underline;
        }
        code {
            background-color: #f0f0f0;
            padding: 3px 6px;
            border-radius: 4px;
            font-family: monospace;
            color: #673ab7;
        }
        .btn {
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }
        .btn-success {
            background-color: #673ab7;
            border-color: #673ab7;
            color: #fff !important;
        }
        .btn-success:hover {
            background-color: #9575cd;
            border-color: #9575cd;
        }
        .carousel-item {
            display: flex;
            justify-content: center;
        }
        .carousel-control-prev, .carousel-control-next {
            width: 50px;
            background-color: rgba(0, 0, 0, 0.3);
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }
        .carousel-control-prev:hover, .carousel-control-next:hover {
            opacity: 1;
        }
        .carousel-control-prev-icon, .carousel-control-next-icon {
            font-size: 24px;
        }
        .mt-5 {
            margin-top: 3rem;
        }
        .mb-5 {
            margin-bottom: 3rem;
        }
        .card-img-top {
            max-width: 80%; /* تصغير الصور في الـ carousel */
            height: auto;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin: 0 auto; /* توسيط الصورة */
        }
        .card-img-top:hover {
            transform: scale(1.03);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
<section class="oe_container">
    <div class="oe_row">
        <div class="oe_span12 text-center">
            <h2 class="oe_slogan">Product Analytic Account for Odoo</h2>
            <h3 class="oe_slogan">Effortlessly manage analytic distributions for products across invoices, sales, and
                purchases</h3>
        </div>
    </div>
</section>

<section class="oe_container mt-5">
    <div class="oe_row">
        <div class="oe_span12">
            <h2 class="oe_slogan">Simplify Analytic Distribution</h2>
            <h3 class="oe_slogan">Set default analytic distributions on products and auto-apply them</h3>
            <p>The <code>product_analytic_account</code> module adds an "Analytic Distribution" field to product
                templates, allowing you to define default analytic distributions. These are automatically applied to
                invoice lines, sales order lines, and purchase order lines when the product is selected, streamlining
                your accounting processes.</p>
        </div>
        <div class="oe_screenshot">
            <img alt="Analytic Distribution Field">
            <div class="image-caption">Setting analytic distribution in the product form</div>
        </div>
    </div>
</section>

<section class="oe_container mt-5">
    <div class="oe_row">
        <div class="oe_span12">
            <h2 class="oe_slogan">Key Features</h2>
            <ul>
                <li>Add an "Analytic Distribution" field to product templates for easy configuration.</li>
                <li>Automatically populate analytic distributions in invoice lines when selecting a product.</li>
                <li>Apply default analytic distributions to sales order lines and purchase order lines.</li>
                <li>Support for customizable analytic precision to control decimal places.</li>
            </ul>
        </div>
        <div class="oe_screenshot">
            <img alt="Key Features Demo">
            <div class="image-caption">Demonstrating automatic analytic distribution in purchase orders</div>
        </div>
    </div>
</section>

<section class="oe_container mt-5">
    <div class="oe_row">
        <div class="oe_span12">
            <h2 class="oe_slogan">Usage Steps</h2>
            <ol>
                <li><strong>Configure a Product:</strong> Open the product form and set the desired analytic
                    distribution (e.g., 50% to Azad ZS-L007, 50% to Grading Department).
                </li>
                <div class="oe_screenshot">
                    <img src="screenshot_1.png" alt="Configure Product Demo">
                    <div class="image-caption">Setting analytic distribution in the product form</div>
                </div>
                <li><strong>Create a Purchase Order:</strong> Add the product to a purchase order line, and the analytic
                    distribution will sync automatically.
                </li>
                <div class="oe_screenshot">
                    <img src="screenshot_2.png" alt="Purchase Order Demo">
                    <div class="image-caption">Analytic distribution auto-applied in a purchase order</div>
                </div>
                <li><strong>Create a Sales Order:</strong> Select the product in a sales order, and the analytic
                    distribution will be applied.
                </li>
                <div class="oe_screenshot">
                    <img src="screenshot_3.png" alt="Sales Order Demo">
                    <div class="image-caption">Analytic distribution auto-applied in a sales order</div>
                </div>
                <li><strong>Create an Invoice:</strong> Add the product to an invoice line, and the analytic
                    distribution will auto-populate.
                </li>
                <div class="oe_screenshot">
                    <img src="screenshot_4.png" alt="Invoice Demo">
                    <div class="image-caption">Analytic distribution auto-applied in an invoice</div>
                </div>
            </ol>
        </div>
    </div>
</section>

<section class="oe_container mt-5">
    <div class="oe_row">
        <div class="oe_span12">
            <h2 class="oe_slogan">Requirements</h2>
            <p>This module depends on the following Odoo modules:<br><strong>product</strong>, <strong>account</strong>,
                <strong>sale</strong>, <strong>purchase</strong>, <strong>analytic</strong><br>Note: Contact us if you
                face any difficulties during setup or installation.</p>
        </div>
    </div>
</section>

<section class="oe_container mt-5">
    <div class="oe_row">
        <div class="oe_span12 text-center">
            <h4 class="oe_slogan">
                <a href="https://wa.me/+************" target="_blank" class="btn btn-whatsapp"
                   style="background-color:#25D366; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none;">
                    <i class="fa fa-whatsapp"></i> Contact Us on WhatsApp
                </a>
            </h4>
        </div>
    </div>
</section>

<section class="oe_container mt-5">
    <h2 class="oe_slogan">Screenshots</h2>
    <div id="slides" class="carousel slide mb-5" data-ride="carousel">
        <div class="carousel-inner">
            <div class="carousel-item active">
                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <img class="card-img-top" src="screenshot_1.png" alt="Configure Product">
                            <div class="card-body text-center">
                                <h4 class="card-title">Configure Analytic Distribution on Product</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <img class="card-img-top" src="screenshot_2.png" alt="Purchase Order">
                            <div class="card-body text-center">
                                <h4 class="card-title">Auto-Apply in Purchase Order</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <img class="card-img-top" src="screenshot_3.png" alt="Sales Order">
                            <div class="card-body text-center">
                                <h4 class="card-title">Auto-Apply in Sales Order</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="carousel-item">
                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <img class="card-img-top" src="screenshot_4.png" alt="Invoice Line">
                            <div class="card-body text-center">
                                <h4 class="card-title">Auto-Apply in Invoice Line</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <a class="carousel-control-prev" href="#slides" data-slide="prev">
            <span class="carousel-control-prev-icon"><i class="fa fa-chevron-left"></i></span>
        </a>
        <a class="carousel-control-next" href="#slides" data-slide="next">
            <span class="carousel-control-next-icon"><i class="fa fa-chevron-right"></i></span>
        </a>
    </div>
</section>

<section class="oe_container mt-5">
    <div class="oe_row">
        <div class="oe_span12 text-center">
            <h2 class="oe_slogan">Support</h2>
            <p>Please contact us if you need customization/support for this module: <a
                    href="mailto:<EMAIL>"><EMAIL></a></p>
            <p>
                <a class="btn btn-success mt-3" href="https://www.linkedin.com/in/abdelrhman1997/" target="_blank">LinkedIn
                    Profile</a>
                <a class="btn btn-success mt-3" href="mailto:<EMAIL>">Support</a>
            </p>
        </div>
    </div>
</section>

<!-- Bootstrap JS and dependencies -->
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>