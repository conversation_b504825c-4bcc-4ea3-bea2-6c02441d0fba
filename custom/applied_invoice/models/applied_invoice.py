from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError


class MiscellaneousMiscellaneous(models.Model):
    _inherit = 'miscellaneous.miscellaneous'

    ### Add Period ###
    period_id = fields.Many2one(
        "invoicing.period.line", string="Period", compute="_compute_get_period", store=True
    )

    ### Add Period #
    @api.depends("date")
    def _compute_get_period(self):
        if self:
            for rec in self:
                rec.period_id = False
                if rec.date:
                    period = (
                        self.env["invoicing.period.line"]
                        .sudo()
                        .search(
                            [
                                ("date_start", "<=", rec.date),
                                ("date_end", ">=", rec.date),
                                ('move_type', '=', 'out_invoice'),                                
                            ],
                            limit=1,
                        )
                    )
                    if period:
                        rec.period_id = period.id

    @api.model_create_multi
    def create(self, vals):
        # OVERRIDE
        write_off_line_vals_list = []
        for val in vals:
            # print(val, 'masuk', self)
            if val.get('misc_name', 'New') == 'New' and val.get('misc_type') == 'receive':
                val['misc_name'] = self.env['ir.sequence'].next_by_code('miscellaneous.receipt') or '/'
            if val.get('misc_name', 'New') == 'New' and val.get('misc_type') == 'payment':
                val['misc_name'] = self.env['ir.sequence'].next_by_code('miscellaneous.payment') or '/'
            if 'journal_id' not in val:
                val['journal_id'] = self._get_default_journal().id
            if 'currency_id' not in val:
                journal = self.env['account.journal'].browse(val['journal_id'])
                val['currency_id'] = journal.currency_id.id or journal.company_id.currency_id.id
            if val.get('amount', '0') == 0:
                raise UserError(
                    _(
                        "the Amount Misc must greater than 0"
                    )
                )

            write_off_line_vals_list.append(val.pop('write_off_line_vals', None))

            # Force the move_type to avoid inconsistency with residual 'default_move_type' inside the context.
            val['move_type'] = 'entry'
            val['name'] = '/'
            
            #  Period validation check
            date = val.get('date')
            if date:
                period = (
                    self.env["invoicing.period.line"]
                    .sudo()
                    .search(
                        [
                            ("date_start", "<=", date),
                            ("date_end", ">=", date),
                            ('move_type', '=', 'out_invoice'),
                        ],
                        limit=1,
                    )
                )
                if period and period.state == 'close':
                    raise UserError(_("You cannot select a date from a closed invoice period."))
                
        misc = super().create(vals)

        for i, pay in enumerate(misc):
            write_off_line_vals = write_off_line_vals_list[i]
            to_write = {'id': pay.id}
            for k, v in vals[i].items():
                if k in self._fields and self._fields[k].store and k in pay.move_id._fields \
                        and pay.move_id._fields[k].store:
                    to_write[k] = v

            if 'line_ids' not in vals[i]:
                to_write['line_ids'] = [(0, 0, line_vals) for line_vals in
                                        pay._prepare_move_line_default_vals(write_off_line_vals=write_off_line_vals)]

            if pay.reserve_amount < 0:
                raise ValidationError('Available Amount cannot be negative')
            pay.move_id.write(to_write)
        return misc

    def write(self, vals):
        # OVERRIDE

        # Validate period if 'date' is being updated
        if 'date' in vals:
            new_date = vals['date']
            if new_date:
                for rec in self:
                    period = (
                        self.env["invoicing.period.line"]
                        .sudo()
                        .search(
                            [
                                ("date_start", "<=", new_date),
                                ("date_end", ">=", new_date),
                                ("move_type", "=", "out_invoice"),
                            ],
                            limit=1,
                        )
                    )
                    if period and period.state == "close":
                        raise UserError(_(
                            "You cannot select a date from a closed invoice period (Record ID: %s)."
                        ) % rec.id)

        # Track move states for all records
        original_move_states = {rec.id: rec.move_id.state for rec in self}

        for rec in self:
            if rec.move_id.state == 'posted':
                rec.move_id.write({'state': 'draft'})

        res = super(MiscellaneousMiscellaneous, self.with_context(skip_readonly_check=True)).write(vals)

        for rec in self:
            rec._synchronize_to_moves(set(vals.keys()))

            if not rec.analytic_account_id:
                rec.analytic_account_id = rec._get_default_analytic_account()

            if rec.applied_customer_move_id:
                rec.applied_customer_move_id.with_context(skip_readonly_check=True).write({
                    'date': rec.applied_partner_date
                })

            if rec.reserve_amount < 0:
                raise UserError(_("Available amount cannot be negative (Record ID: %s)") % rec.id)

        # Restore original move state if it changed
        for rec in self:
            original_state = original_move_states.get(rec.id)
            if rec.move_id.state != original_state:
                rec.move_id.write({'state': original_state})

        return res

    def _synchronize_to_moves(self, changed_fields):
        if self._context.get('skip_account_move_synchronization'):
            return

        if not any(field_name in changed_fields for field_name in (
                'date', 'amount', 'receipt_type_id', 'currency_id', 'partner_id', 'destination_account_id',
                'partner_bank_id', 'journal_id', 'description', 'journal_group',
        )):
            return

        for pay in self.with_context(skip_account_move_synchronization=True):
            liquidity_lines, counterpart_lines, writeoff_lines = pay._seek_for_lines()

            # Make sure to preserve the write-off amount.
            # This allows to create a new payment with custom 'line_ids'.

            if liquidity_lines and counterpart_lines and writeoff_lines:

                counterpart_amount = sum(counterpart_lines.mapped('amount_currency'))
                writeoff_amount = sum(writeoff_lines.mapped('amount_currency'))

                # To be consistent with the payment_difference made in account.payment.register,
                # 'writeoff_amount' needs to be signed regarding the 'amount' field before the write.
                # Since the write is already done at this point, we need to base the computation on accounting values.
                if (counterpart_amount > 0.0) == (writeoff_amount > 0.0):
                    sign = -1
                else:
                    sign = 1
                writeoff_amount = abs(writeoff_amount) * sign

                write_off_line_vals = {
                    'name': writeoff_lines[0].name,
                    'amount': writeoff_amount,
                    'account_id': writeoff_lines[0].account_id.id,
                }
            else:
                write_off_line_vals = {}

            line_vals_list = pay._prepare_move_line_default_vals(write_off_line_vals=write_off_line_vals)

            line_ids_commands = []
            if liquidity_lines:
                for line in liquidity_lines:
                    line_ids_commands.append((1, line.id, line_vals_list[0]))  # Adjust if needed per-line
            else:
                line_ids_commands.append((0, 0, line_vals_list[0]))

            if counterpart_lines:
                for line in counterpart_lines:
                    line_ids_commands.append((1, line.id, line_vals_list[1]))  # Adjust if needed per-line
            else:
                line_ids_commands.append((0, 0, line_vals_list[1]))

            for line in writeoff_lines:
                line_ids_commands.append((2, line.id))

            for extra_line_vals in line_vals_list[2:]:
                line_ids_commands.append((0, 0, extra_line_vals))

            # Update the existing journal items.
            # If dealing with multiple write-off lines, they are dropped and a new one is generated.

            pay.move_id.with_context(skip_readonly_check=True).write({
                'partner_id': pay.partner_id.id,
                'currency_id': pay.currency_id.id,
                'partner_bank_id': pay.partner_bank_id.id,
                # 'analytic_account_id': pay.analytic_account_id.id,
                'line_ids': line_ids_commands,
            })
            
    def action_post(self):
        ''' draft -> posted '''
        # Validate that date is not in a closed period
        if self.date:
            period = (
                self.env["invoicing.period.line"]
                .sudo()
                .search(
                    [
                        ("date_start", "<=", self.date),
                        ("date_end", ">=", self.date),
                        ("move_type", "=", "out_invoice"),
                    ],
                    limit=1,
                )
            )
            if period and period.state == "close":
                raise UserError(_("You cannot post this record because the date falls in a closed invoice period."))
        
        if self.reserve_amount < 0:
            raise ValidationError('Available Amount cannot be negative')

        self.move_id.with_context(from_misc=True)._post(soft=False)
        self.applied_customer_move_id.with_context(from_misc=True)._post(soft=False)   
        if self.misc_type == 'receive' and self.misc_partner_id and self.journal_group == 'split' \
                and self.applied_customer_journal_id and not self.applied_customer_move_id:
            self.action_applied_to_partner()
        elif self.misc_type == 'receive' and self.misc_partner_id \
                and self.applied_customer_journal_id and self.applied_customer_move_id \
                and self.applied_customer_move_id.state == 'draft':
            if self.applied_customer_move_id.amount_total == self.amount:
                self.applied_customer_move_id.with_context(from_misc=True)._post(soft=False)
            elif self.applied_customer_move_id.amount_total != self.amount:
                to_write = []
                for move_line in self.applied_customer_move_id.line_ids:
                    to_write.append((1, move_line.id, {
                        'debit': self.amount if move_line.debit > 0.0 else 0.0,
                        'credit': self.amount if move_line.credit > 0.0 else 0.0,
                    }))
                    self.applied_customer_move_id.with_context(check_move_validity=False).write({'line_ids': to_write})
                self.applied_customer_move_id.with_context(from_misc=True)._post(soft=False)    

    def action_applied_invoice_bulky(self):
        # Validate that date is not in a closed period
        if self.date:
            period = (
                self.env["invoicing.period.line"]
                .sudo()
                .search(
                    [
                        ("date_start", "<=", self.date),
                        ("date_end", ">=", self.date),
                        ("move_type", "=", "out_invoice"),
                    ],
                    limit=1,
                )
            )
            if period and period.state == "close":
                raise UserError(_("You cannot applied this record because the date falls in a closed invoice period."))
                
        if not self.applied_customer_move_id and self.journal_group == 'split':
            raise UserError(
                _(
                    "Can't applied to invoice without applied to customer in Split Journal Type, Please applied to "
                    "customer Firstly "
                )
            )
        elif (self.applied_customer_move_id and self.journal_group == 'split') or (
                not self.applied_customer_move_id and self.journal_group == 'merge'):
            for line in self.invoice_ids:
                print(line)
                if line.state == 'draft':
                    if line.applied_amount <= 0:
                        raise ValidationError('Applied amount must be more than 0')
                    
                    line.action_applied_invoice()

class AppliedInvoices(models.Model):
    _inherit = 'applied.invoices'

    amount = fields.Monetary(
        currency_field='misc_currency_id',
        related='misc_id.amount',
        string="Amount",
        readonly=True, default=0)

    @api.onchange('applied_amount', 'payment_difference_handling')
    def _onchange_difference_handling(self):
        if self.payment_difference_handling in ['open', 'full_invoice']:
            self.payment_difference = 0
        elif self.payment_difference_handling == 'reconcile':
            if self.misc_currency_id == self.invoice_currency_id:
                self.payment_difference = abs(self.invoice_amount - self.applied_amount)
            elif self.misc_currency_id == self.company_id.currency_id == self.invoice_currency_id:
                self.payment_difference = abs(self.invoice_amount - self.applied_amount)
            else:
                amount_payment_currency = self.company_id.currency_id._convert(self.invoice_amount,
                                                                                self.invoice_currency_id,
                                                                                self.company_id,
                                                                                self.transaction_date)
                self.payment_difference = abs(amount_payment_currency - self.applied_amount)

    # calculate difference applied amount with invoice amount
    @api.onchange('applied_amount')
    def compute_payment_difference(self):
        if self.payment_difference_handling in ['open', 'full_invoice']:
            self.payment_difference = 0
        elif self.payment_difference_handling == 'reconcile':
            if self.misc_currency_id == self.invoice_currency_id:
                self.payment_difference = abs(self.invoice_amount - self.applied_amount)
            elif self.misc_currency_id == self.company_id.currency_id == self.invoice_currency_id:
                self.payment_difference = abs(self.invoice_amount - self.applied_amount)
            else:
                amount_payment_currency = self.company_id.currency_id._convert(self.invoice_amount,
                                                                                self.invoice_currency_id,
                                                                                self.company_id,
                                                                                self.transaction_date)
                self.payment_difference = abs(amount_payment_currency - self.applied_amount)

    @api.constrains('payment_difference_handling', 'amount', 'invoice_amount_residual')
    def _check_full_invoice_amount(self):
        if self.payment_difference_handling == 'full_invoice' and self.amount < self.invoice_amount_residual:
            raise ValidationError(
                _("Amount must not be less than residual invoice amount when using 'Full Invoice'.")
            )

    @api.onchange('applied_amount', 'invoice_amount_residual', 'payment_difference')
    def _compute_amount_remaining(self):
        """ compute function to calculate amount_remaining """
        for rec in self:
            if rec.move_id and rec.state != 'posted':
                rec.invoice_amount_residual = rec.invoice_amount
                temp = rec.applied_amount - rec.payment_difference
                rec.amount_remaining = rec.invoice_amount_residual - abs(temp)
            elif rec.state == 'posted':
                temp = rec.applied_amount - rec.payment_difference
                rec.amount_remaining = rec.invoice_amount_residual - abs(temp)

    @api.onchange('applied_amount')
    def _onchange_applied_amount(self):
        if self.applied_amount > self.invoice_amount:
            if self.payment_difference_handling != 'reconcile' or not self.writeoff_account_id:
                raise UserError(_(
                    "You can't set applied amount greater than invoice amount unless you are reconciling the difference and a write-off account is defined."))
