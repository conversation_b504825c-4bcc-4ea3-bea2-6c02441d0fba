<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data>

        <record id="assesment_purchase_view_form" model="ir.ui.view">
            <field name="name">assesment.purchase.view.form</field>
            <field name="model">assesment.purchase</field>
            <field name="arch" type="xml">
                <form string="FPIP" create="0">
                    <header>
                        <button string="Submit" name="button_write_approval" type="object" class="oe_highlight" invisible="state != 'draft'"/>
                        <button string="Reject" name="button_reject_approval" type="object" class="oe_highlight" invisible="1"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,waiting,approve,reject,done"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <label for="name"/>
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>
                        <group>
                            <field name="purchase_id" />
                            <field name="po_description" />
                            <field name="partner_id" />
                            <field name="user_id" />
                            <field name="employee_id" />
                            <field name="final_score" />
                            <field name="description" />
                        </group>
                        <notebook>
                            <page name="penilaian" string="Penilaian">
                                <field name="assesment_purchase_line_ids">
                                    <list create="0" delete="0" editable="bottom">
                                        <field name="assesment_aspect" />
                                        <field name="score_penilaian" required="1" />
                                    </list>
                                </field>
                            </page>
                            <page name="assesment_detail" string="Assesment Detail">
                                <field name="assesment_detail_line_ids">
                                    <list create="0" editable="bottom">
                                        <field name="parameter" />
                                        <field name="penjelasan" />
                                        <field name="assesment_detail_max" />
                                        <field name="weight" />
                                        <field name="assesment_param_satu" />
                                        <field name="assesment_param_dua" />
                                        <field name="assesment_param_tiga" />
                                        <field name="assesment_param_empat" />
                                    </list>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                    <chatter/>
                </form>
            </field>
        </record>

        <record id="assesment_purchase_view_list" model="ir.ui.view">
            <field name="name">assesment.purchase.view.list</field>
            <field name="model">assesment.purchase</field>
            <field name="arch" type="xml">
                <list string="FPIP" create="0">
                    <field name="name" />
                    <field name="purchase_id" />
                </list>
            </field>
        </record>

        <record id="assesment_purchase_action" model="ir.actions.act_window">
            <field name="name">FPIP</field>
            <field name="res_model">assesment.purchase</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    There is no examples click here to add new Assesment Purchase.
                </p>
            </field>
        </record>
    
    </data>
    

</odoo>
