<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data>
    
        <record id="inherit_purchase_order_form_view_id_inherit_module_purchase" model="ir.ui.view">
            <field name="name">purchase.order.view.form.inherit</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="purchase.purchase_order_form"/>
            <field name="arch" type="xml">
                <xpath expr="//header" position="inside">
                    <button string="Create FPIP" name="action_create_fpip" type="object" class="oe_highlight" invisible="new_state != 'closed'"/>
                </xpath>
                <xpath expr="//field[@name='tax_calculation_rounding_method']" position="after">
                    <field name="requester_user_id" />
                    <field name="created_user_id" />
                    <field name="description" />
                </xpath>
            </field>
        </record>

        <record id="inherit_purchase_order_view_tree_id_inherit_purchase" model="ir.ui.view">
            <field name="name">purchase.order.view.form.inherit</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="purchase.purchase_order_view_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='activity_ids']" position="after">
                    <field name="fpip" optional="hide" />
                </xpath>
            </field>
        </record>

        <record id="purchase_order_fpip_pending_action" model="ir.actions.act_window">
            <field name="name">FPIP Pending</field>
            <field name="res_model">purchase.order</field>
            <field name="view_mode">list,form</field>
            <field name="domain">[('new_state', '=', 'closed'),('fpip', '=', 'pending')]</field>
            <field name="help" type="html">
                <h1>Kosong</h1>
            </field>
        </record>
    
    </data>
    

</odoo>
