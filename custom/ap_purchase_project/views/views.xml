<odoo>
  <data>
    <!-- explicit list view definition -->
<!--
    <record model="ir.ui.view" id="ap_purchase_project.list">
      <field name="name">ap_purchase_project list</field>
      <field name="model">ap_purchase_project.ap_purchase_project</field>
      <field name="arch" type="xml">
        <list>
          <field name="name"/>
          <field name="value"/>
          <field name="value2"/>
        </list>
      </field>
    </record>
-->

    <!-- actions opening views on models -->
<!--
    <record model="ir.actions.act_window" id="ap_purchase_project.action_window">
      <field name="name">ap_purchase_project window</field>
      <field name="res_model">ap_purchase_project.ap_purchase_project</field>
      <field name="view_mode">list,form</field>
    </record>
-->

    <!-- server action to the one above -->
<!--
    <record model="ir.actions.server" id="ap_purchase_project.action_server">
      <field name="name">ap_purchase_project server</field>
      <field name="model_id" ref="model_ap_purchase_project_ap_purchase_project"/>
      <field name="state">code</field>
      <field name="code">
        action = {
          "type": "ir.actions.act_window",
          "view_mode": "list,form",
          "res_model": model._name,
        }
      </field>
    </record>
-->

    <!-- Top menu item -->
<!--
    <menuitem name="ap_purchase_project" id="ap_purchase_project.menu_root"/>
-->
    <!-- menu categories -->
<!--
    <menuitem name="Menu 1" id="ap_purchase_project.menu_1" parent="ap_purchase_project.menu_root"/>
    <menuitem name="Menu 2" id="ap_purchase_project.menu_2" parent="ap_purchase_project.menu_root"/>
-->
    <!-- actions -->
<!--
    <menuitem name="List" id="ap_purchase_project.menu_1_list" parent="ap_purchase_project.menu_1"
              action="ap_purchase_project.action_window"/>
    <menuitem name="Server to list" id="ap_purchase_project" parent="ap_purchase_project.menu_2"
              action="ap_purchase_project.action_server"/>
-->
  </data>
</odoo>
