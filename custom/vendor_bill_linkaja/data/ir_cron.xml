<odoo>
  <data >
    <record id="ir_cron_send_vendor_bill_tax_messages" model="ir.cron">
      <field name="name">Send Vendor Bill Tax Messages</field>
      <field name="model_id" ref="vendor_bill_linkaja.model_send_bill_message_queue"/>
      <field name="state">code</field>
      <field name="code">model.action_send_scheduled_messages()</field>
      <field name="interval_number">1</field>
      <field name="interval_type">days</field>
      <!-- <field name="numbercall">-1</field> -->
      <field name="active">True</field>
      <field name="nextcall" eval="(datetime.now() + timedelta(days=1)).replace(hour=8, minute=0, second=0)"/>
    </record>

    <record id="ir_cron_send_customer_invoice_tax_messages" model="ir.cron">
      <field name="name">Send Customer Invoice Tax Messages</field>
      <field name="model_id" ref="vendor_bill_linkaja.model_send_bill_message_queue"/>
      <field name="state">code</field>
      <field name="code">model.action_send_scheduled_messages_invoice()</field>
      <field name="interval_number">1</field>
      <field name="interval_type">days</field>
      <!-- <field name="numbercall">-1</field> -->
      <field name="active">True</field>
      <field name="nextcall" eval="(datetime.now() + timedelta(days=1)).replace(hour=8, minute=0, second=0)"/>
    </record>

  </data>
</odoo>
