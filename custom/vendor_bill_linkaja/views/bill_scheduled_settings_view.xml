<odoo>
    <data>
    
        <record id="res_config_bill_settings_view_form" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.approval</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="100"/>
            <field name="inherit_id" ref="account.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//block[@name='fiscal_localization_setting_container']" position="after">
                    <block title="Scheduled Bill Settings" name="bill_scheduler_settings" invisible='1'>
                        <setting id="bill_scheduler_time">
                            <field name="bill_scheduler_time"/>
                        </setting>
                        <setting id="res_partner_ids">
                            <field name="res_partner_ids" widget="many2many_tags"/>
                        </setting>
                        <setting id="body">
                            <field name="body" />
                        </setting>
                    </block>
                </xpath>
            </field>
        </record>
    </data>
    
</odoo>