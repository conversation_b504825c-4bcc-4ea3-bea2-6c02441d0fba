# -*- coding: utf-8 -*-
import logging

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from odoo.tools import frozendict
import json

_logger = logging.getLogger(__name__)


class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'
    
    line_category = fields.Selection([
        ('denda', 'Denda Kurang Lebih Potong'),
        ('materai', 'Materai'),
        ('pph21', 'PPh 21'),
        ('pph22', 'PPh 22'),
        ('pph23', 'PPh 23'),
        ('pph26', 'PPh 26'),
        ('pph4', 'PPh 4 Ayat 2'),
        ('pph', 'PPh Yang Ditanggung'),
        ('vatsa', 'Vatsa'),
    ], string='Line Category', copy=False)
    category_id = fields.Many2one('fpjp.line.category', string='Line Category')
    
    product_template_id = fields.Many2one('product.template', string='Product',ondelete='restrict', index=True)
    # product_domain = fields.Char(string='Product Domain', store=True, compute='_compute_product_domain')
    description = fields.Char('Description')
    
    manual_conversion = fields.Boolean(string='Apply Manual Exchange Rate')
    manual_conversion_rate = fields.Boolean(string='Rate', column_invisible="manual_conversion")
    #custom
    company_currency_id = fields.Many2one('res.currency', string='Company Currency',default=lambda self: self.env.company.currency_id)
    currency_id = fields.Many2one('res.currency', string='Currency', required=True, default=lambda self: self.env.company.currency_id)
    inverse_conv_rate = fields.Float(string='Rate', default=1.0,) 
    price_total_currency_bill = fields.Monetary(string='Sub Total Currency', compute='_compute_price_total_cure', currency_field="currency_conversion_id")
    price_subtotal_bill = fields.Monetary(string="Sub Total IDR", compute='_compute_price_subtotal', currency_field="company_currency_id")
    
    currency_conversion_id = fields.Many2one('res.currency', string='Currency Conversion', default=lambda self: self.env.company.currency_id, store=True)
    price_total_currency = fields.Monetary(string='Total Currency', currency_field='currency_conversion_id', store=True, compute='_compute_price_total_currency')
    
    amount_discount = fields.Monetary(string='Amount Discount', store=True, compute='_compute_amount_discount', inverse='_inverse_amount_discount', currency_field="company_currency_id")
    group_id = fields.Many2one('hr.department', string='Group', domain=[('department_type', '=', '3_group')])
    product_type = fields.Selection(related='product_template_id.type', string='Product Type')
    rkap_code_id = fields.Many2one('account.budget.post', string='Kode RKAP', tracking=True, domain=[('is_allocated', '=', True)])
    rkap_code = fields.Char(string='Kode RKAP',tracking=True,related='move_id.rkap_code',store=True)
    
    source = fields.Selection(selection=[
        ('purchase', 'Purchase Order'),
        ('fpjp', 'FPJP'),
        ('manual', 'Manual'),
        ],string='Source',related='move_id.source')
    new_unit_price = fields.Float('New Unit Price')
    
    type = fields.Selection([
        ('asset', 'Asset'),
        ('cip', 'CIP'),
        ('asset_service', 'Asset Service'),
    ], string='Asset Type')

    product_tmpl_domain = fields.Binary(
        compute="_compute_product_domain"
    )

    product_domain = fields.Binary(
        compute="_compute_product_domain"
    )

    ### redefine UoM domain
    product_uom_domain = fields.Binary(compute="_compute_product_uom_domain")

    @api.depends('product_id')
    def _compute_product_uom_domain(self):
        """Compute domain for UoM field"""
        for line in self:
            if line.product_id:
                domain = [('category_id', '=', line.product_uom_category_id)]
            else:
                domain = []  

            line.product_uom_domain = domain

    @api.depends('fpjp_line_id', 'product_template_id')
    def _compute_product_domain(self):
        """Compute domain for partner field based on journal"""
        for line in self:
            if line.fpjp_line_id and line.fpjp_line_id.type == 'asset':
                domain_tmpl = [('is_asset', '=', True)]
                domain = [('is_asset', '=', True), ('product_tmpl_id','=', line.product_template_id.id), ('is_product_variant', '=', True)]
            elif line.fpjp_line_id and line.fpjp_line_id.type == 'cip':
                domain_tmpl = [('is_cip', '=', True)]
                domain = [('is_cip', '=', True), ('product_tmpl_id','=', line.product_template_id.id), ('is_product_variant', '=', True)]
            else:
                domain_tmpl = []  
                domain = [('product_tmpl_id','=', line.product_template_id.id), ('is_product_variant', '=', True)]

            line.product_tmpl_domain = domain_tmpl
            line.product_domain = domain

    @api.depends('account_id', 'company_id', 'discount', 'price_unit', 'quantity', 'currency_rate')
    def _compute_discount_allocation_needed(self):
        res = super(AccountMoveLine, self)._compute_discount_allocation_needed()
        for line in self:
            if line.move_id.move_type == 'in_invoice':
                line.discount_allocation_dirty = True
                discount_allocation_account = line.move_id._get_discount_allocation_account()

                if not discount_allocation_account or line.display_type != 'product' or line.currency_id.is_zero(line.discount):
                    line.discount_allocation_needed = False
                    continue

                # Hitung nilai diskon
                # discounted_amount_currency = line.currency_id.round(line.move_id.direction_sign * line.quantity * line.price_unit * line.discount/100)
                discounted_amount_currency = line.amount_discount
                
                
                # Siapkan entri untuk alokasi diskon
                discount_allocation_needed = {}
                
                # Perbedaan utama dari implementasi default:
                # Kita hanya membuat baris untuk akun diskon, tidak memodifikasi akun asli
                discount_allocation_needed_vals = discount_allocation_needed.setdefault(
                    frozendict({
                        'move_id': line.move_id.id,
                        'account_id': discount_allocation_account.id,
                        'currency_rate': line.currency_rate,
                    }),
                    {
                        'display_type': 'discount',
                        'name': _("Discount"),
                        'credit': 0.0,
                    },
                )
                # Kurangi nilai diskon pada akun diskon
                discount_allocation_needed_vals['credit'] = discounted_amount_currency
                
                # Set nilai yang dihitung
                line.discount_allocation_needed = {k: frozendict(v) for k, v in discount_allocation_needed.items()}
        return res
        
    @api.onchange('new_unit_price')
    def _onchange_new_unit_price(self):
        if self.move_type == 'in_invoice':
            self.price_unit = self.price_subtotal_bill
        else:
            self.price_unit = self.new_unit_price

    @api.onchange('product_template_id')
    def _onchange_product_template_id_set_description(self):
        for line in self:
            if line.product_template_id:
                line.description = line.product_template_id.name
                    
    @api.onchange('currency_conversion_id', 'manual_conversion')
    def _onchange_currency_conversion_id(self):
        for line in self:
            if line.currency_conversion_id:
        
                latest_rate = self.env['res.currency.rate'].search([
                    ('currency_id', '=', line.currency_conversion_id.id)
                ], order='name desc', limit=1)

                if latest_rate:
            
                    line.inverse_conv_rate = latest_rate.inverse_company_rate
                else:
                    line.inverse_conv_rate = 1.0
            else:
                line.inverse_conv_rate = 1.0

            if line.currency_conversion_id == line.company_currency_id:
                line.inverse_conv_rate = 1.0
                line.manual_conversion = False
                
        
    # @api.depends('product_template_id')
    # def _compute_product_domain(self):
    #     for record in self:
    #         domain = [('purchase_ok', '=', True)]
    #         if record.move_id.move_type == 'in_invoice':
    #             domain.append(('product_tmpl_id', '=', record.product_template_id.id))
    #         print(domain)
    #         record.product_domain = json.dumps(domain)

    @api.depends('price_total_currency_bill', 'manual_conversion', 'price_subtotal_bill', 'inverse_conv_rate', 'amount_discount')
    def _compute_price_subtotal(self):
        for record in self:
            # if record.manual_conversion:
                record.price_subtotal_bill = record.price_total_currency_bill * record.inverse_conv_rate
                # record.price_subtotal = record.price_subtotal_bill
                # record.price_total = record.price_subtotal - record.amount_discount

    # @api.depends('quantity', 'discount', 'price_unit', 'tax_ids', 'currency_id')
    # def _compute_totals(self):
    #     super(AccountMoveLine, self)._compute_totals()
    #     for record in self:
    #         record.price_subtotal_bill = record.price_total_currency_bill * record.inverse_conv_rate
    #         record.price_subtotal = record.price_subtotal_bill
    #         record.price_total = record.price_subtotal - record.amount_discount
                
            
    # @api.depends('currency_conversion_id', 'manual_conversion', 'inverse_conv_rate', 'price_total')
    # def _compute_price_total_currency(self):
    #     for record in self:
    #         if record.manual_conversion:
    #             price_total_currency = record.price_subtotal * record.inverse_conv_rate
    #         else:
    #             price_total_currency =  record.price_subtotal * record.inverse_conv_rate
    #         record.price_total_currency = record.currency_conversion_id.round(price_total_currency)
            
    @api.depends('quantity', 'new_unit_price', 'price_total_currency_bill')
    def _compute_price_total_cure(self):
        for record in self:
            record.price_total_currency_bill = record.quantity * record.new_unit_price        
        
    @api.depends('price_subtotal_bill', 'discount')
    def _compute_amount_discount(self):
        for record in self:
            record.amount_discount = record.price_subtotal_bill * record.discount / 100
    
    @api.onchange('amount_discount')
    def _inverse_amount_discount(self):
        for record in self:
            if record.move_type == 'in_invoice':
                if record.quantity and record.price_unit:
                    record.discount =  (record.amount_discount / (record.price_subtotal_bill)) * 100
                else:
                    record.discount = 0
            else:
                record.discount = 0

    @api.onchange('description')
    def _onchange_description(self):
        if self.description:
            self.name = self.description

    @api.onchange('product_template_id')
    def _onchange_product_tmpl(self):
        self.product_uom_id = self.product_template_id.uom_id.id

    @api.model
    def create(self, vals):
        if vals.get('move_id'):
            bill = self.env['account.move'].browse(vals['move_id'])
            if bill.source in ['purchase', 'fpjp'] and not self._context.get('create_non_manual') and self._context.get('skip_account_move_synchronization'):
                raise ValidationError('Tidak bisa create line jika source dari GR atau FPJP')
        res = super(AccountMoveLine, self).create(vals)
        return res

    def unlink(self):
        for rec in self:
            if rec.move_id.source == 'purchase' and not self._context.get('create_non_manual') and not self._context.get('dynamic_unlink'):
                raise ValidationError('Bill line dari GR tidak bisa dihapus')
        res = super(AccountMoveLine, self).unlink()
        return res

    @api.constrains('account_id', 'display_type')
    def _check_payable_receivable(self):
        for line in self:
            account_type = line.account_id.account_type
            if line.move_id.is_sale_document(include_receipts=True):
                if (line.display_type == 'payment_term') ^ (account_type == 'asset_receivable'):
                    raise UserError(_("Any journal item on a receivable account must have a due date and vice versa."))
            # if line.move_id.is_purchase_document(include_receipts=True):
            #     if (line.display_type == 'payment_term') ^ (account_type == 'liability_payable'):
            #         raise UserError(_("Any journal item on a payable account must have a due date and vice versa."))
    
    @api.depends('currency_conversion_id', 'balance', 'inverse_conv_rate', 'price_unit', 'quantity', 'discount')
    def _compute_amount_currency(self):
        for line in self:
            if line.currency_id != line.company_currency_id:
                return super()._compute_amount_currency()
            elif line.currency_conversion_id == line.company_id.currency_id:
                line.amount_currency = line.balance
            else:  
                line.amount_currency = line.price_subtotal_bill

    @api.onchange('amount_currency', 'currency_conversion_id','currency_id')
    def _inverse_amount_currency(self):
        res = super(AccountMoveLine, self)._inverse_amount_currency()
        for line in self:
            if line.move_type == 'in_invoice':
                line._compute_amount_currency()
        return res