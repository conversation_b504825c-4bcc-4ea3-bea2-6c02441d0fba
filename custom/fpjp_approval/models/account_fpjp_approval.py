from odoo import api, fields, models


class AccountFpjpApproval(models.Model):
    _name = 'account.fpjp.approval'
    _inherit = 'approval.history.mixin'
    _description = 'Account FPJP Approval'

    fpjp_id = fields.Many2one('account.fpjp', 'Account FPJP',
                               ondelete='cascade')
    
    reassign_employee_ids = fields.Many2many(
        'hr.employee',
        'employee_reassign_hierarchy_account_fpjp_rel',
        'account_fpjp_order_reassign_approval_id',
        'employee_id',
        string='Reassign Employees'
    )


    employee_ids = fields.Many2many(
        'hr.employee',
        'employee_approval_hierarchy_account_fpjp_rel',  # tetap
        'account_fpjp_order_approval_id',
        'employee_id',
        string='Employees'
    )

    approval_employee_ids = fields.Many2many(
        'hr.employee',
        'approval_employee_hierarchy_account_fpjp_rel',
        'account_fpjp_order_approval_approval_id',
        'approval_employee_id',
        string='Approval Employees'
    )

    state = fields.Selection([
        ('in_progress', 'In Progress'),
        ('approve', 'Approved'),
        ('reject', 'Rejected'),
        ('return', 'Return')
    ], 'Approval Status', copy=False)
    
class AccountFpjpApprovalDetail(models.Model):
    _name = 'account.fpjp.approval.detail'
    _inherit = 'approval.history.detail.mixin'
    _description = 'Account FPJP'

    fpjp_id = fields.Many2one('account.fpjp', 'Assesment Purchase',
                               ondelete='cascade')

class AccountFpjpReassign(models.Model):
    _name = 'account.fpjp.reassign'
    _description = 'Account FPJP Reassign'
    

    fpjp_id = fields.Many2one('account.fpjp', 'Account FPJP',
                               ondelete='cascade')
    from_employee_id = fields.Many2one('hr.employee', string='From Employee')
    to_employee_id = fields.Many2one('hr.employee', string='To Employee') 
