from odoo import _, api, fields, models


class StockHistoryApproval(models.Model):
    _name = 'stock.history.approval'
    _inherit = 'approval.history.mixin'
    _description = 'Stock Picking Approval'

    stock_picking_id = fields.Many2one(string='GR Number',comodel_name='stock.picking')
    employee_ids = fields.Many2many(
        string='Employees',
        comodel_name='hr.employee',
        relation='history_employee_rel',
        column1='stock_history_approval_id',
        column2='hr_employee_id',
    )
    reassign_employee_ids = fields.Many2many(
        string='Reassign Employees',
        comodel_name='hr.employee',
        relation='history_employee_reassign_rel',
        column1='stock_history_approval_id',
        column2='hr_employee_id',
    )
    approval_employee_ids = fields.Many2many(
        string='Employees',
        comodel_name='hr.employee',
        relation='history_employee_approval_rel',
        column1='stock_history_approval_id',
        column2='hr_employee_id',
    )
    
