import logging
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

from odoo import SUPERUSER_ID, api, fields, models, _
from odoo.exceptions import ValidationError, UserError

STATE = [
    ('draft', "Draft"),
    ('submit', "Submited"),
    ('cancel', "Cancelled"),
]

class BiddingAgreement(models.Model):
    _name = 'bidding.agreement'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Bidding Agreement'

    active = fields.Boolean(
        string="Active",
        help="Indicates whether this record is active. If disabled, the record will not appear in lists but will remain in the system.",
        copy=False, default=True, index=True, tracking=True)
    name = fields.Char(
        string="MPA Document",
        help="Document number or reference related to the bidding agreement.", 
        copy=False,
        default=lambda self: _('New'))
    agreement_type = fields.Selection(
        selection=[('new_agreement', 'New Agreement'), ('amandement', 'Amandement')],
        string="Agreement Type",
        copy=False, 
        index=True,
        help="The type of agreement process, such as New Agreement or Amandement.")
    agreement_reference_id = fields.Many2one('bidding.agreement', string='Agreement Reference', domain=[('state', '=', 'submit'), ('agreement_type', '=', 'new_agreement')], copy=False)
    agreement_name = fields.Char(
        string="Agreement Number",
        help="Agreement name related to the bidding agreement.", 
        copy=False)
    agreement_id = fields.Many2one(
        comodel_name="bidding.agreement", 
        string="MPA", 
        copy=False, 
        index=True,
        help="Reference to the related agreement record.")
    bidding_id = fields.Many2one(
        comodel_name="bidding.bidding", 
        string="Bidding Number", 
        copy=False, 
        index=True,
        related='negotiation_id.bidding_id',
        store=True,
        help="Reference to the related bidding record.")
    negotiation_id = fields.Many2one(
        comodel_name="bidding.negotiation", 
        string="Negotiation Number", 
        copy=False, 
        index=True,
        help="Reference to the related negotiation record.")
    bidding_description = fields.Char(
        string="Bidding Description",
        related='negotiation_id.bidding_description',
        store=True,
        help="Description of the bidding related to this record.", 
        copy=False)
    agreement_template = fields.Selection(
        selection=[('purchase_agreement', 'Master Purchase Agreement'), ('purchase_template', 'Purchase Template')],
        string="Agreement Template",
        copy=False, 
        index=True,
        default='purchase_agreement',
        help="The template of agreement process, such as Master Purchase Agreement or Purchase Template.")
    partner_id = fields.Many2one(
        comodel_name='res.partner',
        string="Vendor",
        help="Select the vendor associated with this contact.",
        copy=False, index=True, domain="[('id', 'in', available_partner_ids)]",)
    available_partner_ids = fields.Many2many(
        comodel_name='res.partner',
        string="Available Partners", copy=False)
    buyer_id = fields.Many2one(
        comodel_name='hr.employee', 
        string="Buyer", 
        help="The buyer responsible for managing this agreement.")
    start_date = fields.Date(
        string="Start Date",
        default=fields.Date.context_today,
        help="The date when the agreement becomes effective. Used to define the validity period.")
    end_date = fields.Date(
        string="End Date",
        help="The date when the agreement expires. After this date, the agreement is no longer valid.")
    vendor_status = fields.Selection(
        selection=[
            ('draft', 'Draft'),
            ('submit', 'Pending Approval Prospective'),
            ('prospective', 'Prospective'),
            ('duediligence', 'Due Diligence'),
            ('spendauthorized', 'Spend Authorized')
        ],
        string="Vendor Status",
        help="Indicates whether the vendor is fully authorized for spending or is still a prospective supplier.")
    due_diligence_status = fields.Selection(
        selection=[
            ('yes', 'Yes'),
            ('no', 'No')
        ],
        string="Due Diligence Status",
        help="Specifies whether the vendor has passed due diligence checks.")
    attachment_ids = fields.Many2many(
        comodel_name='ir.attachment',
        string="Attachments", copy=False)
    unit_id = fields.Many2one(
        comodel_name='hr.department',
        string="Unit",
        context={'hierarchical_naming': False},
        help="The unit or department that submitted the agreement.")
    group_id = fields.Many2one(
        comodel_name='hr.department',
        string="Group",
        context={'hierarchical_naming': False},
        help="The group or category of the agreement.")
    unit = fields.Char(
        string="Unit",
        help="The unit or department that submitted the agreement.")
    group = fields.Char(
        string="Group",
        help="The group or category of the agreement.")
    payment_term_id = fields.Many2one(
        comodel_name='account.payment.term',
        string='TOP',
        copy=False)
    is_purchase_request = fields.Boolean(
        string="Is Purchase Request",
        related="bidding_id.is_purchase_request", store=True,
        help="Check this if this record is associated with a purchase request.",
        copy=False)
    purchase_request_exist = fields.Boolean('Purchase Request Exist', default=False)
    requestor_id = fields.Many2one(
        comodel_name='hr.employee', 
        string="Requested By", 
        help="The person who requested the agreement.")
    company_currency_id = fields.Many2one(
        string='Company Currency',
        related='company_id.currency_id', readonly=True,
        help="The default currency of the company.")
    company_id = fields.Many2one(
        comodel_name="res.company",
        string="Company",
        help="Specify the company associated with this transaction.",
        default=lambda self: self.env.company,
        copy=False)
    state = fields.Selection(
        selection=STATE,
        string="Status",
        readonly=True, 
        copy=False, 
        index=True,
        default='draft',
        help="The status of the agreement, such as 'Draft', 'Submit', or other states.")
    line_ids = fields.One2many(
        comodel_name="bidding.agreement.line", 
        inverse_name="agreement_id", 
        string="Products")
    amandement_ids = fields.One2many(
        comodel_name="bidding.agreement", 
        inverse_name="agreement_id", 
        string="Amandements")
    notes = fields.Text(
        string="Notes",
        help="Additional notes or remarks related to this record.")
    is_award = fields.Boolean(compute='_compute_is_award', string='Award')
    

    display_name = fields.Char(compute='_compute_display_name', store=False)
    
    @api.depends('name', 'agreement_name')
    @api.depends_context('show_agreement_name')
    def _compute_display_name(self):
        for record in self:
            record.display_name = record.agreement_name or record.name
            # if self._context.get('show_agreement_name'):
            #     record.display_name = record.agreement_name or record.name
            # else:
            #     record.display_name = record.name

    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=100, name_get_uid=None):
        args = args or []
        if self.env.context.get('show_agreement_name'):
            domain = [('agreement_name', operator, name)] + args
        else:
            domain = [('name', operator, name)] + args
        recs = self.search_fetch(domain, ['display_name'], limit=limit) 

        # Format the result: list of (id, display_name) tuples 
        return [(rec.id, rec.display_name) for rec in recs]    
    @api.depends('state')
    def _compute_is_award(self):
        for record in self:
            record.is_award = record.state == 'submit'

    # def action_create_amandement(self):
    #     self.ensure_one()
    #     return {
    #         'name': _("MPA"),
    #         'type': 'ir.actions.act_window',
    #         'res_model': 'bidding.agreement',
    #         'view_mode': 'form',
    #         'context': {
    #             'default_agreement_id': self.id,
    #             'default_negotiation_id': self.negotiation_id.id,
    #             'default_bidding_id': self.bidding_id.id,
    #             'default_agreement_name': self.name,
    #             'default_agreement_reference': self.agreement_name,
    #             'default_agreement_type': 'amandement',
    #         },
    #         'target': 'current',
    #     }

    def action_create_amandement(self):
        self.ensure_one()

        # Cek apakah sudah ada amandement dari agreement ini
        existing_amandements = self.env['bidding.agreement'].search([
            ('agreement_id', '=', self.id),
        ], order='id desc', limit=1)

        if existing_amandements and existing_amandements.state != 'submit':
            raise UserError(_("Amandement cannot be created because the existing amandement is not in 'Submit' state."))

        # Pakai source record
        source_record = existing_amandements if existing_amandements else self

        line_vals = []
        for line in source_record.line_ids:
            line_vals.append((0, 0, {
                'product_tmpl_id': line.product_tmpl_id.id,
                'product_variant_id': line.product_variant_id.id,
                'product_uom_id': line.product_uom_id.id,
                'description': line.description,
                'currency_id': line.currency_id.id,
                'unit_price': line.unit_price,
            }))

        amandement = self.env['bidding.agreement'].create({
            'agreement_id': self.id,
            'negotiation_id': source_record.negotiation_id.id,
            'bidding_id': source_record.bidding_id.id,
            'is_purchase_request': source_record.bidding_id.is_purchase_request,
            'agreement_name': source_record.name,
            'agreement_reference': source_record.agreement_name,
            'agreement_type': 'amandement',
            'bidding_description': source_record.bidding_description,
            'partner_id': source_record.partner_id.id,
            'buyer_id': source_record.buyer_id.id,
            'vendor_status': source_record.vendor_status,
            'due_diligence_status': source_record.due_diligence_status,
            'requestor_id': source_record.requestor_id.id,
            'unit_id': source_record.unit_id.id,
            'group_id': source_record.group_id.id,
            'payment_term_id': source_record.payment_term_id.id,
            'start_date': source_record.start_date,
            'end_date': source_record.end_date,
            'attachment_ids': source_record.attachment_ids.ids,
            'line_ids': line_vals,
        })

        return {
            'name': _("MPA"),
            'type': 'ir.actions.act_window',
            'res_model': 'bidding.agreement',
            'res_id': amandement.id,
            'view_mode': 'form',
            'target': 'current',
        }


    def get_sequence(self, name=False, obj=False, pref=False, context=None):
        sequence_id = self.env['ir.sequence'].search([
            ('name','=',name),
            ('code','=',obj),
            ('implementation','=','standard'),
            ('prefix','=',pref)
        ])
        if not sequence_id :
            sequence_id = self.env['ir.sequence'].sudo().create({
                'name': name,
                'code': obj,
                'implementation': 'standard',
                'prefix': pref,
                'padding': 3
            })
        return sequence_id.next_by_id()

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if vals.get('name', 'New') == 'New':
                seq_date = None
                if 'start_date' in vals:
                    seq_date = fields.Datetime.context_timestamp(self, fields.Datetime.to_datetime(vals['start_date']))
                vals['name'] = self.get_sequence(
                    'Sequence MPA New Agreement',
                    'bidding.agreement.mpa',
                    'MPA/%(year)s/'
                )
                # Jika bukan amandement, pakai default sequence
                # if vals.get('agreement_type') != 'amandement':
                #     # vals['name'] = self.env['ir.sequence'].next_by_code('bidding.agreement.mpa', sequence_date=seq_date) or '/'
                #     vals['name'] = self.get_sequence(
                #         'Sequence MPA New Agreement',
                #         'bidding.agreement.mpa',
                #         'MPA/%(year)s/'
                #     )
                # else:
                #     # Jika amandement, generate nama berdasarkan agreement induknya
                #     agreement_id = vals.get('agreement_id')
                #     agreement_name = '/'
                #     if agreement_id:
                #         agreement = self.env['bidding.agreement'].browse(agreement_id)
                #         agreement_name = agreement.name or '/'
                #     vals['name'] = self.get_sequence(
                #         f'{agreement_name} Sequence MPA Amandement',
                #         'bidding.agreement.amandement',
                #         f'{agreement_name} - '
                #     )

        return super(BiddingAgreement, self).create(vals_list)


    @api.onchange('negotiation_id')
    def _onchange_available_partners(self):
        if self.bidding_id and self.negotiation_id:
            self.available_partner_ids = [(6, 0, [self.negotiation_id.partner_id.id])]
        else:
            self.available_partner_ids = [(6, 0, [])]

    @api.onchange('unit_id','group_id')
    def onchange_unit_group(self):
        self.unit = self.unit_id.name
        self.group = self.group_id.parent_id.name

    @api.onchange('negotiation_id')
    def onchange_negotiation(self):
        self.partner_id = self.negotiation_id.partner_id.id
        self.bidding_id = self.negotiation_id.bidding_id.id
        self.is_purchase_request = self.bidding_id.is_purchase_request
        self.bidding_description = self.negotiation_id.bidding_description
        self.buyer_id = self.negotiation_id.buyer_id.id
        self.requestor_id = self.negotiation_id.requestor_id.id
        self.unit_id = self.negotiation_id.unit_id.id
        self.group_id = self.negotiation_id.group_id.id
        self.vendor_status = self.negotiation_id.vendor_status
        self.due_diligence_status = self.negotiation_id.due_diligence_status
        self.payment_term_id = self.negotiation_id.payment_term_id.id
        self.onchange_unit_group()

        line_vals = [(5, 0, 0)]
        for line in self.negotiation_id.line_ids:
            if line.is_award_line:
                # line.product_tmpl_id.with_context(from_nego=True).create_variant()
                new_variant = self.env['product.product'].search([('variant_id.name', '=', line.propose_variant), ('product_tmpl_id', '=', line.product_tmpl_id.id)], limit=1)
                # new_variant = line.product_tmpl_id.product_line_ids.filtered(lambda x: x.name == line.propose_variant)
                # new_variant_id = False
                # if new_variant:
                #     new_variant_id = new_variant.id

                line_vals.append((0, 0, {
                    'product_tmpl_id': line.product_tmpl_id.id,
                    'product_variant_id': new_variant.id if new_variant else line.product_variant_id.id,
                    'product_uom_id': line.product_uom_id.id,
                    'description': line.description,
                    'currency_id': line.currency_id.id,
                    'unit_price': line.unit_price,
                    'qty': line.quantity if line.is_percentage else 1,
                }))

        self.line_ids = line_vals

    def unlink(self):
        for record in self:
            if record.state != 'draft':
                raise UserError("You can only delete records with status 'Draft'.")
        return super().unlink()

    def action_draft(self):
        for record in self:
            state_label = dict(self._fields['state'].selection).get(self.state, '')
            if record.state == 'draft':
                raise UserError(_("Data ini sudah dalam status '%s'. Harap muat ulang halaman untuk melihat status yang diperbarui.") % state_label)
            record.state = 'draft'

    def action_submit(self):
        for record in self:
            state_label = dict(self._fields['state'].selection).get(self.state, '')
            if record.state != 'draft':
                raise UserError(_("Data ini sudah dalam status '%s'. Harap muat ulang halaman untuk melihat status yang diperbarui.") % state_label)
            record.state = 'submit'

    def action_cancel(self):
        for record in self:
            state_label = dict(self._fields['state'].selection).get(self.state, '')
            if record.state == 'cancel':
                raise UserError(_("Data ini sudah dalam status '%s'. Harap muat ulang halaman untuk melihat status yang diperbarui.") % state_label)
            record.state = 'cancel'

    def action_open_amandement(self):
        return {
            'type': 'ir.actions.act_window',
            'name': 'Amandement',
            'res_model': 'bidding.agreement',
            'view_mode': 'list,form',
            'domain': [('agreement_id', '=', self.id),('agreement_type','=','amandement')],
            'context': {'create': False},
            'target': 'current',
        }

    def action_open_mpa(self):
        return {
            'type': 'ir.actions.act_window',
            'name': 'MPA',
            'res_model': 'bidding.agreement',
            'view_mode': 'list,form',
            'domain': [('id', '=', self.agreement_id.id),('agreement_type','=','new_agreement')],
            'context': {'create': False},
            'target': 'current',
        }

    def action_open_po(self):
        self.ensure_one()
        return {
            'name': _("Purchase Orders"),
            'type': 'ir.actions.act_window',
            'res_model': 'purchase.order',
            'view_mode': 'list,form',
            'domain': [('agreement_id', '=', self.id)],
       }

    def action_open_pr(self):
        self.ensure_one()
        return {
            'name': _("Purchase Request"),
            'type': 'ir.actions.act_window',
            'res_model': 'purchase.request',
            'view_mode': 'list,form',
            'domain': [('agreement_id', '=', self.id)],
       }
  




