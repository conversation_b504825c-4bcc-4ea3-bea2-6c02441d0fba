<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

         <!-- Formview MPA-->
        <record id="ap_agreement_view_form" model="ir.ui.view">
            <field name="name">MPA</field>
            <field name="model">bidding.agreement</field>
            <field name="arch" type="xml">
                <form string="MPA">
                    <field name="is_award" invisible="1"/>
                    <header>
                        <button name="action_submit" string="Submit" type="object" class="oe_highlight"
                                invisible="state != 'draft'"/>
                        <!-- <button name="action_create_amandement" string="Amandement" type="object" invisible="state not in ('submit') or agreement_type == 'amandement'" confirm="Apakah Anda yakin ingin membuat Amandement atas MPA ini?"/> -->
                        <button name="action_create_amandement" string="Amandement" type="object" invisible="1" confirm="Apakah Anda yakin ingin membuat Amandement atas MPA ini?"/>
                        <button name="action_draft" string="Set to Draft" type="object" invisible="state in ('draft')"/>
                        <button name="action_cancel" string="Cancel" type="object" invisible="state in ('cancel','submit')"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,submit"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button class="oe_stat_button" name="action_open_amandement" invisible="not amandement_ids"
                                    type="object" icon="fa-bars">
                                <div class="o_stat_info">
                                    <span class="o_stat_text">
                                        Amandement
                                    </span>
                                </div>
                            </button>
                            <button class="oe_stat_button" name="action_open_mpa" invisible="agreement_type != 'amandement'"
                                    type="object" icon="fa-bars">
                                <div class="o_stat_info">
                                    <span class="o_stat_text">
                                        MPA
                                    </span>
                                </div>
                            </button>
                            <button class="oe_stat_button" name="action_open_pr" type="object" icon="fa-bars" invisible="is_purchase_request == True">
                                <div class="o_stat_info">
                                    <span class="o_stat_text">
                                        Purchase Request
                                    </span>
                                </div>
                            </button>
                            <button class="oe_stat_button" name="action_open_po" type="object" icon="fa-bars">
                                <div class="o_stat_info">
                                    <span class="o_stat_text">
                                        Purchase Orders
                                    </span>
                                </div>
                            </button>
                        </div>

                        <div class="oe_title">
                            <h1><field name="name" readonly="1"/></h1>
                        </div>
                        <group>
                            <group>
                                <field name="is_purchase_request" invisible="1" />
                                <field name="agreement_type" required="1" readonly="state != 'draft'"/>
                                <field name="agreement_reference_id" options="{'no_create': True}" required="agreement_type == 'amandement'" invisible="agreement_type != 'amandement'" readonly="state != 'draft'"/>
                                <field name="agreement_id" invisible="1"/>
                                <field name="agreement_name" required="1" readonly="state != 'draft'"/>
                                <field name="negotiation_id" required="1" domain="[('bidding_type', '=', 'rfp')]" readonly="1"/>
                                <field name="bidding_id" required="1" force_save="1" readonly="1"/>
                                <field name="bidding_description" readonly='1' force_save="1"/>
                                <field name="agreement_template" required="1" readonly="state != 'draft'"/>
                                <field name="partner_id" readonly="1" force_save="1"/>
                                <field name="available_partner_ids" widget="many2many_tags" invisible="1"/>
                                <field name="buyer_id" readonly="1" force_save="1"/>
                            </group>
                            <group>
                                <label for="start_date" string="Agreement Validity"/>
                                <div>
                                    <field name="start_date" class="oe_inline" required="1" readonly="state != 'draft'"/> s/d 
                                    <field name="end_date" class="oe_inline" required="1" readonly="state != 'draft'"/>
                                </div>
                                <field name="vendor_status" readonly="1" force_save="1"/>
                                <field name="due_diligence_status" readonly="1" force_save="1"/>
                                <field name="attachment_ids" widget="many2many_binary" required="1" readonly="state != 'draft'"/>
                                <field name="requestor_id" readonly="1" force_save="1"/>
                                <field name="unit_id" readonly="1" force_save="1"/>
                                <field name="group_id" readonly="1" force_save="1"/> 
                                <field name="payment_term_id" readonly="state != 'draft'" required="1"/>
                                <field name="amandement_ids" invisible="1" widget="many2many_tags"/>
                            </group>
                        </group>
                        <notebook>
                        <page string="Products">
                            <field name="line_ids" readonly="state != 'draft'">
                                <list editable="bottom" create="false">
                                    <field name="product_tmpl_id" readonly='1' force_save='1' required="1"/>
                                    <field name="product_variant_id" readonly='1' force_save='1' required="0" domain="[('product_tmpl_id', '=', product_tmpl_id), ('is_product_variant', '=', True)]" />
                                    <field name="description" readonly='1' force_save='1' required="1"/>
                                    <field name="currency_id" readonly='1' force_save='1' required="0"/>
                                    <field name="company_currency_id" readonly="1" column_invisible="1"/>
                                    <field name="company_id" readonly="1" force_save='1' column_invisible="1"/>
                                    <field name="qty" readonly='1' force_save='1' required="1"/>
                                    <field name="product_uom_id" readonly='1' force_save='1' required="1"/>
                                    <field name="is_percentage" force_save="1" column_invisible="1"/>
                                    <field name="uom_type" required="0" column_invisible="1"/>
                                    <field name="unit_price" readonly='1' force_save='1' />
                                    <!-- <field name="is_manajemen_fee" invisible="is_percentage == True"/> -->
                                    <field name="is_active" readonly="parent.state != 'draft'" />
                                </list>
                                <form string="Products">
                                    <field name="product_tmpl_id" required="1"/>
                                    <field name="product_variant_id" required="0" domain="[('product_tmpl_id', '=', product_tmpl_id), ('is_product_variant', '=', True)]" />
                                    <field name="description" required="1"/>
                                    <group>
                                        <group>
                                            <field name="currency_id" required="1"/>
                                            <field name="company_currency_id" readonly="1" invisible="1"/>
                                            <field name="company_id" readonly="1" invisible="1"/>
                                            <field name="product_uom_id" required="1"/>
                                            <field name="is_percentage" force_save="1" column_invisible="1"/>
                                            <field name="uom_type" required="0" invisible="1"/>
                                            <field name="unit_price" required="1"/>
                                        </group>
                                        <group>
                                            <!-- <field name="is_manajemen_fee" invisible="is_percentage == True"/> -->
                                            <field name="is_active"/>
                                        </group>
                                    </group>
                                </form>
                            </field>

                            <group>
                                <field name="notes" placeholder="Notes..." nolabel="1" readonly="state != 'draft'"/>
                            </group>
                            <div class="clearfix"/>

                        </page>

                    </notebook>
                </sheet>
                <chatter/>
                </form>
            </field>
        </record>

         <!-- Listview Agreement-->
        <record id="ap_agreement_view_list" model="ir.ui.view">
            <field name="name">MPA</field>
            <field name="model">bidding.agreement</field>
            <field name="arch" type="xml">
                <list string="MPA" decoration-info="state == 'draft'"
                      decoration-muted="state == 'cancel'">
                    <field name="name" string="MPA Number"/>
                    <field name="bidding_id"/> 
                    <field name="negotiation_id"/> 
                    <field name="partner_id"/> 
                    <field name="agreement_name" />
                    <field name="agreement_reference_id" />
                    <field name="state" string="Negotiation Status"/>
                    <field name="is_award" />
                </list>
            </field>
        </record>

         <!-- Search MPA-->
        <record id="ap_agreement_view_search" model="ir.ui.view">
            <field name="name">bidding.agreement.search</field>
            <field name="model">bidding.agreement</field>
            <field name="arch" type="xml">
                <search string="Search MPA">
                    <separator string="General" />
                    <field name="agreement_name"/>
                    <field name="name"/>
                    <field name="agreement_type"/>
                    <field name="state"/>

                    <separator string="Dates" />
                    <field name="start_date"/>
                    <field name="end_date"/>

                    <separator string="Request Details" />
                    <field name="buyer_id"/>
                    <field name="requestor_id"/>
                    <field name="unit_id"/>
                    <field name="group_id"/>

                    <separator string="Filters" />
                    <filter name="state_draft" string="Draft" domain="[('state', '=', 'draft')]" />
                    <filter name="state_submit" string="Submit" domain="[('state', '=', 'submit')]" />
                    <filter name="state_cancel" string="Cancelled" domain="[('state', '=', 'cancel')]" />

                    <separator string="Grouping" />
                    <group expand="0" string="Group By">
                        <filter name="group_by_bidding" string="Bidding" domain="[]" context="{'group_by': 'bidding_id'}"/>
                        <filter name="group_by_agreement_type" string="Agreement Type" domain="[]" context="{'group_by': 'agreement_type'}"/>
                        <filter name="group_by_buyer_id" string="Buyer" domain="[]" context="{'group_by': 'buyer_id'}"/>
                        <filter name="group_by_requested_by" string="Requested By" domain="[]" context="{'group_by': 'requestor_id'}"/>
                        <filter name="group_by_unit" string="Unit" domain="[]" context="{'group_by': 'unit_id'}"/>
                        <filter name="group_by_group" string="Group" domain="[]" context="{'group_by': 'group_id'}"/>
                        <filter name="group_by_state" string="Status" domain="[]" context="{'group_by': 'state'}"/>
                    </group>
                </search>
            </field>
        </record>


        <!-- Action MPA-->
    <record id="action_agreement" model="ir.actions.act_window">
        <field name="name">MPA</field>
        <field name="res_model">bidding.agreement</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="ap_agreement_view_search"/>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'list', 'view_id': ref('ap_agreement_view_list')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('ap_agreement_view_form')})]"/>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
              Click to add a new agreement.
          </p>
        </field>
    </record>


    </data>
</odoo>