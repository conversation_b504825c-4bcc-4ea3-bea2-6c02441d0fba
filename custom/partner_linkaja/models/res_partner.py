from odoo import models, fields, api
from odoo.exceptions import UserError, ValidationError


class ResPartner(models.Model):
    _inherit = 'res.partner'
    

    is_vendor = fields.Boolean('Is Vendor', default=False)
    vendor_state = fields.Selection([
        ('draft', 'Draft'),
        ('submit', 'Pending Approval'),
        ('prospective', 'Prospective'),
        ('duediligence', 'Due Diligence'),
        ('spendauthorized', 'Spend Authorized')
    ], string='Vendor Status',default='draft', readonly=True, index=True, copy=False)
    
    busines_category = fields.Many2one('supplier.business.category', string="Kategori Badan Usaha")
    partner_type = fields.Selection([
        ('procurement', 'Procurement'),
        ('nonprocurement', 'Non-Procurement'),
    ], string="Type")

    supplier_type = fields.Selection([
        ('related', 'Related'),
        ('nonrelated', 'Non-Related'),
    ], string="Supplier Type")
    supplier_type_id = fields.Many2one('supplier.type', string='Supplier Type')
    tax_document = fields.Selection([
        ('ktp', 'KTP'),
        ('npwp', 'NPWP'),
        ('others', 'Others (ex. COR / DGT)'),
    ], string="Tax Document")
    tax_organization_type = fields.Many2one('tax.organization.type', string="Tax Organization Type")
    npwp_number = fields.Char(string="Tax Registration Number")
    ktp_number = fields.Char(string="KTP Number")
    cor_number = fields.Char(string="COR/DGT Number")
    payment_term_id = fields.Many2one('account.payment.term', string="Term of Payment")
    created_by = fields.Many2one('res.users', string="Created By", default=lambda self: self.env.user)
    promote_spend_authorized = fields.Boolean(string="Promote Spend Authorized")
    
    tags = fields.Many2many('res.partner.category', 'res_partner_category_rel_custom', 'partner_id', 'category_id', string='Tags')
    
    contact_supplier_ids = fields.One2many('contact.supplier', 'partner_id', string="Contact Supplier")  
    bank_supplier_ids = fields.One2many('bank.account.supplier', 'partner_id', string="Bank Accounts")
    approval_ids = fields.One2many('res.partner.approval', 'partner_id', string="Approval Prospective")
    approval_detail_ids = fields.One2many("approval.prospective.detail", inverse_name="partner_id", string="Approval Details")
    attachment_ids = fields.One2many('res.partner.attachment', 'partner_id', string="Attachments")

    link_vp = fields.Char('Link VP')
    
    procurement_count = fields.Integer(compute='_compute_procurement_count')
    
    fax = fields.Char(string='FAX')
    first_name = fields.Char(string="First Name")
    middle_name = fields.Char(string="Middle Name")
    last_name = fields.Char(string="Last Name")

    vp_account_email = fields.Char(string="Email", help="Virtual Account Email")
    vp_account_password = fields.Char(string="Password", help="Virtual Account Password")
    can_edit_due_diligence = fields.Boolean(compute='_compute_can_edit_due_diligence', store=False)
    procurement_unit = fields.Boolean('Procurement Unit', default=False, compute='_compute_edit_rights', store=False)
    fraud_unit = fields.Boolean('Fraud Management Unit', default=False, compute='_compute_edit_rights', store=False)
    compilance_unit = fields.Boolean('Compliance Unit', default=False, compute='_compute_edit_rights', store=False)
    is_all_detail_filled = fields.Boolean(string='Semua Detail Terisi', compute='_compute_is_all_detail_filled', store=True)

    @api.depends('approval_detail_ids.procurement_unit',
             'approval_detail_ids.fraud_unit',
             'approval_detail_ids.compilance_unit')
    def _compute_is_all_detail_filled(self):
        for partner in self:
            units = partner.approval_detail_ids
            has_proc = any(units.mapped('procurement_unit'))
            has_fraud = any(units.mapped('fraud_unit'))
            has_compliance = any(units.mapped('compilance_unit'))

            partner.is_all_detail_filled = has_proc and has_fraud and has_compliance

    
    @api.constrains('is_all_detail_filled')
    def _check_and_trigger_approve(self):
        for partner in self:
            if partner.is_all_detail_filled:
                partner._approve()

    # @api.depends_context('uid')
    def _compute_edit_rights(self):
        user = self.env.user
        employee = user.sudo().employee_id
        dept = employee.department_id.name if employee.department_id else ''
        for rec in self:
            rec.procurement_unit = dept == 'Procurement Unit'
            rec.fraud_unit = dept == 'Fraud Management Unit'
            rec.compilance_unit = dept == 'Compliance Unit'
    # procurement_unit = fields.Boolean('Procurement Unit', default=False)
    # fraud_unit = fields.Boolean('Fraud Management Unit', default=False)
    # compilance_unit = fields.Boolean('Compliance Unit', default=False)

    def _compute_can_edit_due_diligence(self):
        current_user = self.env.user
        employee = current_user.sudo().employee_id
        allowed_departments = ['Procurement Unit', 'Fraud Management Unit', 'Compliance Unit']
        for rec in self:
            rec.can_edit_due_diligence = employee.department_id.name in allowed_departments if employee.department_id else False

    @api.onchange('first_name', 'middle_name', 'last_name')
    def _onchange_split_name(self):
        for rec in self:
            # Gabungkan ke field name standar Odoo
            name_parts = filter(None, [rec.first_name, rec.middle_name, rec.last_name])
            rec.name = ' '.join(name_parts)


    @api.onchange('supplier_type_id')
    def _onchange_only_supplier_type(self):
        if self.env.context.get('vendor_view'):
            self.property_account_receivable_id = self.supplier_type_id.property_account_receivable_id.id
            self.property_account_payable_id = self.supplier_type_id.property_account_payable_id.id

    def _onchange_supplier_type(self):
        self.property_account_receivable_id = self.supplier_type_id.property_account_receivable_id.id
        self.property_account_payable_id = self.supplier_type_id.property_account_payable_id.id

    def _compute_procurement_count(self):
        for partner in self:
            partner.procurement_count = self.env['procurement.update.data'].search_count([('partner_id', '=', partner.id)])
            
    def action_open_procurement_updates(self):
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': 'Update History',
            'res_model': 'procurement.update.data',
            'domain': [('partner_id', '=', self.id)],
            'view_mode': 'list,form',
            'context': {'default_partner_id': self.id},
        }
            
    def action_open_procurement_form(self):
        # if self.partner_type == 'nonprocurement':
        #     raise UserError("Update-data belum tersedia untuk Non-Procurement.")
        
        return {
            'type': 'ir.actions.act_window',
            'name': 'Update Procurement Data',
            'res_model': 'procurement.update.data',
            'view_mode': 'form',
            'view_id': self.env.ref('partner_linkaja.procurement_view_form').id,
            'context': {
                'default_partner_id': self.id,
                'default_name': self.name,
            }
        }
        
    def action_submit(self):
        self.vendor_state = 'submit'
        self._check_validation()

    def action_supplier_draft(self):
        self.vendor_state = 'draft'
    
    def action_prospective(self):
        self.vendor_state = 'prospective'
                
    def action_spendauthorized(self):
        self.vendor_state = 'duediligence'

    def approve(self):
        self.vendor_state = 'spendauthorized'
        
    def action_open_self_service_popup(self):
        if self.partner_type == 'nonprocurement':
            raise UserError("Self-Service tidak tersedia untuk Non-Procurement.")
        
        first_child = self.child_ids[0] if self.child_ids else False
        self.link_vp = 'link/to/vp'
        
                
        return {
            'name': 'Initial Data',
            'type': 'ir.actions.act_window',
            'res_model': 'partner.vp',
            'view_mode': 'form',
            'view_id': self.env.ref('partner_linkaja.partner_vp_view_form').id,
            'target': 'new',
            'context': {
                'default_partner_id': self.id,
                'default_name': self.name,
                'default_tax_organization_type': self.tax_organization_type.id,
                'default_busines_category': self.busines_category.id,
                # 'default_contact_name': first_child.name if first_child else '',
                # 'default_contact_email': first_child.email if first_child else '',
                # 'default_contact_phone': first_child.phone if first_child else '',
            },
        }


    
    @api.onchange('tax_organization_type')
    def _onchange_tax_type(self):
        perseroan_list = [
            (0, 0, {"req_document": "Dokumen Profil Perusahaan / Company Profile", "availability": "wajib"}),
            (0, 0, {"req_document": "Halaman depan buku Tabungan / Rekening Koran / Surat Referensi Bank atas nama Perusahaan", "availability": "wajib"}),
            (0, 0, {"req_document": "Akta Pendirian Perseroan", "availability": "wajib"}),
            (0, 0, {"req_document": "Akta Perubahan Terakhir Yang Memuat Susunan Direksi", "availability": "wajib"}),
            (0, 0, {"req_document": "SK KemenHumHAM Perihal PENGESAHAN Badan Hukum Perseroan / SK Terdaftar KemenHumHam", "availability": "wajib"}),
            (0, 0, {"req_document": "SK KemenHumHAM Perihal PERSETUJUAN/PEMBERITAHUAN Badan Hukum Perseroan", "availability": "wajib"}),
            (0, 0, {"req_document": "Kartu Tanda Penduduk (KTP) penanggung jawab perusahaan", "availability": "wajib"}),
            (0, 0, {"req_document": "Nomor Induk Berusaha (NIB) dari OSS / Tanda Daftar Perusahaan (TDP)", "availability": "wajib"}),
            (0, 0, {"req_document": "Surat Ijin Usaha Perdagangan / Izin Usaha OSS", "availability": "wajib"}),
            (0, 0, {"req_document": "Surat Izin Gangguan dan atau Izin Tempat Usaha / Domisili / Izin Lokasi OSS", "availability": "optional"}),
            (0, 0, {"req_document": "Nomor Pokok Wajib Pajak dan Tanggal Terdaftar", "availability": "wajib"}),
            (0, 0, {"req_document": "Surat Pengukuhan Pengusaha Kena Pajak / Surat Pernyataan Non PKP", "availability": "wajib"}),
            (0, 0, {"req_document": "Surat Keterangan Terdaftar dari DirJend Pajak", "availability": "optional"}),
            (0, 0, {"req_document": "ISO Certification", "availability": "optional"}),
        ]

        overseas_list = [
            (0, 0, {"req_document": "Company Profile", "availability": "wajib"}),
            (0, 0, {"req_document": "Letter of Integrity", "availability": "wajib"}),
            (0, 0, {"req_document": "Copy of Bank Account Statement or Bank Reference", "availability": "wajib"}),
            (0, 0, {"req_document": "Certificate of Domicile or Company Establishment Document", "availability": "wajib"}),
            (0, 0, {"req_document": "Tax Registration Document", "availability": "wajib"}),
            (0, 0, {"req_document": "ID Card or Passport or Statement Letter of the responsible person in your company", "availability": "wajib"}),
            (0, 0, {"req_document": "ISO Certification", "availability": "optional"}),
        ]

        perseorangan_list = [
            (0, 0, {"req_document": "Dokumen Profil Perusahaan / Company Profile", "availability": "optional"}),
            (0, 0, {"req_document": "Halaman depan buku Tabungan / Rekening Koran / Surat Referensi Bank atas nama Perusahaan", "availability": "wajib"}),
            (0, 0, {"req_document": "Akta Pendirian Perseroan", "availability": "optional"}),
            (0, 0, {"req_document": "Akta Perubahan Terakhir Yang Memuat Susunan Direksi", "availability": "optional"}),
            (0, 0, {"req_document": "SK KemenHumHAM Perihal PENGESAHAN Badan Hukum Perseroan / SK Terdaftar KemenHumHam", "availability": "optional"}),
            (0, 0, {"req_document": "SK KemenHumHAM Perihal PERSETUJUAN/PEMBERITAHUAN Badan Hukum Perseroan", "availability": "optional"}),
            (0, 0, {"req_document": "Kartu Tanda Penduduk (KTP) penanggung jawab perusahaan", "availability": "wajib"}),
            (0, 0, {"req_document": "Nomor Induk Berusaha (NIB) dari OSS / Tanda Daftar Perusahaan (TDP)", "availability": "optional"}),
            (0, 0, {"req_document": "Surat Ijin Usaha Perdagangan / Izin Usaha OSS", "availability": "optional"}),
            (0, 0, {"req_document": "Surat Izin Gangguan dan atau Izin Tempat Usaha / Domisili / Izin Lokasi OSS", "availability": "optional"}),
            (0, 0, {"req_document": "Nomor Pokok Wajib Pajak dan Tanggal Terdaftar", "availability": "wajib"}),
            (0, 0, {"req_document": "Surat Pengukuhan Pengusaha Kena Pajak / Surat Pernyataan Non PKP", "availability": "optional"}),
            (0, 0, {"req_document": "Surat Keterangan Terdaftar dari DirJend Pajak", "availability": "optional"}),
            (0, 0, {"req_document": "ISO Certification", "availability": "optional"}),
        ]

        self.attachment_ids = [(5,0,0)]
        if self.tax_organization_type.tax_type == 'perseroan':
            self.attachment_ids = perseroan_list
        elif self.tax_organization_type.tax_type == 'perseorangan':
            self.attachment_ids = perseorangan_list
        elif self.tax_organization_type.tax_type == 'overseas':
            self.attachment_ids = overseas_list
        else:
            self.attachment_ids = [(5,0,0)]

    # @api.constrains('attachment_ids')
    def _check_validation(self):
        if not self.child_ids:
            raise ValidationError('Required Contact!')
        if not self.bank_ids:
            raise ValidationError('Required Bank Account!')
        if self.attachment_ids:
            for line in self.attachment_ids:
                if line.availability == 'wajib' and not line.attachment and not line.reason_unavailable:
                    raise ValidationError("Wajib mengupload dokumen %s atau menambahkan alasan karena tidak mengupload document tersebut." % line.req_document)
                
                
class ContactSupplier(models.Model):
    _name = 'contact.supplier'
    _description = 'Contact Supplier'

    partner_id = fields.Many2one('res.partner', string="Partner")
    contact_name = fields.Char(string="Contact Name")
    contact_email = fields.Char(string="Email")
    contact_number = fields.Char(string="Phone")
    
class BankAccountSupplier(models.Model):
    _name = 'bank.account.supplier'
    _description = 'Bank Account for Partners'
    

    partner_id = fields.Many2one('res.partner', string="Partner", ondelete='cascade', invisible='True')
    sequence = fields.Integer(string="Sequence", help="Sequence of the bank account")
    acc_number = fields.Char(string="Account Number", required=True, help="Bank account number")
    bank_id = fields.Many2one('res.bank', string="Bank", required=True, help="Bank where the account is held")
    allow_out_payment = fields.Boolean(string="Send Money", help="Allow outgoing payments from this bank account")
    acc_holder_name = fields.Char(string="Account Holder Name", help="The name of the account holder")
    tipe_rekening_bank = fields.Many2one('tipe.rekening.bank', string='Tipe Rekening Bank')
    _sql_constraints = [
        ('unique_account', 'unique(acc_number, partner_id)', 'The account number must be unique for each partner!')
    ]
            
class ResPartnerApproval(models.Model):
    _name = 'res.partner.approval'
    _inherit = 'approval.history.mixin'
    _description = 'Approval Prospective'

    employee_ids = fields.Many2many(
        'hr.employee', 'res_partner_approval_authorized_employee_rel', string='Employees'
    )
    reassign_employee_ids = fields.Many2many(
        'hr.employee',
        'res_partner_approval_authorized_reassign_employee_rel',
        string='Reassign Employees',
    )
    approval_employee_ids = fields.Many2many(
        'hr.employee',
        'res_partner_approval_approval_authorized_employee_rel',
        string='Employees',
    )
    partner_id = fields.Many2one('res.partner', string="Partner")
    sequence = fields.Integer(string="Sequence")
    approval_name = fields.Char(string="Approval Name")
    approval_position = fields.Char(string="Approval Position")
    approval_status = fields.Selection([
        ('submitted', 'Submitted'),
        ('approved', 'Approved'),
    ], string="Approval Status")
    approval_date = fields.Date(string="Approval Date")
    approval_note = fields.Text(string="Approval Note")
    reassign_to = fields.Many2one('res.users', string="Reassign To")
    reassign_status = fields.Selection([
        ('submitted', 'Submitted'),
        ('approved', 'Approved'),
    ], string="Reassign Status")
    reassign_date = fields.Date(string="Reassign Date")
    reassign_note = fields.Text(string="Reassign Note")
    
    
class ApprovalProspectiveDetail(models.Model):
    _name = 'approval.prospective.detail'
    _description = 'Approval Prospective Details'
    
    sequence = fields.Integer(string='Sequence')
    name = fields.Char(string='Name', required=True)
    birth_date = fields.Date(string='Birth Date')
    bl_internal = fields.Selection([('y', 'Yes'), ('n', 'No')], string='BL Internal')
    media_info = fields.Selection([('clear', 'Clear'), ('not_clear', 'Not Clear')], string='Media Info')
    fraud_data = fields.Selection([('clear', 'Clear'), ('not_clear', 'Not Clear')], string='Fraud Data')
    apu_ppt = fields.Selection([('clear', 'Clear'), ('not_clear', 'Not Clear')], string='APU-PPT')
    employee_status = fields.Selection([('y', 'Yes'), ('n', 'No')], string='Employee Status')
    note = fields.Text(string='Note')
    procurement_unit = fields.Boolean(related='partner_id.procurement_unit', store=True)
    fraud_unit = fields.Boolean(related='partner_id.fraud_unit', store=True)
    compilance_unit = fields.Boolean(related='partner_id.compilance_unit', store=True)
    partner_id = fields.Many2one('res.partner', string='Partner')
    is_fully_filled = fields.Boolean(string='All Fields Filled', compute='_compute_is_fully_filled', store=True)
    # unit_department_due_dill = fields.Selection([
    #     ('proc', 'Procurement Unit'),
    #     ('fraud', 'Fraud Management Unit'),
    #     ('compliance', 'Compliance Unit')
    # ], string='Unit Department', compute='_compute_unit_department_due_dill', store=True)

    # @api.depends('partner_id.procurement_unit',
    #              'partner_id.fraud_unit',
    #              'partner_id.compilance_unit')
    # def _compute_unit_department_due_dill(self):
    #     for rec in self:
    #         if rec.partner_id.procurement_unit:
    #             rec.unit_department_due_dill = 'proc'
    #         elif rec.partner_id.fraud_unit:
    #             rec.unit_department_due_dill = 'fraud'
    #         elif rec.partner_id.compilance_unit:
    #             rec.unit_department_due_dill = 'compliance'
    #         else:
    #             rec.unit_department_due_dill = False

    @api.depends(
        'bl_internal', 'media_info',
        'fraud_data', 'apu_ppt'
    )
    def _compute_is_fully_filled(self):
        for rec in self:
            rec.is_fully_filled = False
            if rec.bl_internal or rec.media_info or rec.fraud_data or rec.apu_ppt:
                rec.is_fully_filled = True
            # rec.is_fully_filled = all([
            #     rec.name,
            #     rec.birth_date,
            #     rec.bl_internal,
            #     rec.media_info,
            #     rec.fraud_data,
            #     rec.apu_ppt,
            #     rec.employee_status,
            #     rec.note
            # ])

    # @api.depends_context('uid')
    @api.depends('partner_id')  # or leave empty
    def _compute_edit_rights(self):
        for rec in self:
            rec.procurement_unit = rec.partner_id.procurement_unit
            rec.fraud_unit = rec.partner_id.fraud_unit
            rec.compilance_unit = rec.partner_id.compilance_unit

    
    
class ResPartnerAttachment(models.Model):
    _name = 'res.partner.attachment'
    _description = 'Partner Attachments'

    partner_id = fields.Many2one('res.partner', string="Partner", ondelete="cascade")
    sequence = fields.Integer(string="Nomor", compute='_compute_line_number', store=True)
    req_document = fields.Char(string="Req. Document", required=True)
    availability = fields.Selection([
        ('wajib', 'Wajib'),
        ('optional', 'Optional'),
    ], string="Availability", default="wajib")
    reason_unavailable = fields.Text(string="Alasan jika belum/tidak tersedia")
    attachment = fields.Many2many('ir.attachment', string="Attachment")

    @api.depends('partner_id.attachment_ids')
    def _compute_line_number(self):
        for partner in self.mapped('partner_id'):
            sequence = 1
            for line in partner.attachment_ids:
                line.sequence = sequence
                sequence += 1
            