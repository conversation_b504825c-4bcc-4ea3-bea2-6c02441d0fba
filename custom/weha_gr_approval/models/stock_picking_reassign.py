
# -*- coding: utf-8 -*-
###############################################################################
#    License, author and contributors information in:                         #
#    __manifest__.py file at the root folder of this module.                  #
###############################################################################

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError



class WehaStockPickingReassign(models.Model):
    _name = 'stock.picking.reassign'
    _description = 'GR Reassign'

    stock_picking_id = fields.Many2one('stock.picking', 'GR',)
    from_employee_id = fields.Many2one('hr.employee', string='From Employee')
    to_employee_id = fields.Many2one('hr.employee', string='To Employee')

    

