
# -*- coding: utf-8 -*-
###############################################################################
#    License, author and contributors information in:                         #
#    __manifest__.py file at the root folder of this module.                  #
###############################################################################

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError



# class WehaStockPickingHistoryApproval(models.Model):
#     _name = 'stock.picking.history.approval'
#     _description = 'GR Approval'
#     _order = 'level'

#     stock_picking_id = fields.Many2one(string='GR',comodel_name='stock.picking')
#     level = fields.Integer('Sequence', default=1)
#     approval_by = fields.Selection(selection=[('position_department', 'Unit & Position'),('job_level_department', 'Unit & Job Level'),('position', 'Position'),('department', 'Unit'),('employee', 'Employee'),('job_level', 'Job Level')], string='Approval By', default='position_department')
#     department_ids = fields.Many2many('hr.department', string='Units')
#     job_ids = fields.Many2many('hr.job', string='Positions')
#     job_level = fields.Selection(selection=[('staff', 'Staff'),('avp', 'AVP'),('vp', 'VP'),('direktur', 'Direktur'),('komisaris', 'Komisaris')], string='Job Levels', default='staff')
#     employee_ids = fields.Many2many('hr.employee', 'employee_hierarchy_rel', string='Employees')
#     reassign_employee_ids = fields.Many2many('hr.employee', 'employee_reassign_hierarchy_rel', string='Reassign Employees')
#     approval_employee_ids = fields.Many2many('hr.employee', 'employee_approval_hierarchy_rel', string='Employees')
#     approval_type = fields.Selection(selection=[('serial', 'Serial'),('paralel', 'Paralel'),('voting', 'Voting')], string='Type', default='serial')
#     voting_point = fields.Integer(string='Voting Point (%)', default=0)
#     total_voting_point = fields.Integer(string='Total Voting Point (%)', default=0)
#     state = fields.Selection([('in_progress', 'In Progress'),('approve', 'Approved'),('reject', 'Rejected')], 'Approval Status', copy=False)


class StockPickingApproval(models.Model):
    _name = 'stock.picking.approval'
    _inherit = 'approval.history.mixin'
    _description = 'Stock Picking Approval'

    stock_picking_id = fields.Many2one(string='GR',comodel_name='stock.picking')

    reassign_employee_ids = fields.Many2many(
        'hr.employee',
        'employee_reassign_hierarchy_stock_picking_rel',
        'stock_picking_reassign_approval_id',
        'employee_id',
        string='Reassign Employees'
    )


    employee_ids = fields.Many2many(
        'hr.employee',
        'employee_approval_hierarchy_stock_picking_rel',  # tetap
        'stock_picking_approval_id',
        'employee_id',
        string='Employees'
    )

    approval_employee_ids = fields.Many2many(
        'hr.employee',
        'approval_employee_hierarchy_stock_picking_rel',
        'stock_picking_approval_approval_id',
        'approval_employee_id',
        string='Approval Employees'
    )
    

class StockPickingApprovalDetail(models.Model):
    _name = 'stock.picking.approval.detail'
    _inherit = 'approval.history.detail.mixin'
    _description = 'Account Justification Approval'

    stock_picking_id = fields.Many2one(string='GR',comodel_name='stock.picking')
    

