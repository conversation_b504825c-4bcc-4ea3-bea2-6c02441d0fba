<odoo>
    <data>
        <record id="inherit_view_picking_form_id_inherit_module_stock" model="ir.ui.view">
            <field name="name">stock.picking.fppr.form</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="purchase_fppr.inherit_view_picking_form_id_inherit_module_stock"/>
            <field name="arch" type="xml">
                <xpath expr="//page[@name='approval_gr']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
            </field>
        </record>

        <record id="stock_picking_loa_form" model="ir.ui.view">
            <field name="name">stock.picking.loa.form</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_form"/>
            <field name="arch" type="xml">
                <!-- add button to do action -->
                <xpath expr="//header" position="inside">
                    <button name="button_action" type="object" string="Approve" class="btn btn-success" 
                        invisible="state_fppr != 'pending_approval' or not is_current_approver"
                        context="{'approve': True}" />
                    <button name="button_action" type="object" string="Reject" class="btn-danger"
                        invisible="state_fppr != 'pending_approval' or not is_current_approver"
                        context="{'reject': True}" />
                    <button name="button_action" type="object" string="Reassign" class="btn btn-success" 
                        invisible="state_fppr != 'pending_approval' or (not is_current_approver and not is_admin)"
                        context="{'reassign': True}" />
                </xpath>
                <xpath expr="//field[@name='picking_type_id']" position="before">
                    <field name="requestor_id" invisible='1' />
                    <field name="hierarchy_id" readonly="1" force_save='1'/>
                </xpath>

                <!-- add approval_history_ids in new page inside notebook -->
                <xpath expr="//notebook" position="inside">
                    <page name="history" string="Approvals">
                        <field name="is_current_approver" invisible="1"/>
                        <field name="selected_approver_ids" widget="many2many_tags" invisible="1"/>
                        <field name="approval_history_ids" readonly="1" force_save='1' />
                    </page>
                    <page string="Approval Details" name="history_detail">
                        <field name="approval_history_detail_ids" >
                            <list editable="bottom" create="0" edit="0" delete="0">
                                <field name="level" readonly="1" force_save='1' />
                                <field name="employee_id" readonly="1" force_save='1' />
                                <field name="employee_state" readonly="1" force_save='1' />
                                <field name="employee_date" readonly="1" force_save='1' />
                                <field name="employee_note" readonly="1" force_save='1' />
                                <field name="reassign_employee_id" readonly="1" force_save='1' />
                                <field name="reassign_employee_state" readonly="1" force_save='1' />
                                <field name="reassign_employee_date" readonly="1" force_save='1' />
                                <field name="reassign_employee_note" readonly="1" force_save='1' />
                            </list>
                        </field>
                    </page>
                    <page name="approval_message" string="Message Details">
                        <field name="approval_message_ids" readonly="1" force_save='1' />
                    </page>
                </xpath>

                <xpath expr="//button[@name='action_approve']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>

                <xpath expr="//button[@name='action_reject']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>

                <!-- <xpath expr="//page[@name='approvers']" position="attributes">
                    <attribute name="invisible">True</attribute>
                </xpath> -->
            </field>
        </record>

        <!-- <record id="stock_picking_view_search" model="ir.ui.view">
            <field name="name">stock.picking.search</field>
            <field name="model">stock.picking</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <search>
                    <separator/>
                    <filter name="my_approvals" string="My Approvals" domain="[('selected_approver_ids', 'in', [current_employee_id])]"/>
                    <separator/>
                </search>
            </field>
        </record> -->

        <record id="stock.action_picking_tree_incoming" model="ir.actions.act_window">
            <field name="context">{'search_default_my_approvals': 1, 'contact_display': 'partner_address', 'restricted_picking_type_code': 'incoming', 'search_default_reception': 1}</field>
        </record>

        <record id="view_stock_picking_filter" model="ir.ui.view">
            <field name="name">view.stock.picking.filter</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_internal_search"/>
            <field name="arch" type="xml">
                <xpath expr="//filter[@name='internal']" position="after">
                    <separator/>
                    <filter name="my_approvals" string="My Approvals" domain="[('selected_approver_ids.user_id', '=', uid)]" />
                </xpath>
            </field>
        </record>
    </data>
</odoo>
