import logging
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

from odoo import SUPERUSER_ID, api, fields, models, _
from odoo.exceptions import ValidationError, UserError

STATE = [
    ('draft', "Draft"),
    ('submit', "Submit"),
    ('award', "Award"),
    ('pending_approve', 'Pending Approval'),
    ('award_approve', "Award Approve"),
    ('due_dillgence_approve', "Due Dillgence Approve"),
    ('finish', "Finish"),
    ('cancel', "Cancelled"),
]

class BiddingNegotiation(models.Model):
    _name = 'bidding.negotiation'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Bidding Negotiation'

    active = fields.Boolean(
        string="Active",
        help="Indicates whether this record is active. If disabled, the record will not appear in lists but will remain in the system.",
        copy=False, default=True, index=True, tracking=True)
    name = fields.Char(
        string="Negotiation Document",
        help="Document number or reference related to the negotiation.", 
        copy=False,
        default=lambda self: _('New'))
    bidding_id = fields.Many2one(
        comodel_name="bidding.bidding", 
        string="Bidding Number", 
        copy=False, 
        index=True,
        help="Reference to the related bidding record.")
    bidding_description = fields.Char(
        string="Bidding Description",
        help="Description of the bidding related to this record.", 
        copy=False)
    bidding_type = fields.Selection(
        selection=[('rfq', 'RFQ'), ('rfp', 'RFP')],
        string="Bidding Type",
        copy=False, 
        index=True,
        help="The type of bidding process, such as Request for Quotation (RFQ) or Request for Proposal (RFP).")
    partner_id = fields.Many2one(
        comodel_name='res.partner',
        string="Vendor",
        help="Select the vendor associated with this contact.",
        copy=False, index=True, domain="[('id', 'in', available_partner_ids)]",)
    available_partner_ids = fields.Many2many(
        comodel_name='res.partner',
        string="Available Partners", copy=False)
    partner_reference = fields.Char(
        string="Vendor Reference",
        help="Unique reference for this vendor, used in purchasing processes.",
        copy=False)
    buyer_id = fields.Many2one(
        comodel_name='hr.employee', 
        string="Buyer", 
        help="The buyer responsible for managing this negotiation.")
    requestor_id = fields.Many2one(
        comodel_name='hr.employee', 
        string="Requested By", 
        help="The person who requested the negotiation.")
    negotiation_date = fields.Date(
        string="Negotiation Date", 
        default=fields.Date.context_today,
        help="The date when the negotiation was created.")
    submitted_date = fields.Date(
        string="Submitted Date", 
        help="The date when the transaction was submitted.")
    currency_id = fields.Many2one(
        comodel_name="res.currency", 
        string="Currency",
        help="The currency in which the unit price is defined.")
    company_currency_id = fields.Many2one(
        string='Company Currency',
        related='company_id.currency_id', readonly=True,
        help="The default currency of the company.")
    vendor_status = fields.Selection(
        selection=[
            ('draft', 'Draft'),
            ('submit', 'Pending Approval Prospective'),
            ('prospective', 'Prospective'),
            ('duediligence', 'Due Diligence'),
            ('spendauthorized', 'Spend Authorized')
        ],
        string="Vendor Status",
        help="Indicates whether the vendor is fully authorized for spending or is still a prospective supplier.")
    due_diligence_status = fields.Selection(
        selection=[
            ('yes', 'Yes'),
            ('no', 'No')
        ],
        string="Due Diligence Status",
        help="Specifies whether the vendor has passed due diligence checks.")
    attachment_ids = fields.Many2many(
        comodel_name='ir.attachment',
        string="Attachments", copy=False)
    unit_id = fields.Many2one(
        comodel_name='hr.department',
        string="Unit",
        context={'hierarchical_naming': False},
        help="The unit or department that submitted the negotiation.")
    group_id = fields.Many2one(
        comodel_name='hr.department',
        string="Group",
        context={'hierarchical_naming': False},
        help="The group or category of the negotiation.")
    unit = fields.Char(
        string="Unit",
        help="The unit or department that submitted the negotiation.")
    exchange_rate = fields.Float(
        string="Finance Exchange Rate", 
        help="The exchange rate used for financial transactions, converting from foreign currency to the company’s base currency.")
    group = fields.Char(
        string="Group",
        help="The group or category of the negotiation.")
    state = fields.Selection(
        selection=STATE,
        string="Status",
        readonly=True, 
        copy=False, 
        index=True,
        default='draft',
        help="The status of the negotiation, such as 'Draft', 'Submit', or other states.")
    line_ids = fields.One2many(
        comodel_name="bidding.negotiation.line", 
        inverse_name="negotiation_id", 
        string="Products")
    notes = fields.Text(
        string="Notes",
        help="Additional notes or remarks related to this record.")
    amount_total = fields.Monetary(
        string="Total",
        compute="_compute_amount_total",
        store=True,
        currency_field="currency_id",
        help="Total amount calculated as the sum of the total values from line items.")
    delivery_location_id = fields.Many2one(
        comodel_name="stock.location",
        string="Delivery Location",
        help="Specify the delivery location for this transaction.",
        copy=False)
    delivery_term_id = fields.Many2one(
        comodel_name="account.incoterms",
        string="Delivery Term",
        help="Specify the delivery terms for this transaction.",
        copy=False)
    payment_term_id = fields.Many2one('account.payment.term', string='Term of Payment')
    company_id = fields.Many2one(
        comodel_name="res.company",
        string="Company",
        help="Specify the company associated with this transaction.",
        default=lambda self: self.env.company,
        copy=False)
    is_add_product_negotiation = fields.Boolean(
        string="Add Product Negotiation",
        related="bidding_id.is_add_product_negotiation", store=True,
        help="Enable this option to allow adding products for negotiation in the bidding process.",
        copy=False)
    is_award = fields.Boolean(
        string="Is Award",
        compute="_compute_is_award",
        store=True)
    total_manajemen_fee = fields.Monetary(
        string="Total Management Fee",
        currency_field='currency_id',
        compute="_compute_total_manajemen_fee",
        store=True,
        help="Jumlah total biaya manajemen dari baris yang ditandai sebagai manajemen fee.",
        copy=False)


    @api.depends("line_ids", "line_ids.is_manajemen_fee", "line_ids.total")
    def _compute_total_manajemen_fee(self):
        for record in self:
            record.total_manajemen_fee = sum(
                line.total for line in record.line_ids if line.is_manajemen_fee
            )

    @api.depends('state', 'line_ids.is_award_line') 
    def _compute_is_award(self):
        for record in self:
            if record.state == 'finish': 
                record.is_award = any(line.is_award_line for line in record.line_ids)
            else:
                record.is_award = False

    def get_sequence(self, name=False, obj=False, pref=False, context=None):
        sequence_id = self.env['ir.sequence'].search([
            ('name','=',name),
            ('code','=',obj),
            ('implementation','=','standard'),
            ('prefix','=',pref)
        ])
        if not sequence_id :
            sequence_id = self.env['ir.sequence'].sudo().create({
                'name': name,
                'code': obj,
                'implementation': 'standard',
                'prefix': pref,
                'padding': 5
            })
        return sequence_id.next_by_id()

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if vals.get('name', 'New') in ('New', '/'):
                bidding_name = ''
                if 'bidding_id' in vals:
                    bidding = self.env['bidding.bidding'].browse(vals['bidding_id'])
                    bidding_name = bidding.name
                prefix = f"{bidding_name} - "
                vals['name'] = self.get_sequence(
                    f'{bidding_name} Sequence Negotiation',
                    'bidding.negotiation',
                    pref=prefix
                )
        return super(BiddingNegotiation, self).create(vals_list)



    @api.onchange('bidding_id')
    def _onchange_available_partners(self):
        if self.bidding_id:
            self.available_partner_ids = self.bidding_id.bidding_vendor_ids.mapped('vendor_id')
        else:
            self.available_partner_ids = []

    @api.onchange('bidding_id')
    def onchange_bidding_description_lines(self):
        self.bidding_description = self.bidding_id.description
        self.bidding_type = self.bidding_id.bidding_type
        self.buyer_id = self.bidding_id.buyer_id.id
        self.requestor_id = self.bidding_id.requestor_id.id
        self.unit_id = self.bidding_id.unit_id.id
        self.group_id = self.bidding_id.group_id.id
        self.onchange_unit_group()
        line_vals = [(5, 0, 0)]  # Ini akan clear (hapus semua) line_ids yang sudah ada
        for line in self.bidding_id.line_ids:
            latest_rate = self.env['res.currency.rate'].search([
                ('currency_id', '=', line.currency_id.id)
            ], order='name desc', limit=1)  # Ambil kurs terbaru
            
            exchange_rate = latest_rate.inverse_company_rate if latest_rate else 1

            line_vals.append((0, 0, {
                'product_tmpl_id': line.product_tmpl_id.id,
                'product_variant_id': line.product_variant_id.id,
                'product_uom_id': line.product_uom_id.id,
                'description': line.description,
                'quantity': line.quantity,
                'is_percentage': line.is_percentage,
                'currency_id': line.currency_id.id,
                # 'unit_price': line.unit_price,
                'exchange_rate': exchange_rate,
            }))

        self.line_ids = line_vals


    @api.onchange('unit_id','group_id')
    def onchange_unit_group(self):
        self.unit = self.unit_id.name
        self.group = self.group_id.parent_id.name

    # @api.onchange('partner_id')
    # def onchange_vendor_status(self):
    #     self.vendor_status = self.partner_id.vendor_status
    #     self.onchange_due_diligence_status()

    # @api.onchange('partner_id','vendor_status')
    # def onchange_due_diligence_status(self):
    #     if self.vendor_status == 'spendauthorized':
    #         self.due_diligence_status = 'yes'
    #     elif self.vendor_status == 'prospective':
    #         self.due_diligence_status = 'no'

    @api.onchange('currency_id')
    def onchange_exchange_rate(self):
        if self.currency_id:
            latest_rate = self.env['res.currency.rate'].search([
                ('currency_id', '=', self.currency_id.id)
            ], order='name desc', limit=1)  # Ambil kurs terbaru
            
            self.exchange_rate = latest_rate.inverse_company_rate if latest_rate else 1

    @api.depends('line_ids.total', 'currency_id')
    def _compute_amount_total(self):
        for record in self:
            amount_total = sum(record.line_ids.mapped('total'))
            exchange_rate = record.exchange_rate or 1
            record.amount_total = amount_total / exchange_rate

    def unlink(self):
        for record in self:
            if record.state != 'draft':
                raise UserError("You can only delete records with status 'Draft'.")
        return super().unlink()

    is_already_submit = fields.Boolean(string='Already Submit', compute="_compute_submit")

    def _get_compute_submit(self):
        return False

    def _compute_submit(self):
        for rec in self:
            rec.is_already_submit = rec._get_compute_submit()
    

    def action_check_status_vendor(self):
        self.ensure_one()
        self.vendor_status = self.partner_id.vendor_state
        if self.vendor_status == 'spendauthorized':
            self.due_diligence_status = 'yes'
            self.state = 'award_approve'
        elif self.vendor_status == 'prospective':
            self.due_diligence_status = 'no'
        
    def action_award(self):
        self.ensure_one()
        self.action_check_status_vendor()
        
        if not any(line.is_award_line for line in self.line_ids):
            raise ValidationError("Field 'Award Line' wajib diisi pada saat status Award Approve.")
            
        self.state = 'award_approve' if self.vendor_status == 'spendauthorized' else 'due_dillgence_approve'

    def action_draft(self):
        for record in self:
            state_label = dict(self._fields['state'].selection).get(self.state, '')
            if record.state == 'draft':
                raise UserError(_("Data ini sudah dalam status '%s'. Harap muat ulang halaman untuk melihat status yang diperbarui.") % state_label)
            record.state = 'draft'

    def action_submit(self):
        for record in self:
            state_label = dict(self._fields['state'].selection).get(self.state, '')
            if record.state != 'draft':
                raise UserError(_("Data ini sudah dalam status '%s'. Harap muat ulang halaman untuk melihat status yang diperbarui.") % state_label)
            
            if not record.line_ids:
                raise UserError(_("Data produk belum diisi."))

            # zero_price_lines = record.line_ids.filtered(lambda l: l.unit_price == 0)
            # for line in zero_price_lines:
            #     if not line.is_percentage:
            #         raise UserError(_("Terdapat baris dengan harga satuan 0. Mohon periksa dan isi harga satuan sebelum melanjutkan."))

            zero_qty_lines = record.line_ids.filtered(lambda l: l.quantity == 0)
            if zero_qty_lines:
                raise UserError(_("Terdapat baris dengan quantity 0. Mohon periksa dan isi quantity sebelum melanjutkan."))

            not_variant = record.line_ids.filtered(lambda l: not l.product_variant_id and not l.propose_variant)
            if not_variant:
                raise UserError("Setiap baris harus memiliki 'Product Variant' atau 'Propose Variant' yang diisi.")

            record.state = 'submit'
            record.submitted_date = fields.Datetime.now()
            # record.action_check_status_vendor()

    def print_negotiation(self):
        return True

    # def action_award(self):
    #     for record in self:
    #         state_label = dict(self._fields['state'].selection).get(self.state, '')
    #         if record.state != 'submit':
    #             raise UserError(_("Data ini sudah dalam status '%s'. Harap muat ulang halaman untuk melihat status yang diperbarui.") % state_label)
            
    #         if not any(line.is_award_line for line in record.line_ids):
    #             raise UserError(_("Tidak ada baris yang ditandai sebagai award line. Harap pilih setidaknya satu produk sebagai award line."))
    #         record.state = 'award'

    def action_mass_award(self):
        for record in self:
            if record.state != 'submit':
                raise UserError(_("Data ini '%s' tidak dalam status submit. Data yang bisa award hanya status yang sudah submit.") % record.name)
            record.state = 'award'

    def action_finish(self):
        for record in self:
            state_label = dict(self._fields['state'].selection).get(self.state, '')
            if record.state != 'award_approve':
                raise UserError(_("Data ini sudah dalam status '%s'. Harap muat ulang halaman untuk melihat status yang diperbarui.") % state_label)
            record.state = 'finish'

    def action_cancel(self):
        for record in self:
            state_label = dict(self._fields['state'].selection).get(self.state, '')
            if record.state == 'cancel':
                raise UserError(_("Data ini sudah dalam status '%s'. Harap muat ulang halaman untuk melihat status yang diperbarui.") % state_label)
            record.state = 'cancel'

    def action_open_bidding(self):
        return {
            'type': 'ir.actions.act_window',
            'name': 'Bidding',
            'res_model': 'bidding.bidding',
            'view_mode': 'list,form',
            'domain': [('id', 'in', self.mapped('bidding_id').ids)],
            'context': {'create': False},
            'target': 'current',
        }
  

    def action_open_po(self):
        self.ensure_one()
        return {
            'name': _("Purchase Orders"),
            'type': 'ir.actions.act_window',
            'res_model': 'purchase.order',
            'view_mode': 'list,form',
            'domain': [('purchase_request_id', '=', self.bidding_id.purchase_request_id.id)],
       }


    # def action_negotiation_send(self):
    #     """Membuka wizard email untuk mengirim email ke vendor."""
    #     self.ensure_one()
    #     ir_model_data = self.env['ir.model.data']

    #     # Ambil template email dari modul `ap_bidding_negotiation`
    #     try:
    #         template_id = ir_model_data._xmlid_lookup('ap_bidding_negotiation.email_template_bidding_negotiation')[1]
    #     except ValueError:
    #         template_id = False

    #     if not template_id:
    #         raise ValueError(_("Template email tidak ditemukan!"))

    #     # Ambil form wizard `mail.compose.message`
    #     try:
    #         compose_form_id = ir_model_data._xmlid_lookup('mail.email_compose_message_wizard_form')[1]
    #     except ValueError:
    #         compose_form_id = False

    #     partner_ids = [(6, 0, self.partner_id.id)]
    #     email_to = self.partner_id.email

    #     ctx = dict(self.env.context or {})
    #     ctx.update({
    #         'default_model': 'bidding.negotiation',
    #         'default_res_ids': self.ids,
    #         'default_use_template': bool(template_id),
    #         'default_template_id': template_id,
    #         'default_partner_ids': partner_ids,
    #         # 'author_id': self.env.user.id,
    #         'default_email_from': self.env.user.company_id.email,
    #         #'default_email_to': email_to,
    #         'default_subject': 'Negotiation No. ' + str(self.name),
    #         'default_composition_mode': 'comment',
    #         'default_email_layout_xmlid': "mail.mail_notification_layout_with_responsible_signature",
    #         'custom_layout': "ap_bidding_negotiation.email_template_bidding_negotiation",
    #         'force_email': True,
    #         'mark_so_as_sent': True,
    #         # 'default_attachment_ids': [(6, 0, [att for att in attachment_ids])],
    #     })

    #     return {
    #         'name': _('Compose Email'),
    #         'type': 'ir.actions.act_window',
    #         'view_mode': 'form',
    #         'res_model': 'mail.compose.message',
    #         'views': [(compose_form_id, 'form')],
    #         'view_id': compose_form_id,
    #         'target': 'new',
    #         'context': ctx,
    #     }




