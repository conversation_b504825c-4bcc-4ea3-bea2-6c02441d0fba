from odoo import fields, api, models 
from odoo.exceptions import ValidationError


class BaseHierarchy(models.Model):
    _inherit = 'base.hierarchy'

    is_authorized = fields.Boolean('Is Authorized', default=False)
    is_update_supplier = fields.Selection(
        [
            ('is_update_general', 'Update General Information'), 
            ('is_update_bank', 'Update Bank Account'),
            ('is_update_tax', 'Update Tax Status'),
            ('is_update_term', 'Update Term Of Payment'),
        ]
        ,'Is update Supplier', default=False)

    flagging_res_partner = fields.Boolean('Flagging REs Partner', default=False)

    @api.onchange('model_id')
    def _onchange_model_id(self):
        if self.model_id.model == 'res.partner':
            self.flagging_res_partner = True
        else:
            self.flagging_res_partner = False


class BaseHierarchy(models.Model):
    _inherit = 'base.hierarchy'

    @api.constrains('model_id', 'minimum_amount', 'maximum_amount', 'department_ids')
    def check_hierarchy(self):
        for rec in self:
            if rec.model_id.model == 'res.partner':
                continue

            for dept in rec.department_ids:
                checks = self.env['base.hierarchy'].search([
                    ('model_id', '=', rec.model_id.id),
                    ('id', '!=', rec.id),
                ]).filtered(lambda x: dept.id in x.department_ids.ids and x.minimum_amount == rec.minimum_amount and x.maximum_amount == rec.maximum_amount)

                if checks:
                    raise ValidationError('Already exists hierarchy, check hierarchy %s' % (checks[0].name))
