<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_partner_form" model="ir.ui.view">
            <field name="name">res.partner.base.form</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_partner_form"/>
            <field name="arch" type="xml">
                <!-- add button to do action -->
                <xpath expr="//header" position="inside">
                    <button name="action_approval" type="object" string="Approve" class="btn btn-success"
                        invisible="not context.get('vendor_view', False) or vendor_state not in ('submit', 'duediligence') or not is_current_approver"
                        context="{'approve': True}" />
                    <button name="action_approval" type="object" string="Reject" class="btn-danger"
                        invisible="not context.get('vendor_view', False) or vendor_state not in ('submit', 'duediligence') or not is_current_approver"
                        context="{'reject': True}" />
                    <button name="action_approval" type="object" string="Reassign" class="btn btn-success"
                        invisible="not context.get('vendor_view', False) or vendor_state not in ('submit', 'duediligence') or not is_current_approver or is_reassign"
                        context="{'reassign': True}" />
                </xpath>

                <!-- add approval_history_ids in new page inside notebook -->

                <xpath expr="//field[@name='created_by']" position="after">
                    <field name="hierarchy_id" readonly="1" force_save='1' invisible="not context.get('vendor_view', False)" />
                    <field name="hierarchy_authorized_id" readonly="1" force_save='1' invisible="not context.get('vendor_view', False)" />
                </xpath>
            </field>
        </record>

        <record id="inherit_view_partner_property_form_followup_hide_id_inherit_partner_linkaja" model="ir.ui.view">
            <field name="name">res.partner.view.form.inherit</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="partner_linkaja.view_partner_property_form_followup_hide"/>
            <field name="arch" type="xml">
                <xpath expr="//page[@name='approval_prospective']" position="after">
                    <page name="approval_history" string="Approval Prospective" invisible="not context.get('vendor_view', False)">
                        <field name="is_current_approver" invisible="1" />
                        <field name="selected_approver_ids" widget="many2many_tags" invisible="1"/>
                        <field name="approval_history_ids" readonly="1" force_save='1' />
                    </page>
                    <page string="Approval Details" name="history_detail">
                        <field name="approval_history_detail_ids" >
                            <list editable="bottom" create="0" edit="0" delete="0">
                                <field name="level" readonly="1" force_save='1' />
                                <field name="employee_id" readonly="1" force_save='1' />
                                <field name="employee_state" readonly="1" force_save='1' />
                                <field name="employee_date" readonly="1" force_save='1' />
                                <field name="employee_note" readonly="1" force_save='1' />
                                <field name="reassign_employee_id" readonly="1" force_save='1' />
                                <field name="reassign_employee_state" readonly="1" force_save='1' />
                                <field name="reassign_employee_date" readonly="1" force_save='1' />
                                <field name="reassign_employee_note" readonly="1" force_save='1' />
                            </list>
                        </field>
                    </page>
                    <page string="Approval Details Authorized" name="history_detail_authorized">
                        <field name="approval_history_detail_authorized_ids" >
                            <list editable="bottom" create="0" edit="0" delete="0">
                                <field name="level" readonly="1" force_save='1' />
                                <field name="employee_id" readonly="1" force_save='1' />
                                <field name="employee_state" readonly="1" force_save='1' />
                                <field name="employee_date" readonly="1" force_save='1' />
                                <field name="employee_note" readonly="1" force_save='1' />
                                <field name="reassign_employee_id" readonly="1" force_save='1' />
                                <field name="reassign_employee_state" readonly="1" force_save='1' />
                                <field name="reassign_employee_date" readonly="1" force_save='1' />
                                <field name="reassign_employee_note" readonly="1" force_save='1' />
                            </list>
                        </field>
                    </page>
                    <page name="approval_message" string="Approval Messages" invisible="not context.get('vendor_view', False)">
                        <field name="approval_message_ids" readonly="1" force_save='1' />
                    </page>
                </xpath>
            </field>
        </record>

        <record id="view_partner_form_inherit" model="ir.ui.view">
            <field name="name">res.partner.form</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="partner_linkaja.view_partner_form_inherit"/>
            <field name="arch" type="xml">
                <xpath expr="//header/button[@name='action_prospective']" position="replace" />
                <xpath expr="//header/button[@name='approve']" position="replace" />
            </field>
        </record>

        <record id="view_res_partner_filter" model="ir.ui.view">
            <field name="name">view.res.partner.filter</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_res_partner_filter"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="after">
                    <separator/>
                    <filter name="my_approvals" string="My Approvals" domain="[('selected_approver_ids.user_id', '=', uid)]" />
                    <separator/>
                </xpath>
            </field>
        </record>

        <record id="account.res_partner_action_supplier" model="ir.actions.act_window">
            <field name="context">{'search_default_supplier': 1,'res_partner_search_mode': 'supplier', 'default_is_company': True, 'default_supplier_rank': 1, 'default_is_vendor': True, 'vendor_view': True, 'search_default_my_approvals': 1}</field>
        </record>

        <record id="partner_linkaja.res_partner_action_supplier_master" model="ir.actions.act_window">
            <field name="context">{'search_default_supplier': 1,'res_partner_search_mode': 'supplier', 'default_is_company': True, 'default_supplier_rank': 1, 'default_is_vendor': True, 'vendor_view': True, 'search_default_my_approvals': 1}</field>
        </record>
    </data>
</odoo>
