<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data>
    
        <record id="inherit_view_base_approval_hierarchy_form_supplier_master_id_inherit_base_approval" model="ir.ui.view">
            <field name="name">base.hierarchy.view.form.inherit</field>
            <field name="model">base.hierarchy</field>
            <field name="inherit_id" ref="base_approval.view_base_approval_hierarchy_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='active']" position="after">
                    <field name="flagging_res_partner" invisible="1"/>
                    <field name="is_authorized" invisible = "flagging_res_partner == False" />
                    <field name="is_update_supplier" invisible = "flagging_res_partner == False" />
                </xpath>
            </field>
        </record>
    
    </data>
    

</odoo>
