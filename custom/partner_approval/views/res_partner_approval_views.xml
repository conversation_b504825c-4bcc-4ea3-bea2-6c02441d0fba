<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_res_partner_approval_history_form" model="ir.ui.view">
            <field name="name">view.res.partner.approval.history.form</field>
            <field name="model">res.partner.approval.history</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <group>
                                <field name="level" />
                                <field name="state" />
                                <field name="target_state" />
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="view_res_partner_approval_history_tree" model="ir.ui.view">
            <field name="name">view.res.partner.approval.history.tree</field>
            <field name="model">res.partner.approval.history</field>
            <field name="arch" type="xml">
                <list>
                    <field name="level" />
                    <field name="approval_by" required="True" />
                    <field name="department_ids"
                        domain="[('department_type','=', '4_unit')]"
                        widget="many2many_tags"
                        context="{'hierarchical_naming': False}"
                        invisible="approval_by in ['position', 'employee', 'job_level']"
                    />
                    <field name="job_ids"
                        widget="many2many_tags"
                        invisible="approval_by in ['department', 'employee', 'job_level_department', 'job_level']"
                    />
                    <field name="job_level"
                        invisible="approval_by in ['department', 'employee', 'position_department', 'position']"
                    />
                    <field name="employee_ids"
                        widget="many2many_tags"
                        invisible="approval_by not in ['employee']"
                    />
                    <field name="approval_type" required="True" />
                    <field name="voting_point" invisible="approval_type != 'voting'" />
                    <field name="total_voting_point" invisible="approval_type != 'voting'" />
                    <field name="target_state" />
                    <field name="state" />
                </list>
            </field>
        </record>
    </data>
</odoo>
