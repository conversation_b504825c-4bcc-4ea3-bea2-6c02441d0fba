# -*- coding: utf-8 -*-

from odoo import Command, fields, models
from odoo.exceptions import ValidationError


class PartnerHierarchyWizard(models.TransientModel):
    _name = 'res.partner.hierarchy.wizard'
    _description = 'Partner Hierarchy Wizard'

    partner_id = fields.Many2one('res.partner', string='Partner')
    from_employee_id = fields.Many2one('hr.employee', string='From Employee')
    to_employee_id = fields.Many2one('hr.employee', string='To Employee')
    note = fields.Char(string='Note')

    def action_approve(self):
        self.partner_id.with_context(note=self.note)._approve()

    def action_reject(self):
        self.partner_id.with_context(note=self.note)._reject()

    def action_reassign(self):
        reassign_to_employee_ids = self.partner_id.approval_reassign_ids.mapped(
            'to_employee_id'
        ).ids
        reassign_from_employee_ids = self.partner_id.approval_reassign_ids.mapped(
            'from_employee_id'
        ).ids

        if self.to_employee_id.id in reassign_to_employee_ids:
            raise ValidationError('This employee already reassign by other employee!')
        if self.to_employee_id in reassign_from_employee_ids:
            raise ValidationError('This employee cannot assign approval!')

        reassign_vals = {
            'from_employee_id': self.from_employee_id.id,
            'to_employee_id': self.to_employee_id.id,
        }
        self.partner_id.approval_reassign_ids = [Command.create(reassign_vals)]

        message_vals = {
            'employee_id': self.env.user.employee_id.id,
            'date': fields.Datetime.now(),
            'state': 'reassign',
            'note': f'Reassign approval to {self.to_employee_id.name}',
        }
        self.partner_id.approval_message_ids = [Command.create(message_vals)]

        # send email
        emails = self.to_employee_id.work_email
        if emails:
            template = self.partner_id._get_info_template()
            message = f"""Reassign approval Supplier name {self.partner_id.name}
            from employee {self.from_employee_id.name}
            """

            self.partner_id.send_email(message, emails, template)
