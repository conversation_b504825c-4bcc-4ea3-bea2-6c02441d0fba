<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="res_partner_hierarchy_wizard_view_form" model="ir.ui.view">
            <field name="name">res.partner.hierarchy.wizard.view.form</field>
            <field name="model">res.partner.hierarchy.wizard</field>
            <field name="arch" type="xml">
                <form string="Partner Hierarchy Wizard">
                    <sheet>
                        <group>
                            <group>
                                <field name="partner_id" invisible='1' />
                                <field name="from_employee_id" invisible="not context.get('reassign')" readonly='1' />
                                <field name="to_employee_id" invisible="not context.get('reassign')" required="context.get('reassign')" />
                                <field name="note" invisible="context.get('reassign')" required="not context.get('reassign')" />
                            </group>
                        </group>
                        <footer>
                            <button name="action_approve" type="object" invisible="not context.get('approve')" string="Approve" class="oe_highlight"/>
                            <button name="action_reject" type="object" invisible="not context.get('reject')" string="Reject" class="oe_highlight"/>
                            <button name="action_reassign" type="object" invisible="not context.get('reassign')" string="Reassign" class="oe_highlight"/>
                            <button string="Discard" class="btn-default" special="cancel" />
                        </footer>
                    </sheet>
                </form>
            </field>
        </record>
    </data>
</odoo>
