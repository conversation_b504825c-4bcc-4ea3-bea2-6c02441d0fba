<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="mail_res_partner_approval" model="mail.template">
            <field name="name">Supplier: Request Approval</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="subject">Supplier (Ref {{ object.name or 'n/a' }})</field>
            <field name="email_to">{{ ctx['mail_to'] }}</field>
            <field name="auto_delete" eval="True"/>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            Supplier Approval:<br/><br/>
                            <table width="100%">
                                <tr>
                                    <td>Name</td>
                                    <td>: <t t-out="object.name or ''">-</t></td>
                                </tr>
                            </table>
                        </div>
                    </p>
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <a t-att-href="ctx['link']" >Click here to see details.</a>
                    </p>
                </div>
            </field>
        </record>

        <record id="mail_res_partner_info" model="mail.template">
            <field name="name">Supplier: Info</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="subject">Supplier (Ref {{object.name or 'n/a' }})</field>
            <field name="email_to">{{ ctx['mail_to'] }}</field>
            <field name="auto_delete" eval="True"/>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            <t t-out="ctx['message']" />
                        </div>
                    </p>
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <a t-att-href="ctx['link']" >Click here to see details.</a>
                    </p>
                </div>
            </field>
        </record>

        <record id="email_template_authorized_notification" model="mail.template">
            <field name="name">Authorized Notification Email</field>
            <field name="model_id" ref="base.model_res_partner"/> <!-- Misalnya model partner -->
            <field name="subject">Action Required for (Ref {{ object.name or 'n/a' }})</field>
            <field name="email_to">{{ ctx['mail_to'] }}</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p>Hello,</p>
                    <p>This is a notification for department to fill in the required fields for <strong></strong>.</p>
                    <t t-foreach="object.approval_detail_ids" t-as="approval_detail">
                        <p>
                            <strong>Name:</strong> <t t-out="approval_detail.name or ''"></t><br/>
                            <strong>Media Info:</strong> <t t-out="approval_detail.media_info or ''"></t><br/>
                            <strong>Fraud Data:</strong> <t t-out="approval_detail.fraud_data or ''"></t>
                        </p>
                        
                    </t>
                    <p>Please follow the link: <a t-att-href="ctx['link']" >Click here to see details.</a></p>
                </div>
            </field>
        </record>


        <record id="mail_procurement_update_approval" model="mail.template">
            <field name="name">Update Supplier: Request Approval</field>
            <field name="model_id" ref="partner_linkaja.model_procurement_update_data"/>
            <field name="subject">Update Supplier (Ref {{ object.reference_number or 'n/a' }})</field>
            <field name="email_to">{{ ctx['mail_to'] }}</field>
            <field name="auto_delete" eval="True"/>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            Update Supplier Approval:<br/><br/>
                            <table width="100%">
                                <tr>
                                    <td>Number</td>
                                    <td>: <t t-out="object.reference_number or ''">-</t></td>
                                </tr>
                                <tr>
                                    <td>name</td>
                                    <td>: <t t-out="object.partner_id.display_name or ''">-</t></td>
                                </tr>
                            </table>
                        </div>
                    </p>
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <a t-att-href="ctx['link']" >Click here to see details.</a>
                    </p>
                </div>
            </field>
        </record>

        <record id="mail_procurement_update_info" model="mail.template">
            <field name="name">Update Supplier: Info</field>
            <field name="model_id" ref="partner_linkaja.model_procurement_update_data"/>
            <field name="subject">Update Supplier (Ref {{object.reference_number or 'n/a' }})</field>
            <field name="email_to">{{ ctx['mail_to'] }}</field>
            <field name="auto_delete" eval="True"/>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            <t t-out="ctx['message']" />
                        </div>
                    </p>
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <a t-att-href="ctx['link']" >Click here to see details.</a>
                    </p>
                </div>
            </field>
        </record>
    </data>
</odoo>
