from odoo import models, fields, api


class PurchaseOrderApproval(models.Model):
    _name = 'purchase.order.approval'
    _inherit = 'approval.history.mixin'
    _description = 'Purchase Order Approval'

    purchase_id = fields.Many2one('purchase.order', 'Purchase Order',
                               ondelete='cascade')

    reassign_employee_ids = fields.Many2many(
        'hr.employee',
        'employee_reassign_hierarchy_purchase_rel',
        'purchase_order_reassign_approval_id',
        'employee_id',
        string='Reassign Employees'
    )


    employee_ids = fields.Many2many(
        'hr.employee',
        'employee_approval_hierarchy_purchase_rel',  # tetap
        'purchase_order_approval_id',
        'employee_id',
        string='Employees'
    )

    approval_employee_ids = fields.Many2many(
        'hr.employee',
        'approval_employee_hierarchy_rel',
        'purchase_order_approval_approval_id',
        'approval_employee_id',
        string='Approval Employees'
    )
    hierarchy_department_ids = fields.Many2many(
        'hr.department',
        'approval_hierarchy_department_rel',
        'purchase_order_approval_id',
        'department_id',
        string='Hierarchy Units')
    state = fields.Selection([
        ('in_progress', 'In Progress'),
        ('approve', 'Approved'),
        ('reject', 'Rejected'),
        ('return', 'Return')
    ], 'Approval Status', copy=False)


class PurchaseOrderApprovalDetail(models.Model):
    _name = 'purchase.order.approval.detail'
    _inherit = 'approval.history.detail.mixin'
    _description = 'Purchase Order Approval'

    purchase_id = fields.Many2one('purchase.order', 'Purchase Order',
                               ondelete='cascade')

    

class PurchaseOrderReassign(models.Model):
    _name = 'purchase.order.reassign'
    _description = 'Purchase Order Reassign'
    

    purchase_id = fields.Many2one('purchase.order', 'Purchase Order',
                               ondelete='cascade')
    from_employee_id = fields.Many2one('hr.employee', string='From Employee')
    to_employee_id = fields.Many2one('hr.employee', string='To Employee') 
