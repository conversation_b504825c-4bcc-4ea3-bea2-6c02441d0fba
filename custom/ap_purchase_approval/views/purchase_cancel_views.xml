<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_purchase_cancel_form" model="ir.ui.view">
            <field name="name">purchase.cancel.form</field>
            <field name="model">purchase.order.cancel.close</field>
            <field name="inherit_id" ref="ap_purchase_cancel_close.ap_purchase_cancel_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//button[@name='action_reject']" position="replace" />
                <xpath expr="//button[@name='action_approve']" position="replace">
                    <button name="action_approval" type="object" string="Approve" class="btn btn-success"
                        invisible="state != 'waiting' or not is_current_approver"
                        context="{'approve': True}" />
                    <button name="action_approval" type="object" string="Reject" class="btn btn-danger"
                        invisible="state != 'waiting' or not is_current_approver"
                        context="{'reject': True}" />
                    <button name="action_approval" type="object" string="Reassign" class="btn btn-success"
                        invisible="state != 'waiting' or (not is_current_approver and not is_admin)"
                        context="{'reassign': True}" />
                </xpath>
                <xpath expr="//field[@name='user_id']" position="after">
                    <field name="requestor_id" invisible='1' />
                    <field name="hierarchy_id" readonly="1" force_save='1' />
                </xpath>
                <!-- add approval_history_ids in new page inside notebook -->
                <xpath expr="//sheet" position="inside">
                    <notebook>
                        <page name="approval_history" string="Approvals">
                            <field name="is_current_approver" invisible="1" />
                            <field name="selected_approver_ids" widget="many2many_tags" invisible="1"/>
                            <field name="approval_history_ids" readonly="1" force_save='1' />
                        </page>
                        <page string="Approval Details" name="history_detail">
                            <field name="approval_history_detail_ids" >
                                <list editable="bottom" create="0" edit="0" delete="0">
                                    <field name="level" readonly="1" force_save='1' />
                                    <field name="employee_id" readonly="1" force_save='1' />
                                    <field name="employee_state" readonly="1" force_save='1' />
                                    <field name="employee_date" readonly="1" force_save='1' />
                                    <field name="employee_note" readonly="1" force_save='1' />
                                    <field name="reassign_employee_id" readonly="1" force_save='1' />
                                    <field name="reassign_employee_state" readonly="1" force_save='1' />
                                    <field name="reassign_employee_date" readonly="1" force_save='1' />
                                    <field name="reassign_employee_note" readonly="1" force_save='1' />
                                </list>
                            </field>
                        </page>
                        <page name="approval_message" string="Approval Messages">
                            <field name="approval_message_ids" readonly="1" force_save='1' />
                        </page>
                    </notebook>
                </xpath>
                <xpath expr="//sheet" position="after">
                    <chatter/>
                </xpath>
            </field>
        </record>

        <record id="view_purchase_cancel_filter" model="ir.ui.view">
            <field name="name">purchase.cancel.filter</field>
            <field name="model">purchase.order.cancel.close</field>
            <field name="inherit_id" ref="ap_purchase_cancel_close.ap_purchase_cancel_view_search"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="after">
                    <filter name="my_approvals" string="My Approvals" domain="[('selected_approver_ids.user_id', '=', uid)]" />
                </xpath>
            </field>
        </record>

        <record id="ap_purchase_cancel_close.action_purchase_cancel" model="ir.actions.act_window">
            <field name="context">{'search_default_my_approvals': 1}</field>
            <field name="path">purchase_cancel</field>
        </record>
    </data>
</odoo>
