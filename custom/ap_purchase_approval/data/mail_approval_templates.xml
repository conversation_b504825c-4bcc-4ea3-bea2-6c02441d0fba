<odoo>
    <data>
        <!-- Purchase Order Approval Mail Templates -->
        <record id="mail_purchase_order_approval" model="mail.template">
            <field name="name">PO: Request Approval</field>
            <field name="model_id" ref="purchase.model_purchase_order"/>
            <field name="subject">PO (Ref {{ object.name or 'n/a' }})</field>
            <field name="email_to">{{ ctx['mail_to'] }}</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            PO Approval:<br/><br/>
                            <table width="100%">
                                <tr>
                                    <td>Number</td>
                                    <td>: <t t-out="object.name or ''">-</t></td>
                                </tr>
                                <tr>
                                    <td><strong>Total Amount</strong></td>
                                    <td><strong>: <t t-out="'{:,.0f}'.format(object.amount_total) or ''">-</t></strong></td>
                                </tr>
                            </table>
                        </div>
                    </p>
                    <p style="margin: 5px 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            <table width="100%">
                                <tr>
                                    <td>Display Type</td>
                                    <td>Description</td>
                                    <td>Amount</td>
                                </tr>

                                <t t-foreach="object.order_line" t-as="line">
                                    <tr>
                                        <td><t t-out="line.display_type"/></td>
                                        <td><t t-out="line.name"/></td>
                                        <td><t t-out="'{:,.0f}'.format(line.price_subtotal)"/></td>
                                    </tr>
                                </t>
                            </table>
                        </div>
                    </p>
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <a t-att-href="ctx['link']" >Click here to see details</a> 
                    </p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>

        <record id="mail_purchase_order_info" model="mail.template">
            <field name="name">PO: Info</field>
            <field name="model_id" ref="purchase.model_purchase_order"/>
            <field name="subject">PO (Ref {{object.name or 'n/a' }})</field>
            <field name="email_to">{{ ctx['mail_to'] }}</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            <t t-out="ctx['message']" />
                        </div>
                    </p>
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <a t-att-href="ctx['link']" >Click here to see details</a> 
                    </p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>

        <!-- Purchase DPL Approval Mail Templates -->
        <record id="mail_purchase_dpl_approval" model="mail.template">
            <field name="name">DPL: Request Approval</field>
            <field name="model_id" ref="ap_purchase_dpl.model_purchase_dpl"/>
            <field name="subject">DPL (Ref {{ object.name or 'n/a' }})</field>
            <field name="email_to">{{ ctx['mail_to'] }}</field>
            <field name="auto_delete" eval="True"/>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            DPL Approval:<br/><br/>
                            <table width="100%">
                                <tr>
                                    <td>Name</td>
                                    <td>: <t t-out="object.name or ''">-</t></td>
                                </tr>
                                <tr>
                                    <td>Amount</td>
                                    <td>: <t t-out="object.amount or ''">-</t></td>
                                </tr>
                            </table>
                        </div>
                    </p>
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <a t-att-href="ctx['link']" >Click here to see details.</a>
                    </p>
                </div>
            </field>
        </record>

        <record id="mail_purchase_dpl_info" model="mail.template">
            <field name="name">DPL: Info</field>
            <field name="model_id" ref="ap_purchase_dpl.model_purchase_dpl"/>
            <field name="subject">DPL (Ref {{object.name or 'n/a' }})</field>
            <field name="email_to">{{ ctx['mail_to'] }}</field>
            <field name="auto_delete" eval="True"/>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            <t t-out="ctx['message']" />
                        </div>
                    </p>
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <a t-att-href="ctx['link']" >Click here to see details.</a>
                    </p>
                </div>
            </field>
        </record>

        <!-- Purchase Cancel Approval Mail Templates -->
        <record id="mail_purchase_cancel_approval" model="mail.template">
            <field name="name">Cancel Order: Request Approval</field>
            <field name="model_id" ref="ap_purchase_cancel_close.model_purchase_order_cancel_close"/>
            <field name="subject">Cancel Order (Ref {{ object.name or 'n/a' }})</field>
            <field name="email_to">{{ ctx['mail_to'] }}</field>
            <field name="auto_delete" eval="True"/>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            Cancel Order Approval:<br/><br/>
                            <table width="100%">
                                <tr>
                                    <td>Name</td>
                                    <td>: <t t-out="object.name or ''">-</t></td>
                                </tr>
                                <tr>
                                    <td>PO</td>
                                    <td>: <t t-out="object.purchase_id.name or ''">-</t></td>
                                </tr>
                            </table>
                        </div>
                    </p>
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <a t-att-href="ctx['link']" >Click here to see details.</a>
                    </p>
                </div>
            </field>
        </record>

        <record id="mail_purchase_cancel_info" model="mail.template">
            <field name="name">Cancel Order: Info</field>
            <field name="model_id" ref="ap_purchase_cancel_close.model_purchase_order_cancel_close"/>
            <field name="subject">Cancel Order (Ref {{object.name or 'n/a' }})</field>
            <field name="email_to">{{ ctx['mail_to'] }}</field>
            <field name="auto_delete" eval="True"/>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            <t t-out="ctx['message']" />
                        </div>
                    </p>
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <a t-att-href="ctx['link']" >Click here to see details.</a>
                    </p>
                </div>
            </field>
        </record>
    </data>
</odoo>
