# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError

class AccountJustification(models.Model):
    _name = 'account.justification'
    _description = 'Account Justification'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(
        string='Justification Number',
        readonly=True,
        store=True,
        default=lambda self: _('New'),
        sequence='account.justification'
    )
    active = fields.Boolean(
        string='Archive',
        default=True
    )

    amount_currency = fields.Monetary(
        string='Amount',
        currency_field='company_currency_id',
        compute='_compute_amounts',
        inverse='_set_amount_currency',
        store=True
    )
    amount_justif = fields.Monetary('Amount Justif', related='amount_currency', currency_field='company_currency_id', store=True)
    amount_residual = fields.Monetary(
        string='Amount Residual',
        currency_field='company_currency_id',
        compute='_compute_amounts'
    )
    attachment_ids = fields.Many2many(
        comodel_name='ir.attachment',
        string='Justification Attachment'
    )
    available_fund = fields.Monetary(
        string='Beginning Fund Available RKAP',
        currency_field='company_currency_id',
        compute='_compute_available_fund',
        store=True
    )
    budget_id = fields.Many2one(
        comodel_name='crossovered.budget',
        string='Budget',
        tracking=True,
        domain="[('active', '=', True), ('date_from', '>=', '{}-01-01'), ('date_to', '<=', '{}-12-31')]".format(
            fields.Date.today().year, fields.Date.today().year)
    )
    budgetary_position_domain = fields.Binary(
        compute="_compute_budgetary_position_domain"
    )
    budgetary_position_id = fields.Many2one(
        comodel_name='crossovered.budget.lines',
        string='RKAP',
        tracking=True
    )
    budget_reserved = fields.Monetary(
        string='Total Budget Reserved',
        currency_field='company_currency_id',
        compute='_compute_amounts'
    )
    company_currency_id = fields.Many2one(
        comodel_name='res.currency',
        string='Currency',
        default = lambda self: self.env.company.currency_id
    )
    date = fields.Date(
        string='Transaction Date',
        required=True,
        tracking=True,
        default=fields.Date.today
    )
    description = fields.Text(
        string='Justification Name',
        tracking=True,
        required=True
    )
    directorate_id = fields.Many2one(
        comodel_name='hr.department',
        string='Directorate',
        domain="[('department_type', '=', '1_dir')]",
        context={'hierarchical_naming': False},
        tracking=True
    )
    employee_id = fields.Many2one(
        comodel_name='hr.employee',
        string='Submitter Name',
        default=lambda self: self.env['hr.employee'].search([('user_id', '=', self.env.user.id)], limit=1),
        tracking=True
    )
    end_date = fields.Date(
        string='Periode End',
        tracking=True
    )
    ending_fund = fields.Monetary(
        string='Ending Fund',
        currency_field='company_currency_id',
        compute='_compute_amounts',
        store=True
    )
    group_id = fields.Many2one(
        comodel_name='hr.department',
        string='Group',
        domain="[('department_type', '=', '3_group')]",
        context={'hierarchical_naming': False},
        tracking=True
    )
    justification_type_id = fields.Many2one(
        comodel_name='account.justification.type',
        string='Justification Type',
        ondelete='cascade',
        tracking=True
    )
    is_editable = fields.Boolean(
        string='Is Editable',
        default=False,
    )
    line_ids = fields.One2many(
        comodel_name='account.justification.line',
        inverse_name='justification_id',
        string='Justif Lines'
    )
    manual_currency_rate = fields.Float(
        string='Rate'
    )
    manual_currency_rate_active = fields.Boolean(
        string='Apply Manual Exchange'
    )
    period_id = fields.Many2one(
        comodel_name='crossovered.budget.period',
        string='Periode',
        required=True,
        domain=[('state', '=', 'open')]
    )
    requestor_id = fields.Many2one(
        comodel_name='hr.employee',
        string='Requestor Name',
        default=lambda self: self.env['hr.employee'].search([('user_id', '=', self.env.user.id)], limit=1),
        tracking=True
    )
    start_date = fields.Date(
        string='Periode Start',
        tracking=True
    )
    state = fields.Selection(
        selection=[
            ('draft', 'Draft'),
            ('pending_approval', 'Pending Approval'),
            ('approved', 'Approved'),
            ('return', 'Return'),
            ('rejected', 'Rejected'),
            ('done', 'Closed'),
            ('cancel', 'Cancelled'),
        ],
        string='Justification State',
        default='draft',
        tracking=True
    )
    tax_amt = fields.Monetary(
        string='Tax Amount',
        currency_field='company_currency_id',
        compute='_compute_amounts'
    )
    unit_id = fields.Many2one(
        comodel_name='hr.department',
        string='Unit',
        domain="[('department_type', '=', '4_unit')]",
        context={'hierarchical_naming': False},
        tracking=True
    )
    untaxed_amt = fields.Monetary(
        string='Untaxed Amount',
        currency_field='company_currency_id',
        compute='_compute_amounts'
    )

    # Purchase Request Related Fields
    purchase_request_ids = fields.One2many(
        comodel_name='purchase.request',
        inverse_name='justification_id',  # You'll need to add this field in purchase.request model
        string='Purchase Requests',
        readonly=True,
        copy=False
    )
    purchase_request_count = fields.Integer(
        string='Purchase Request Count',
        compute='_compute_purchase_request_count',
        store=True
    )

    # Approval related fields
    approver_ids = fields.One2many(
        comodel_name='account.justification.approver',
        inverse_name='justification_id',
        string='Approvers'
    )
    current_approver_id = fields.Many2one(
        comodel_name='res.users',
        string='Current Approver',
        compute='_compute_current_approver'
    )

    def _set_amount_currency(self):
        for rec in self:
            # Misal, simpan ke field lain atau abaikan jika hanya ingin bisa ditulis
            pass

    @api.onchange('start_date', 'end_date')
    def _onchange_dates(self):
        """
        When start_date or end_date changes:
        Set period_id based on matching crossovered.budget.period
        """
        if self.start_date or self.end_date:
            domain = [('state', '=', 'open')]
            if self.start_date:
                domain.append(('date_start', '<=', self.start_date))
            if self.end_date:
                domain.append(('date_end', '>=', self.end_date))
            
            # Find matching budget period
            period = self.env['crossovered.budget.period'].search(domain, limit=1)
            if period:
                self.period_id = period.id

    # Default Function
    @api.model
    def create(self, vals):
        vals['name'] = self.env['ir.sequence'].next_by_code('account.justification')
        return super(AccountJustification, self).create(vals)

    def write(self, vals):
        for record in self:
            record.attachment_ids.write({'res_model': self._name, 'res_id': record.id})
        return super(AccountJustification, self).write(vals)

    # Compute
    @api.depends('purchase_request_ids')
    def _compute_purchase_request_count(self):
        for rec in self:
            rec.purchase_request_count = len(rec.purchase_request_ids)

    @api.depends('approver_ids.approval_status')
    def _compute_current_approver(self):
        for rec in self:
            pending_approver = rec.approver_ids.filtered(
                lambda a: a.approval_status == 'pending'
            )
            rec.current_approver_id = pending_approver[0].approver_id if pending_approver else False

    @api.depends('line_ids.amount_currency', 'available_fund')
    def _compute_amounts(self):
        """
        Compute monetary amounts for the justification:
        - budget_reserved: sum of line amounts
        - ending_fund: available fund minus reserved
        - untaxed_amt: sum of line untaxed amounts
        - tax_amt: sum of line tax amounts
        - amount_currency: total including tax
        - amount_residual: sum of line residuals
        """
        for record in self:
            record.budget_reserved = sum(line.amount_currency for line in record.line_ids)
            record.ending_fund = record.available_fund - record.budget_reserved

            # Calculate ending fund
            ending_fund = record.available_fund - record.budget_reserved
            record.ending_fund = min(ending_fund, record.available_fund)

            record.untaxed_amt = sum(line.untaxed_amt for line in record.line_ids)
            record.tax_amt = sum(line.tax_amt for line in record.line_ids)
            record.amount_residual = sum(line.amount_residual for line in record.line_ids)
            record.amount_currency = record.untaxed_amt + record.tax_amt

            # Update budget line reservation
            # record._update_budget_line_reservation()

    @api.depends('budgetary_position_id')
    def _compute_available_fund(self):
        """
        Compute available fund based on planned amount and existing approved justifications
        """
        for record in self:
            available = 0.0
            if record.budgetary_position_id:
                # Get available fund from remaining amount
                available = record.budgetary_position_id.remaining_amount
                # # Get initial planned amount
                # available = record.budgetary_position_id.planned_amount

                # # Only check existing justifications if current record has an ID
                # if record.id:
                #     # Find existing approved justifications with same budget line
                #     existing_justifs = self.search([
                #         ('budgetary_position_id', '=', record.budgetary_position_id.id),
                #         ('id', '!=', record.id),
                #         ('state', '=', 'approved')
                #     ])

                #     # Deduct budget_reserved from existing justifications
                #     if existing_justifs:
                #         total_reserved = sum(existing_justifs.mapped('budget_reserved'))
                #         available -= total_reserved
                # else:
                #     # For new records, get all approved justifications
                #     existing_justifs = self.search([
                #         ('budgetary_position_id', '=', record.budgetary_position_id.id),
                #         ('state', '=', 'approved')
                #     ])
                #     if existing_justifs:
                #         total_reserved = sum(existing_justifs.mapped('budget_reserved'))
                #         available -= total_reserved

            record.available_fund = available

    # Constrains
    @api.constrains('end_date')
    def _check_end_date(self):
        """
        Validates that the end date is not later than December 31st of next year.
        """
        for rec in self:
            if rec.end_date:
                next_year = fields.Date.today().year + 1
                max_date = fields.Date.from_string(f'{next_year}-12-31')
                if rec.end_date > max_date:
                    raise ValidationError(_(
                        'End date cannot be later than December 31st of next year (%s)'
                    ) % fields.Date.to_string(max_date))

    # Onchange
    @api.onchange('requestor_id')
    def _onchange_requestor(self):
        """
        When employee changes:
        1. Clear unit/group/department if no employee
        2. Set unit/group/department based on department hierarchy
        """
        # Clear fields if no employee
        if not self.requestor_id:
            self.unit_id = False
            self.group_id = False
            self.directorate_id = False
            return

        department = self.requestor_id.department_id
        if not department:
            return

        if department.department_type == '4_unit':
            self.unit_id = department.id
            # Set group using parent department
            parent_group = department.parent_id
            if parent_group and parent_group.department_type == '3_group':
                self.group_id = parent_group.id
                # Set directorate using group's parent
                parent_dir = parent_group.parent_id
                if parent_dir and parent_dir.department_type == '1_dir':
                    self.directorate_id = parent_dir.id

        elif department.department_type == '3_group':
            self.unit_id = False
            self.group_id = department.id
            # Set directorate using group's parent
            parent_dir = department.parent_id
            if parent_dir and parent_dir.department_type == '1_dir':
                self.directorate_id = parent_dir.id

        elif department.department_type == '1_dir':
            self.unit_id = False
            self.group_id = False
            self.directorate_id = department.id

    @api.depends('period_id')
    def _compute_budgetary_position_domain(self):
        """Compute domain for budgetary position field based on period"""
        for rec in self:
            domain = [('crossovered_budget_id.state', '=', 'validate'), ('group_id', '=', rec.group_id.id)]

            if rec.period_id:
                domain += [('crossovered_budget_id.period_id', '=', rec.period_id.id)]
            else:
                domain = [('id', '=', False)]  # Empty domain when no period selected

            rec.budgetary_position_domain = domain

    @api.onchange('budgetary_position_id')
    def _onchange_budgetary_position(self):
        """
        Update budget_id based on selected budgetary position.
        """
        if self.budgetary_position_id:
            self.budget_id = self.budgetary_position_id.crossovered_budget_id

    # return budget
    def _update_budget_line_reservation_return(self):
        """
        Updates the budget line's justification reserve amount.
        Only considers pending and approved justifications.
        Called when computing amounts or state changes.
        Validates that total reservations don't exceed planned amount.
        """
        for record in self:
            if record.budgetary_position_id:
                budget_line = record.budgetary_position_id

                # Get all justifications for this budget line except current one
                other_justifs = record.search([
                    ('budgetary_position_id', '=', budget_line.id),
                    ('state', 'in', ['pending_approval', 'approved']),
                    ('id', '!=', record.id)
                ])

                # Sum other justification amounts
                total_reserved = sum(other_justifs.mapped('amount_currency'))
                begin_total_reserved = sum(other_justifs.mapped('amount_currency'))

                # Add current justification amount if pending/approved
                # if record.state in ['pending_approval', 'approved']:
                #     total_reserved += record.amount_currency

                #     # Check if total reserved exceeds planned amount
                #     if total_reserved > budget_line.remaining_amount + begin_total_reserved:
                #         raise ValidationError(_(
                #             'Total reserved amount (%.2f) exceeds remaining budget amount (%.2f)'
                #         ) % (total_reserved, budget_line.remaining_amount))

                budget_line.justifikasi_reserve_amount = total_reserved

    # Process
    def _update_budget_line_reservation(self):
        """
        Updates the budget line's justification reserve amount.
        Only considers pending and approved justifications.
        Called when computing amounts or state changes.
        Validates that total reservations don't exceed planned amount.
        """
        for record in self:
            if record.budgetary_position_id:
                budget_line = record.budgetary_position_id

                # Get all justifications for this budget line except current one
                other_justifs = record.search([
                    ('budgetary_position_id', '=', budget_line.id),
                    ('state', 'in', ['pending_approval', 'approved']),
                    ('id', '!=', record.id)
                ])

                # Sum other justification amounts
                total_reserved = sum(other_justifs.mapped('amount_currency'))
                begin_total_reserved = sum(other_justifs.mapped('amount_currency'))

                # Add current justification amount if pending/approved
                if record.state in ['pending_approval', 'approved']:
                    total_reserved += record.amount_currency

                    # Check if total reserved exceeds planned amount
                    if total_reserved > budget_line.remaining_amount + begin_total_reserved:
                        raise ValidationError(_(
                            'Total reserved amount (%.2f) exceeds remaining budget amount (%.2f)'
                        ) % (total_reserved, budget_line.remaining_amount))

                budget_line.justifikasi_reserve_amount = total_reserved

    def action_toggle_edit(self):
        """Toggle edit mode on/off"""
        for record in self:
            record.is_editable = not record.is_editable
        return True

    def action_submit(self):
        """
        Submit the account justification and change state to pending approval.
        Validates required fields before submission.
        """
        for rec in self:
            if not rec.line_ids:
                raise ValidationError(_('Cannot submit empty justification. Please add at least one line.'))

            if rec.amount_currency <= 0:
                raise ValidationError(_('Total amount must be greater than zero.'))

            rec.write({
                'state': 'pending_approval'
            })

            # Update available fund depends on remaining budget
            rec._compute_available_fund()
            rec._update_budget_line_reservation()

    def action_approve(self):
        """
        Approve the account justification.
        Only pending approval justifications can be approved.
        """
        self.ensure_one()

        # # Get the current employee
        # current_employee = self.env['hr.employee'].search(
        #     [('user_id', '=', self.env.user.id)], limit=1)
        # if not current_employee:
        #     raise ValidationError(_('No employee record found for current user.'))

        # # Find current approver record
        # current_approver = self.approver_ids.filtered(
        #     lambda a: a.approval_status == 'pending'
        # )[:1]

        # if not current_approver:
        #     raise ValidationError(_('No pending approval found.'))

        # # Check if employee is authorized to approve
        # authorized_employees = current_approver.approver_id | current_approver.reassign_to
        # if current_employee not in authorized_employees:
        #     raise ValidationError(_('You are not authorized to approve this justification.'))

        # # Update approver record
        # current_approver.write({
        #     'approval_status': 'approved',
        #     'approval_date': fields.Datetime.now(),
        #     'approval_note': self.env.context.get('approval_note', '')
        # })

        # Check if all approvers have approved
        # if not self.approver_ids.filtered(lambda a: a.approval_status == 'pending'):
        self.write({'state': 'approved'})

        # # Delete activity for current approver
        # activities = self.activity_ids.filtered(
        #     lambda act: act.user_id == self.env.user and
        #                 act.activity_type_id == self.env.ref(
        #         'account_budget_justif.mail_activity_justification_approval')
        # )
        # activities.action_feedback(feedback=self.env.context.get('approval_note', ''))

        return True

    def action_cancel(self):
        """
        Cancel the account justification and its related purchase requests.
        Only draft and approved justifications can be cancelled.
        """
        for rec in self:
            if rec.state not in ['draft', 'approved']:
                raise ValidationError(_('Only draft or approved justifications can be cancelled.'))

            # Cancel related purchase requests
            purchase_requests = rec.purchase_request_ids.filtered(
                lambda pr: pr.state not in ['rejected']
            )
            if purchase_requests:
                raise ValidationError(_("Purchase Requests must be rejected before canceling justification."))

            rec.write({
                'state': 'cancel',
            })

            # Reset budget reserved on lines
            rec.line_ids.write({'budget_reserved': 0.0})

            # Update budget reservation
            rec._update_budget_line_reservation_return()

    @api.model
    def _close_completed_justifications(self):
        """
        Scheduled action to close approved justifications with ending_fund <= 0
        Sets their state to 'done'
        """
        justifications = self.search([('state', '=', 'approved'), ('remaining_amount', '<=', 0), ('remaining_amount_fpjp', '<=', 0)])
        if justifications:
            return justifications.write({'state': 'done'})
        return True


    def action_reset_draft(self):
        """Reset justification to draft state"""
        self.ensure_one()
        if self.state not in ['pending_approval', 'return', 'rejected', 'cancel']:
            raise ValidationError(
                _('Only pending, returned, rejected or cancelled justifications can be reset to draft.'))

        # # Delete existing activities
        # self.activity_ids.unlink()

        # # Reset approvers
        # self.approver_ids.write({
        #     'approval_status': 'pending',
        #     'approval_date': False,
        #     'approval_note': False,
        #     'reassign_to': False
        # })

        # Update budget reservation
        self._update_budget_line_reservation_return()
        # self._update_budget_line_reservation()

        return self.write({'state': 'draft'})

    def action_reject(self):
        """
        Reject the account justification.
        Only pending approval justifications can be rejected.
        Required approval note in context.
        """
        self.ensure_one()

        # # Get current employee
        # current_employee = self.env['hr.employee'].search(
        #     [('user_id', '=', self.env.user.id)], limit=1)
        # if not current_employee:
        #     raise ValidationError(_('No employee record found for current user.'))

        # # Find current approver record
        # current_approver = self.approver_ids.filtered(
        #     lambda a: a.approval_status == 'pending'
        # )[:1]

        # if not current_approver:
        #     raise ValidationError(_('No pending approval found.'))

        # # Check if employee is authorized to reject
        # authorized_employees = current_approver.approver_id | current_approver.reassign_to
        # if current_employee not in authorized_employees:
        #     raise ValidationError(_('You are not authorized to reject this justification.'))

        # # Update approver record
        # current_approver.write({
        #     'approval_status': 'rejected',
        #     'approval_date': fields.Datetime.now(),
        # })

        # Update justification state
        self.write({'state': 'rejected'})

        # # Handle activities
        # activities = self.activity_ids.filtered(
        #     lambda act: act.user_id == self.env.user and
        #                 act.activity_type_id == self.env.ref(
        #         'account_budget_justif.mail_activity_justification_approval')
        # )
        # activities.action_feedback()

        # Update budget reservation
        self._update_budget_line_reservation()

        return True

    # Approval
    def _create_approval_activity(self, approver):
        """Create approval activity for approver"""
        activity_type_id = self.env.ref(
            'account_budget_justif.mail_activity_justification_approval'
        ).id

        # Get the user from employee record
        user_id = approver.approver_id.user_id.id
        if not user_id:
            raise ValidationError(_(
                'Approver %s has no related user account'
            ) % approver.approver_id.name)

        self.activity_schedule(
            activity_type_id=activity_type_id,
            user_id=user_id,
            note=_(
                'Please review and approve justification %(name)s\n'
                'Your approval sequence: %(sequence)s'
            ) % {
                     'name': self.name,
                     'sequence': approver.sequence
                 }
        )

    def action_reassign(self):
        """
        Reassign all pending approvals to new users.
        Creates activities for new approvers.
        """
        self.ensure_one()
        pending_approvers = self.approver_ids.filtered(
            lambda a: a.approval_status == 'pending' and a.reassign_to
        )

        if not pending_approvers:
            raise ValidationError(_('No pending approvals with reassignment selected.'))

        for approver in pending_approvers:
            # Create new approver record
            new_approver = approver.copy({
                'approver_id': approver.reassign_to.id,
                'approval_status': 'pending',
                'approval_date': False,
                'approval_note': False,
                'reassign_to': False,
                'sequence': approver.sequence
            })

            # Update original approver record
            approver.write({
                'approval_status': 'approved',
                'approval_date': fields.Datetime.now(),
                'approval_note': _('Reassigned to %s') % approver.reassign_to.name
            })

            # Create activity for new approver
            self._create_approval_activity(new_approver)
        return True

    # Integration
    def create_purchase_request(self):
        """Create purchase request from account justification"""
        purchase_request_obj = self.env['purchase.request']
        purchase_request_line_obj = self.env['purchase.request.line']

        for rec in self:
            if not rec.line_ids:
                continue

            # Create purchase request header
            request_vals = {
                'requested_by': rec.requestor_id.user_id.id or self.env.user.id,
                'assigned_to': rec.employee_id.user_id.id,
                'description': rec.description,
                'date_start': rec.start_date,
                'picking_type_id': self.env.ref('stock.picking_type_in').id,
                'company_id': self.env.company.id,
                'origin': rec.name,
                'justification_id': rec.id,
            }
            purchase_request = purchase_request_obj.create(request_vals)

            # Create purchase request lines
            for line in rec.line_ids:
                procurement_template = self.env.ref('account_budget_justif.product_template_procurement')
                non_procurement_template = self.env.ref('account_budget_justif.product_template_non_procurement')
                product_template = procurement_template if line.procurement_type_id.is_procurement else non_procurement_template
                product = self.env['product.product'].search([('product_tmpl_id', '=', product_template.id)], limit=1)

                line_vals = {
                    'request_id': purchase_request.id,
                    'product_id': product.id,
                    'name': line.description or '/',
                    'product_qty': 1.0,  # You might want to set this based on your needs
                    'product_uom_id': self.env.ref('uom.product_uom_unit').id,
                    'date_required': rec.date,
                    'estimated_cost': line.amount_currency,
                    'currency_id': rec.company_currency_id.id,
                    'justification_line_id': line.id,
                }
                purchase_request_line_obj.create(line_vals)

            # Update the state of purchase request
            purchase_request.button_to_approve()

            return {
                'type': 'ir.actions.act_window',
                'name': _('Purchase Request'),
                'res_model': 'purchase.request',
                'res_id': purchase_request.id,
                'view_mode': 'form',
                'target': 'current',
            }

    def action_view_purchase_requests(self):
        self.ensure_one()
        action = {
            'type': 'ir.actions.act_window',
            'name': _('Purchase Requests'),
            'res_model': 'purchase.request',
            'view_mode': 'list,form',
            'domain': [('justification_id', '=', self.id)],
            'context': {'default_justification_id': self.id},
            'target': 'current',
        }
        return action

