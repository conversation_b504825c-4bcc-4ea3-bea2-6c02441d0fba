<odoo>
    <data>
        <record id="mail_bidding_negotiation_approval" model="mail.template">
            <field name="name">Negotiation: Request Approval</field>
            <field name="model_id" ref="ap_bidding_negotiation.model_bidding_negotiation"/>
            <field name="subject">Negotiation (Ref {{ object.name or 'n/a' }})</field>
            <field name="email_to">{{ ctx['mail_to'] }}</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            Bidding Approval Form:<br/><br/>
                            <table width="100%">
                                <tr>
                                    <td>Number</td>
                                    <td>: <t t-out="object.name or ''">-</t></td>
                                </tr>
                                <tr>
                                    <td><strong>Total Amount</strong></td>
                                    <td><strong>: <t t-out="'{:,.0f}'.format(object.amount_total) or ''">-</t></strong></td>
                                </tr>
                            </table>
                        </div>
                    </p>
                    <p style="margin: 5px 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            <table width="100%">
                                <tr>
                                    <td>Product</td>
                                    <td>Product Variant</td>
                                    <td>Total</td>
                                </tr>

                                <t t-foreach="object.line_ids" t-as="line">
                                    <tr>
                                        <td><t t-out="line.product_tmpl_id.name"/></td>
                                        <td><t t-out="line.product_variant_id.name"/></td>
                                        <td><t t-out="'{:,.0f}'.format(line.total)"/></td>
                                    </tr>
                                </t>
                            </table>
                        </div>
                    </p>
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <a t-att-href="ctx['link']" >Click here to see details</a> 
                    </p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>

        <record id="mail_bidding_negotiation_info" model="mail.template">
            <field name="name">Negotation: Info</field>
            <field name="model_id" ref="ap_bidding_negotiation.model_bidding_negotiation"/>
            <field name="subject">Negotiation (Ref {{object.name or 'n/a' }})</field>
            <field name="email_to">{{ ctx['mail_to'] }}</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            <t t-out="ctx['message']" />
                        </div>
                    </p>
                    <p style="margin: 0px; padding: 0px; font-size: 12px;">
                        <a t-att-href="ctx['link']" >Click here to see details</a> 
                    </p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>
    </data>
</odoo>
