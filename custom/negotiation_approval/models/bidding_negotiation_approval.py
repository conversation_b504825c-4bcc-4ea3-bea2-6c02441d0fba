from odoo import api, fields, models


class BiddingNegotiationApproval(models.Model):
    _name = 'bidding.negotiation.approval'
    _inherit = 'approval.history.mixin'
    _description = 'Bidding Negotiation Approval'

    negotiation_id = fields.Many2one('bidding.negotiation', 'Bidding Negotiation',
                               ondelete='cascade')
    
    reassign_employee_ids = fields.Many2many(
        'hr.employee',
        'employee_reassign_hierarchy_bidding_negotiation_rel',
        'bidding_negotiation_reassign_approval_id',
        'employee_id',
        string='Reassign Employees'
    )


    employee_ids = fields.Many2many(
        'hr.employee',
        'employee_approval_hierarchy_bidding_negotiation_rel',  # tetap
        'bidding_negotiation_employee_id',
        'employee_id',
        string='Employees'
    )

    approval_employee_ids = fields.Many2many(
        'hr.employee',
        'approval_employee_hierarchy_bidding_negotiationel',
        'bidding_negotiation_approval_employee_id',
        'approval_employee_id',
        string='Approval Employees'
    )
    

class BiddingNegotiationApprovalDetail(models.Model):
    _name = 'bidding.negotiation.approval.detail'
    _inherit = 'approval.history.detail.mixin'
    _description = 'Bidding Negotiation Approval Detail'

    negotiation_id = fields.Many2one('bidding.negotiation', 'Bidding Negotiation',
                               ondelete='cascade')
    

class BiddingNegotiationReassign(models.Model):
    _name = 'bidding.negotiation.reassign'
    _description = 'Bidding Negotiation Reassign'
    

    negotiation_id = fields.Many2one('bidding.negotiation', 'Bidding Negotiation',
                               ondelete='cascade')
    from_employee_id = fields.Many2one('hr.employee', string='From Employee')
    to_employee_id = fields.Many2one('hr.employee', string='To Employee') 
