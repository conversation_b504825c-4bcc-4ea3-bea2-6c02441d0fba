<odoo>
    <data>
        <record id="bidding_negotiation_loa_form" model="ir.ui.view">
            <field name="name">bidding.negotiation.loa.form</field>
            <field name="model">bidding.negotiation</field>
            <field name="inherit_id" ref="ap_bidding_negotiation.ap_negotiation_view_form"/>
            <field name="arch" type="xml">
                <!-- add button to do action -->
                <xpath expr="//header" position="inside">
                    <button string="Award" name="action_request_approve" type="object" class="oe_highlight" invisible="state != 'submit'"/>
                    <button name="button_action" type="object" string="Approve" class="btn btn-success" 
                        invisible="state != 'pending_approve' or not is_current_approver"
                        context="{'approve': True}" />
                    <button name="button_action" type="object" string="Reject" class="btn-danger"
                        invisible="state != 'pending_approve' or not is_current_approver and vendor_status != 'spendauthorized'"
                        context="{'reject': True}" />
                    <button name="button_action" type="object" string="Reassign" class="btn btn-success" 
                        invisible="state != 'pending_approve' or (not is_current_approver and not is_admin) and vendor_status != 'spendauthorized'"
                        context="{'reassign': True}" />
                </xpath>

                <xpath expr="//field[@name='state']" position="replace">
                    <field name="state" widget="statusbar" statusbar_visible="draft,submit,pending_approve,due_dillgence_approve,award_approve,finish,reject"/>
                </xpath>
                <xpath expr="//button[@name='action_submit']" position="replace">
                    <button name="new_action_submit" string="Submit" type="object" class="oe_highlight" invisible="state != 'draft'"/>
                </xpath>
                <xpath expr="//button[@name='action_draft']" position="attributes">
                    <attribute name="invisible" >state not in ('submit','cancel','due_dillgence_approve','award_approve', 'finish')</attribute>
                </xpath>

                <xpath expr="//button[@name='action_award']" position="attributes">
                    <!-- <attribute name="invisible">vendor_status == 'spendauthorized' or state != 'submit'</attribute> -->
                     <attribute name="invisible" >1</attribute>
                </xpath>
                <!-- add approval_history_ids in new page inside notebook -->
                <xpath expr="//notebook" position="inside">
                    <page name="history" string="Approvals">
                        <field name="is_current_approver" invisible="1"/>
                        <field name="selected_approver_ids" widget="many2many_tags" invisible="1"/>
                        <field name="approval_history_ids" readonly="1" force_save='1' />
                    </page>
                    <page string="Approval Details" name="history_detail">
                        <field name="approval_history_detail_ids" >
                            <list editable="bottom" create="0" edit="0" delete="0">
                                <field name="level" readonly="1" force_save='1' />
                                <field name="employee_id" readonly="1" force_save='1' />
                                <field name="employee_state" readonly="1" force_save='1' />
                                <field name="employee_date" readonly="1" force_save='1' />
                                <field name="employee_note" readonly="1" force_save='1' />
                                <field name="reassign_employee_id" readonly="1" force_save='1' />
                                <field name="reassign_employee_state" readonly="1" force_save='1' />
                                <field name="reassign_employee_date" readonly="1" force_save='1' />
                                <field name="reassign_employee_note" readonly="1" force_save='1' />
                            </list>
                        </field>
                    </page>
                    <page name="approval_message" string="Message Details">
                        <field name="approval_message_ids" readonly="1" force_save='1' />
                    </page>
                </xpath>

                <!-- <xpath expr="//button[@name='action_approve']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath> -->

                <!-- <xpath expr="//button[@name='action_reject']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath> -->

                <!-- <xpath expr="//button[@name='action_submit']" position="attributes">
                    <attribute name="invisible">state != 'draft'</attribute>
                </xpath> -->

                <!-- put hierarchy_id and current_user_id after currency_id -->
                <xpath expr="//field[@name='requestor_id']" position="after">
                    <field name="hierarchy_id" readonly="1" force_save='1' />
                </xpath>

                <xpath expr="//field[@name='requestor_id']" position="attributes">
					<attribute name="required">True</attribute>
				</xpath>

                <!-- <xpath expr="//page[@name='approvers']" position="attributes">
                    <attribute name="invisible">True</attribute>
                </xpath> -->
            </field>
        </record>

        <!-- <record id="bidding_negotiation_view_search" model="ir.ui.view">
            <field name="name">bidding.negotiation.search</field>
            <field name="model">bidding.negotiation</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <search>
                    <separator/>
                    <filter name="my_approvals" string="My Approvals" domain="[('selected_approver_ids', 'in', [current_employee_id])]"/>
                    <separator/>
                </search>
            </field>
        </record> -->

        <record id="ap_bidding_negotiation.action_negotiation" model="ir.actions.act_window">
            <field name="context">{'search_default_my_approvals': 1}</field>
            <field name="path">bidding_negotiation</field>
        </record>

        <record id="view_bidding_negotiation_filter" model="ir.ui.view">
            <field name="name">view.bidding.negotiation.filter</field>
            <field name="model">bidding.negotiation</field>
            <field name="inherit_id" ref="ap_bidding_negotiation.ap_negotiation_view_search"/>
            <field name="arch" type="xml">
                <xpath expr="//filter[@name='state_cancel']" position="after">
                    <separator/>
                    <filter name="my_approvals" string="My Approvals" domain="[('selected_approver_ids.user_id', '=', uid)]" />
                    <separator/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
