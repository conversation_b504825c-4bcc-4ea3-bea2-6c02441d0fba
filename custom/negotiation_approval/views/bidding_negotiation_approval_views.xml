<odoo>
    <data>
        <record id="view_bidding_negotiation_approval_form" model="ir.ui.view">
            <field name="name">view.bidding.negotiation.approval.form</field>
            <field name="model">bidding.negotiation.approval</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <group>
                                <field name="level" />
                                <field name="state" />
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="view_bidding_negotiation_approval_tree" model="ir.ui.view">
            <field name="name">view.bidding.negotiation.approval.tree</field>
            <field name="model">bidding.negotiation.approval</field>
            <field name="arch" type="xml">
                <list editable="bottom">
                    <field name="level" />
                    <field name="approval_by" required="True" />
                    <field name="department_ids" 
                        domain="[('department_type','=', '4_unit')]"
                        widget="many2many_tags"
                        context="{'hierarchical_naming': False}"
                        invisible="approval_by in ['position', 'employee', 'job_level']"
                        required="approval_by in ['position_department', 'department', 'job_level_department']"
                    />
                    <field name="job_ids" 
                        widget="many2many_tags"
                        invisible="approval_by in ['department', 'employee', 'job_level_department', 'job_level']"
                        required="approval_by in ['position_department', 'position']"
                    />
                    <field name="job_level" 
                        invisible="approval_by in ['department', 'employee', 'position_department', 'position']"
                        required="approval_by in ['job_level_department', 'job_level']"
                    />
                    <field name="employee_ids" 
                        widget="many2many_tags"
                        invisible="approval_by not in ['employee']"
                        required="approval_by in ['employee']"
                    />
                    <field name="approval_type" required="True" />
                    <field name="voting_point" invisible="approval_type != 'voting'" />
                    <field name="total_voting_point" invisible="approval_type != 'voting'" />
                    <field name="state" />
                </list>
            </field>
        </record>
    </data>
</odoo>
