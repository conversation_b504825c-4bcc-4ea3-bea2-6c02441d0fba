from odoo import fields, api, models
from datetime import datetime


class WizardMassAssetGenerate(models.TransientModel):
    _inherit = 'wizard.mass.asset.generate'

    asset_line_ids = fields.One2many('wizard.mass.asset.line', 'generate_id', string='Asset Line')
    stock_location_id = fields.Many2one('stock.location', string='Stock Location')
    asset_location_id = fields.Many2one('asset.location', string='Asset Location')
    major_id = fields.Many2one('account.asset', 'Asset Major')
    date = fields.Date(default=datetime.today().date())

    @api.model
    def default_get(self, fields):
        res = {}
        # default journal
        # journal_id = self.env['account.journal'].search([
        #     ('type', '=', 'general'),
        # ], limit=1)
        # res['journal_id'] = journal_id.id
        res['date'] = datetime.today()

        return res

    @api.onchange('purchase_ids')
    def _onchange_purchase_ids(self):
        if self.purchase_ids:
            line_ids = [(5, 0, 0)]
            domain = self.account_move_line_domain()
            if self.type == 'split':
                domain += [('quantity', '>', 1)]

            domain += [('purchase_line_id.order_id', 'in', self.purchase_ids.ids)]
            move_lines = self.env['account.move.line'].sudo().search(domain)
            for ln in move_lines:
                picking = False
                move = ln.purchase_line_id.move_ids
                if move:
                    picking = move[0].picking_id.id
                    date_picking = move[0].picking_id.date_done
                data = {
                    'selected': True,
                    'product_id': ln.product_id.id,
                    'purchase_id': ln.purchase_line_id.order_id.id,
                    'picking_id': picking,
                    'date_picking': date_picking,
                    'invoice_id': ln.move_id.id,
                    'invoice_line_id': ln.id,
                    'qty': ln.quantity,
                    'price_unit': ln.price_unit,
                    'amount': ln.price_subtotal,

                    # 'purchase_line_number': ln.purchase_line_number,
                }
                line_ids.append((0, 0, data))

            self.line_ids = line_ids

    @api.onchange('date_acquisition')
    def _onchange_date_acquisition(self):
        if self.date_acquisition:
            for line in self.line_ids:
                line.date_acquisition = self.date_acquisition

    @api.onchange('type', 'is_selected_all', 'line_ids', 'line_ids.selected', 'line_ids.amount')
    def onchange_amount_total(self):
        amount_total = 0
        if self.type in ['merge', 'add']:
            for line in self.line_ids.filtered(lambda x: x.selected):
                amount_total += line.amount * line.currency_rate
        self.amount_total = amount_total
        self.amount_add = amount_total

    @api.onchange('major_id', 'type')
    def onchange_major_type(self):
        self.line_ids = [(6, 0, [])]
        if self.type == 'merge':
            lines = []
            sql = """
                SELECT sp.id as sp_id, sm.id as sm_id, am.id as am_id from stock_move sm LEFT JOIN stock_picking sp ON sp.id = sm.picking_id 
                    LEFT JOIN stock_picking_type spt ON spt.id = sp.picking_type_id
                    LEFT JOIN product_template pt ON pt.id = sm.product_tmpl_id
                    LEFT JOIN account_move am ON am.picking_journal_bill_id = sp.id
                    WHERE spt.code = 'incoming' AND sp.state_fppr = 'ap_invoice' AND sp.purchase_dua_id notnull AND 
                    sm.is_asset isnull AND am.state = 'posted' AND pt.is_asset <> false
                """
            self.env.cr.execute(sql)
            picking_lines = self.env.cr.dictfetchall()

            for picking in picking_lines:
                bill = self.env['account.move'].browse(picking['am_id'])
                stock_move = self.env['stock.move'].browse(picking['sm_id'])
                picking_id = self.env['stock.picking'].browse(picking['sp_id'])
                purchase_id = picking_id.purchase_dua_id
                purchase_line_number = 0
                quantity = 0
                price_unit = 0
                currency_id = False
                currency_rate = 1

                if purchase_id:
                    po_lines = purchase_id.order_line.filtered(lambda l: l.product_tmpl_id == stock_move.product_tmpl_id)
                    purchase_line_number = min(po_lines.mapped('sequence') or [0])

                for line in bill.invoice_line_ids:
                    if line.product_template_id == stock_move.product_tmpl_id:
                        quantity = line.quantity
                        price_unit = line.price_unit
                        currency_id = line.currency_conversion_id.id
                        currency_rate = line.inverse_conv_rate

                if self.major_id and self.major_id.id == stock_move.product_tmpl_id.asset_model_id.id:
                    data = {
                        'selected': False,
                        'product_tmpl_id': stock_move.product_tmpl_id.id,
                        'purchase_id': purchase_id.id if purchase_id else False,
                        'picking_id': picking_id.id,
                        'date_picking': picking_id.scheduled_date,
                        # 'analytic_account_id': 30,
                        'invoice_id': bill.id,
                        # 'invoice_line_id': 1,
                        # 'asset_location_id': ln.product_id.asset_location_id.id,
                        'qty': quantity,
                        'price_unit': price_unit,
                        'amount': quantity * price_unit,
                        'purchase_line_number': purchase_line_number,
                        'model_id': stock_move.product_tmpl_id.asset_model_id.id,
                        'stock_move_id': stock_move.id,
                        'currency_id': currency_id,
                        'currency_rate': currency_rate,
                        'from_receipt': True
                    }
                    lines.append((0, 0, data))

            sql = """
                SELECT afl.id as afl_id from account_fpjp_line afl LEFT JOIN account_fpjp af ON af.id = afl.account_fpjp_id
                    WHERE afl.type in ('asset','asset_service') AND af.state = 'done' AND afl.is_asset isnull
                """
            self.env.cr.execute(sql)
            fpjp_lines = self.env.cr.dictfetchall()
            # fpjp_lines = self.env['account.fpjp.line'].search([('type', '=', 'asset'), ('account_fpjp_id.state', '=', 'done')])
            for line in fpjp_lines:
                fpjp = self.env['account.fpjp.line'].browse(line['afl_id'])
                is_posted = True
                bill = False
                product_id = False
                quantity = 0
                price_unit = 0
                currency_id = False
                currency_rate = 1
                for bill_no in fpjp.bill_line_ids:
                    bill = bill_no.move_id.id
                    product_id = bill_no.product_template_id
                    quantity = bill_no.quantity
                    price_unit = bill_no.price_unit
                    currency_id = bill_no.currency_conversion_id.id
                    currency_rate = bill_no.inverse_conv_rate
                    if bill_no.move_id.state != 'posted':
                        is_posted = False
                        break
                # if is_posted and self.major_id and product_id and self.major_id.id == product_id.asset_model_id.id:
                data = {
                    'selected': False,
                    'product_tmpl_id': product_id,
                    'model_id': product_id.asset_model_id.id if product_id and product_id.asset_model_id else False,
                    'date_picking': fpjp.account_fpjp_id.fpjp_date,
                    'invoice_id': bill,
                    'qty': quantity,
                    'price_unit': price_unit,
                    'amount': quantity * price_unit,
                    'from_fpjp': True,
                    'fpjp_line_id': fpjp.id,
                    'fpjp_id': fpjp.account_fpjp_id.id,
                    'desc_fpjp': fpjp.name,
                    'currency_id': currency_id,
                    'currency_rate': currency_rate,
                }
                lines.append((0, 0, data))

            self.line_ids = lines
            self.asset_line_ids = lines
        else:
            lines = []
            sql = """
                SELECT sp.id as sp_id, sm.id as sm_id, am.id as am_id from stock_move sm LEFT JOIN stock_picking sp ON sp.id = sm.picking_id 
                    LEFT JOIN stock_picking_type spt ON spt.id = sp.picking_type_id
                    LEFT JOIN product_template pt ON pt.id = sm.product_tmpl_id
                    LEFT JOIN account_move am ON am.picking_journal_bill_id = sp.id
                    WHERE spt.code = 'incoming' AND sp.state_fppr = 'ap_invoice' AND sp.purchase_dua_id notnull AND 
                    sm.is_asset isnull AND am.state = 'posted' AND pt.is_asset <> false
                """
            self.env.cr.execute(sql)
            picking_lines = self.env.cr.dictfetchall()

            for picking in picking_lines:
                bill = self.env['account.move'].browse(picking['am_id'])
                stock_move = self.env['stock.move'].browse(picking['sm_id'])
                picking_id = self.env['stock.picking'].browse(picking['sp_id'])
                purchase_id = picking_id.purchase_dua_id
                purchase_line_number = 0
                quantity = 0
                price_unit = 0
                currency_id = False
                currency_rate = 1

                if purchase_id:
                    po_lines = purchase_id.order_line.filtered(lambda l: l.product_tmpl_id == stock_move.product_tmpl_id)
                    purchase_line_number = min(po_lines.mapped('sequence') or [0])

                for line in bill.invoice_line_ids:
                    if line.product_template_id == stock_move.product_tmpl_id:
                        quantity = line.quantity
                        price_unit = line.price_unit
                        currency_id = line.currency_conversion_id.id
                        currency_rate = line.inverse_conv_rate

                data = {
                    'selected': False,
                    'product_tmpl_id': stock_move.product_tmpl_id.id,
                    'purchase_id': purchase_id.id if purchase_id else False,
                    'picking_id': picking_id.id,
                    'date_picking': picking_id.scheduled_date,
                    # 'analytic_account_id': 30,
                    'invoice_id': bill.id,
                    # 'invoice_line_id': 1,
                    # 'asset_location_id': ln.product_id.asset_location_id.id,
                    'qty': quantity,
                    'price_unit': price_unit,
                    'amount': quantity * price_unit,
                    'purchase_line_number': purchase_line_number,
                    'model_id': stock_move.product_tmpl_id.asset_model_id.id if stock_move.product_tmpl_id else stock_move.product_tmpl_id.asset_model_id.id,
                    'stock_move_id': stock_move.id,
                    'currency_id': currency_id,
                    'currency_rate': currency_rate,
                    'from_receipt': True
                }
                lines.append((0, 0, data))

            sql = """
                SELECT afl.id as afl_id from account_fpjp_line afl LEFT JOIN account_fpjp af ON af.id = afl.account_fpjp_id
                    WHERE afl.type in ('asset','asset_service') AND af.state = 'done' AND afl.is_asset isnull
                """
            self.env.cr.execute(sql)
            fpjp_lines = self.env.cr.dictfetchall()
            # fpjp_lines = self.env['account.fpjp.line'].search([('type', '=', 'asset'), ('account_fpjp_id.state', '=', 'done')])
            for line in fpjp_lines:
                fpjp = self.env['account.fpjp.line'].browse(line['afl_id'])
                is_posted = True
                bill = False
                product_id = False
                quantity = 0
                price_unit = 0
                currency_id = False
                currency_rate = 1
                for bill_no in fpjp.bill_line_ids:
                    bill = bill_no.move_id.id
                    product_id = bill_no.product_template_id
                    quantity = bill_no.quantity
                    price_unit = bill_no.price_unit
                    currency_id = bill_no.currency_conversion_id.id
                    currency_rate = bill_no.inverse_conv_rate
                    if bill_no.move_id.state != 'posted':
                        is_posted = False
                        break
                if is_posted:
                    data = {
                        'selected': False,
                        'product_tmpl_id': product_id,
                        'model_id': product_id.asset_model_id.id if product_id and product_id.asset_model_id else False,
                        'date_picking': fpjp.account_fpjp_id.fpjp_date,
                        'invoice_id': bill,
                        'qty': quantity,
                        'price_unit': price_unit,
                        'amount': quantity * price_unit,
                        'from_fpjp': True,
                        'fpjp_line_id': fpjp.id,
                        'fpjp_id': fpjp.account_fpjp_id.id,
                        'desc_fpjp': fpjp.name,
                        'currency_id': currency_id,
                        'currency_rate': currency_rate,
                    }
                    lines.append((0, 0, data))

            self.line_ids = lines
            self.asset_line_ids = lines

    @api.onchange('major_id', 'type', 'selected_asset_id', 'is_selected_all', 'line_ids')
    def _onchange_journal(self):
        if self.type == 'merge':
            self.journal_id = self.major_id.journal_id.id
        elif self.type == 'split':
            check_line1 = self.line_ids.filtered(lambda x: x.model_id and x.selected)
            check_line2 = self.line_ids.filtered(lambda x: x.model_id)
            check = check_line1 or check_line2
            if check:
                self.journal_id = check[0].model_id.journal_id.id
            else:
                self.journal_id = False
        elif self.type == 'add':
            self.journal_id = self.selected_asset_id.journal_id.id

    @api.onchange('is_selected_all')
    def onchange_selected_all(self):
        if self.is_selected_all:
            for line in self.line_ids:
                line.selected = True

    @api.onchange('type')
    def onchange_type_line(self):
        for line in self.line_ids:
            line.date_acquisition = self.date

    @api.onchange('selected_asset_id')
    def onchange_selected_asset_id(self):
        if self.selected_asset_id:
            if self.selected_asset_id.method_period == '1':
                self.age_type = 'month'
            else:
                self.age_type = 'year'

    def button_generate(self):
        """ function to generate assets """
        # self._check_lines()  # check first
        details = self.line_ids.filtered(lambda x: x.selected)
        journal = self.journal_id.id  # always use the journal
        if not self.type:  # nothing, proceed
            for line in details:
                # name comes from purchase, product code and name
                name = '%s %s %s' % (
                    line.purchase_id.name, line.product_tmpl_id.default_code,
                    line.product_tmpl_id.name)
                source_line = []
                vals = {
                    # 'invoice_id': line.invoice_id.id,
                    'invoice_name': line.invoice_id.name,
                    'invoice_date': line.invoice_id.date,
                    'invoice_line_number': line.line_number,
                    # 'purchase_id': line.purchase_id.id,
                    'purchase_name': line.purchase_id.name,
                    'purchase_line_number': line.purchase_line_number,
                    'description': line.product_id.display_name,
                    'amount': line.amount,
                    'product_id': line.product_id.id,
                }
                source_line.append((0, 0, vals))
                data = {
                    'name': name,
                    'qty': line.qty,
                    'purchase_id': line.purchase_id.id,
                    'picking_id': line.picking_id.id,
                    'model_id': line.model_id.id,
                    'original_value': line.amount,
                    'acquisition_date': self.date_acquisition,
                    'date_received': line.date_acquisition,
                    'state': 'draft',
                    'asset_type': 'purchase',
                    'origin_ids': [(4, line.invoice_line_id.id)],
                    # 'source_line_ids': source_line,
                }

                # create data, call _onchange_model_id, then write cache
                new_asset = self.env['account.asset'].new(data)
                new_asset._onchange_model_id()  # to assign journal and accounts
                vals = new_asset._convert_to_write(new_asset._cache)
                asset = self.env['account.asset'].create(vals)
                asset.with_context({
                    'product_id': line.product_id,
                    'journal_id': journal,
                }).action_move_create(partner_id=line.partner_id, date_acc=self.date)
                line.stock_move_id.is_asset = True

        elif self.type == 'merge':  # merge create 1 asset and 1 journal
            name = self.asset_name
            product = False
            picking = False
            credit_account = False
            source_line = []
            qty = 0
            for detail in details:
                if detail.product_tmpl_id:
                    product = detail.product_tmpl_id
                    credit_account = detail.product_tmpl_id.property_account_expense_id.id
                if detail.picking_id:
                    picking = detail.picking_id
                vals = {
                    # 'invoice_id': detail.invoice_id.id,
                    # 'purchase_id': detail.purchase_id.id,
                    'invoice_name': detail.invoice_id.name,
                    'purchase_name': detail.purchase_id.name if detail.purchase_id else '',
                    'invoice_line_number': detail.line_number,
                    'purchase_line_number': detail.purchase_line_number,
                    'description': detail.product_tmpl_id.display_name if detail.product_tmpl_id else detail.desc_fpjp,
                    'amount': detail.amount,
                    'product_id': detail.product_tmpl_id.id if detail.product_tmpl_id else False,
                }
                source_line.append((0, 0, vals))
                qty += detail.qty
                if detail.stock_move_id:
                    detail.stock_move_id.is_asset = True
                if detail.fpjp_line_id:
                    if not credit_account:
                        credit_account = detail.fpjp_line_id.coa_id.id
                    detail.fpjp_line_id.is_asset = True
            total = sum(x.amount * x.currency_rate for x in details)
            data = {
                'name': name,
                'qty': qty,
                'picking_id': picking if picking else False,
                'model_id': self.major_id.id,
                'account_asset_id': self.major_id.account_asset_id.id,
                'original_value': total,
                'acquisition_date': self.date_acquisition,
                'date_received': self.date_acquisition,
                'state': 'draft',
                'asset_type': 'purchase',
                # 'origin_ids': [(4, x.invoice_line_id.id) for x in details],
                # 'source_line_ids': source_line,
            }

            # create data, call _onchange_model_id, then write cache
            new_asset = self.env['account.asset'].new(data)
            new_asset._onchange_model_id()  # to assign journal and accounts
            vals = new_asset._convert_to_write(new_asset._cache)
            asset = self.env['account.asset'].create(vals)
            asset.with_context({
                'name': self.asset_name,
                'debit_account_id': self.major_id.account_asset_id.id,
                'credit_account_id': credit_account,
                'product_id': product,
                'journal_id': journal,
            }).action_move_create(False, date_acc=self.date)

        elif self.type == 'split':  # based on selected line, loop qty
            # NOTE: for split, take the price_unit instead of amount
            # line = details
            product_tmpl_service = details.filtered(lambda x: x.product_tmpl_id.is_asset_service)
            total_service = 0
            currency_service = False
            for service_tmpl in product_tmpl_service:
                if service_tmpl.stock_move_id:
                    service_tmpl.stock_move_id.is_asset = True
                if service_tmpl.fpjp_line_id:
                    service_tmpl.fpjp_line_id.is_asset = True
                total_service += service_tmpl.amount * service_tmpl.currency_rate
                currency_service = service_tmpl.currency_id
            qty_non_service = 0
            for line in details.filtered(lambda x: not x.product_tmpl_id.is_asset_service):
                qty_non_service += line.qty
            service_amount = total_service / qty_non_service if qty_non_service != 0 else 0
            for line in details.filtered(lambda x: not x.product_tmpl_id.is_asset_service):
                for x in range(int(line.qty)):
                    # name comes from purchase, product code and name
                    if line.stock_move_id:
                        name = '%s %s %s (%s)' % (
                            line.purchase_id.name, line.purchase_id.description if line.purchase_id.description else '',
                            line.product_tmpl_id.name, x + 1)
                    else:
                        name = '%s %s' % (line.fpjp_id.name, line.desc_fpjp)
                    source_line = []
                    vals = {
                        # 'invoice_id': line.invoice_id.id,
                        'invoice_name': line.invoice_id.name,
                        'invoice_line_number': line.line_number,
                        # 'purchase_id': line.purchase_id.id,
                        'purchase_name': line.purchase_id.name if line.purchase_id else '',
                        'purchase_line_number': line.purchase_line_number,
                        'description': line.product_tmpl_id.display_name if line.product_tmpl_id else line.desc_fpjp,
                        'amount': line.amount,
                        'product_id': line.product_id.id if line.product_id else False,
                    }
                    source_line.append((0, 0, vals))
                    if currency_service == line.currency_id:
                        original_value = ((line.amount * line.currency_rate) / line.qty) + service_amount
                    else:
                        if currency_service and currency_service.name == 'IDR':
                            original_value = ((line.amount * line.currency_rate) / line.qty) + (service_amount / line.currency_rate)
                        else:
                            original_value = ((line.amount * line.currency_rate) / line.qty) + (service_amount * line.currency_rate)
                    data = {
                        'name': name,
                        'qty': 1,
                        'purchase_id': line.purchase_id.id if line.purchase_id else False,
                        'picking_id': line.picking_id.id if line.picking_id else False,
                        'model_id': line.product_tmpl_id.asset_model_id.id,
                        'account_asset_id': line.product_tmpl_id.asset_model_id.account_asset_id.id,
                        'original_value': original_value,
                        'acquisition_date': self.date_acquisition,
                        'date_received': line.date_acquisition,
                        'state': 'draft',
                        'asset_type': 'purchase',
                        # 'origin_ids': [(4, line.invoice_line_id.id)],
                        # 'source_line_ids': source_line,
                        'original_value_currency': line.amount,
                        'currency_rate': line.currency_rate,
                        'currency_id': line.currency_id.id,
                    }

                    # create data, call _onchange_model_id, then write cache
                    new_asset = self.env['account.asset'].new(data)
                    new_asset._onchange_model_id()  # to assign journal and accounts
                    vals = new_asset._convert_to_write(new_asset._cache)
                    asset = self.env['account.asset'].create(vals)
                    asset.with_context({
                        'name': name,
                        'debit_account_id': line.product_tmpl_id.asset_model_id.account_asset_id.id,
                        'credit_account_id': line.product_tmpl_id.property_account_expense_id.id if line.product_tmpl_id else line.fpjp_line_id.coa_id.id,
                        'product_id': line.product_id,
                        'journal_id': journal,
                    }).action_move_create(partner_id=line.partner_id, date_acc=self.date)
                if line.stock_move_id:
                    line.stock_move_id.is_asset = True
                if line.fpjp_line_id:
                    line.fpjp_line_id.is_asset = True

        elif self.type == 'add':
            self.selected_asset_id.write({'amount_add': self.amount_add})
            self.selected_asset_id.compute_depreciation_list()
            self.selected_asset_id.method_number += self.age_add
            book_value = self.selected_asset_id.book_value
            original_value = self.selected_asset_id.original_value
            self.selected_asset_id.book_value = self.amount_add
            self.selected_asset_id.original_value = self.amount_add
            product = False
            picking = False
            credit_account = False
            for detail in details:
                if detail.product_tmpl_id:
                    product = detail.product_tmpl_id
                    credit_account = detail.product_tmpl_id.property_account_expense_id.id
                if detail.picking_id:
                    picking = detail.picking_id
                if detail.stock_move_id:
                    detail.stock_move_id.is_asset = True
                if detail.fpjp_line_id:
                    if not credit_account:
                        credit_account = detail.fpjp_line_id.coa_id.id
                    detail.fpjp_line_id.is_asset = True
            self.selected_asset_id.with_context({
                'name': self.selected_asseet_id.name,
                'debit_account_id': self.selected_asset_id.model_id.account_asset_id.id,
                'credit_account_id': credit_account,
                'product_id': product,
                'journal_id': journal,
            }).action_move_create(False, date_acc=self.date)
            self.selected_asset_id.book_value = book_value + self.amount_add
            self.selected_asset_id.original_value = original_value + self.amount_add
            for line in details:
                if line.stock_move_id:
                    line.stock_move_id.is_asset = True
                if line.fpjp_line_id:
                    line.fpjp_line_id.is_asset = True


class InheritWizardMassAssetDetail(models.TransientModel):
    _inherit = 'wizard.mass.asset.detail'

    generate_id = fields.Many2one('wizard.mass.asset.generate', 'Generate', required=False)
    company_id = fields.Many2one('res.company', 'Company',
                                 required=False)
    type = fields.Selection(related='generate_id.type')
    selected = fields.Boolean('Selected', default=True, required=False)
    purchase_id = fields.Many2one('purchase.order', 'Purchase Order', required=False)
    partner_id = fields.Many2one('res.partner', 'Vendor', required=False)
    picking_id = fields.Many2one('stock.picking', 'Receipt Ref.', required=False)
    date_picking = fields.Date('Reciept Date', required=False)
    product_id = fields.Many2one('product.product', 'Product', required=False, related='')
    product_code = fields.Char('Product Code', required=False)
    invoice_id = fields.Many2one('account.move', 'Invoice', required=False)
    invoice_line_id = fields.Many2one('account.move.line', 'Invoice Line', required=False)
    line_number = fields.Integer('Line No', required=False)
    qty = fields.Float('Qty', required=False)
    date_acquisition = fields.Date('Acquisition Date', required=False)
    currency_id = fields.Many2one('res.currency', string='Currency', default=lambda self: self.env.company.currency_id.id)
    amount = fields.Monetary('Acquisition Value', required=False, currency_field='currency_id')
    price_unit = fields.Float('Price Unit', required=False)
    model_id = fields.Many2one('account.asset', 'Asset Category', required=False)
    analytic_account_id = fields.Many2one(
        'account.analytic.account', 'Analytic Account', required=False)
    purchase_line_number = fields.Integer('PO line no')
    stock_move_id = fields.Many2one('stock.move', 'Stock Move')
    from_receipt = fields.Boolean('From Receipt')
    from_fpjp = fields.Boolean('From FPJP')
    fpjp_line_id = fields.Many2one('account.fpjp.line', 'FPJP Line')
    fpjp_id = fields.Many2one('account.fpjp', 'FPJP')
    desc_fpjp = fields.Char('Description FPJP')
    asset_location_id = fields.Many2one('asset.location', string='Asset Location')
    product_tmpl_id = fields.Many2one('product.template', 'Product')
    currency_rate = fields.Float(string='Rate')



class WizardMassAssetLine(models.TransientModel):
    _name = 'wizard.mass.asset.line'

    generate_id = fields.Many2one('wizard.mass.asset.generate', 'Generate', required=False)
    company_id = fields.Many2one('res.company', 'Company',
                                 required=False)
    type = fields.Selection(related='generate_id.type')
    selected = fields.Boolean('Selected', default=True, required=False)
    purchase_id = fields.Many2one('purchase.order', 'Purchase Order', required=False)
    partner_id = fields.Many2one('res.partner', 'Vendor', required=False)
    picking_id = fields.Many2one('stock.picking', 'Receipt Ref.', required=False)
    date_picking = fields.Date('Reciept Date', required=False)
    product_id = fields.Many2one('product.product', 'Product', required=False)
    product_code = fields.Char('Product Code', required=False)
    invoice_id = fields.Many2one('account.move', 'Invoice', required=False)
    invoice_line_id = fields.Many2one('account.move.line', 'Invoice Line', required=False)
    line_number = fields.Integer('Line No', required=False)
    qty = fields.Float('Qty', required=False)
    date_acquisition = fields.Date('Acquisition Date', required=False)
    currency_id = fields.Many2one('res.currency', string='Currency', default=lambda self: self.env.company.currency_id.id)
    amount = fields.Monetary('Acquisition Value', required=False, currency_field='currency_id')
    price_unit = fields.Float('Price Unit', required=False)
    model_id = fields.Many2one('account.asset', 'Asset Category', required=False)
    analytic_account_id = fields.Many2one(
        'account.analytic.account', 'Analytic Account', required=False)
    purchase_line_number = fields.Integer('PO line no')
    stock_move_id = fields.Many2one('stock.move', 'Stock Move')
    from_receipt = fields.Boolean('From Receipt')
    from_fpjp = fields.Boolean('From FPJP')
    fpjp_line_id = fields.Many2one('account.fpjp.line', 'FPJP Line')
    fpjp_id = fields.Many2one('account.fpjp', 'FPJP')
    desc_fpjp = fields.Char('Description FPJP')
    asset_location_id = fields.Many2one('asset.location', string='Asset Location')
    product_tmpl_id = fields.Many2one('product.template', 'Product')
    currency_rate = fields.Float(string='Rate')
