from datetime import date
from odoo import api, fields, models, _
from odoo.tools import float_compare, float_is_zero
from odoo.exceptions import UserError, ValidationError

import calendar
from datetime import date, datetime
from dateutil.relativedelta import relativedelta


class AssetProgress(models.Model):
    _inherit = 'asset.progress'

    is_selected_all = fields.Boolean('Select All Lines', default=False)
    asset_group_id = fields.Many2one('account.asset.group', string='Asset Minor')
    stock_location_id = fields.Many2one('asset.location', string='Asset Location')
    phase_project_cip_id = fields.Many2one('phase.project.cip', string='Project')

    @api.onchange('model_id')
    def _onchange_model(self):
        self.journal_id = self.model_id.journal_id.id
    
    @api.onchange('phase_project_cip_id')
    def _onchange_phase_project_cip_id(self):
        if self.phase_project_cip_id:
            list_cip = self.env['account.asset.cip'].search([
                ('phase_project_cip_id', '=', self.phase_project_cip_id.id),
                ('is_asset', '=', False),
                ('cip_status', 'in', ['draft', False]),
            ])
            lines = []
            for cip in list_cip:
                data = {
                    'selected': False,
                    'cip_id': cip.id,
                }
                lines.append((0, 0, data))
            self.line_ids = lines
        else:
            self.line_ids = [(5, 0, 0)]


    @api.depends('line_ids', 'line_ids.selected', 'line_ids.original_value', 'is_selected_all')
    def _compute_total(self):
        for rec in self:
            rec.amount_total = sum(x.original_value for x in rec.line_ids.filtered(lambda x: x.selected))

    @api.onchange('is_selected_all')
    def _onchange_is_selected_all(self):
        if self.is_selected_all:
            for line in self.line_ids:
                line.selected = True
        else:
            for line in self.line_ids:
                line.selected = False

    @api.model
    def default_get(self, fields):
        res = super(AssetProgress, self).default_get(fields)
        list_cip = self.env['account.asset.cip'].search([
            ('is_asset', '=', False),
            ('cip_status', 'in', ['draft', False]),
        ])
        lines = []
        for cip in list_cip:
            data = {
                'selected': False,
                'cip_id': cip.id,
            }
            lines.append((0, 0, data))
        res['line_ids'] = lines
        return res
        

    # @api.onchange('asset_cost_progress_id', 'model_id')
    # def onchange_asset_cost_progress(self):
    #     if self.asset_cost_progress_id and self.model_id:
    #         list_cip = self.env['account.asset.cip'].search([('cip_id', '=', self.asset_cost_progress_id.id), ('is_asset', '=', False), ('model_id', '=', self.model_id.id)])
    #         lines = []
    #         for cip in list_cip:
    #             data = {
    #                 'selected': False,
    #                 'cip_id': cip.id,
    #             }
    #             lines.append((0, 0, data))
    #         self.line_ids = lines

    def button_done(self):
        """ function to generate asset and journal items """
        for rec in self:
            lines = [x for x in rec.line_ids if x.selected]
            asset_major = self.line_ids.mapped('cip_id.model_id')
            if len(asset_major) > 1:
                raise UserError(_('You can only select one asset major. Please select only one asset major.'))
            for line in lines:
                line.cip_id.is_asset = True
            amt = sum(x.original_value for x in lines)
            vals = {
                'name': rec.name,
                'qty': 1,
                'model_id': rec.model_id.id,
                'asset_group_id': rec.asset_group_id.id,
                'stock_location_id': rec.stock_location_id.id,
                'acquisition_date': rec.date_acquisition,
                'date_received': rec.date,
                'original_value': amt,
                'asset_type': 'purchase',
                'state': 'draft',
                # 'origin_ids': [(4, x.move_line_id.id) for x in lines],
            }
            new_asset = self.env['account.asset'].new(vals)
            new_asset._onchange_model_id()  # to assign journal and accounts
            vals = new_asset._convert_to_write(new_asset._cache)
            asset = self.env['account.asset'].create(vals)
            context = {
                'name': rec.name,
                'journal_id': rec.journal_id.id,
                'debit_account_id': rec.model_id.account_asset_id.id,
                'credit_account_id': rec.asset_cost_progress_id.account_id.id,
            }
            asset.with_context(context).action_move_create()
            rec.write({'state': 'done'})
        return True


class AssetProgressLine(models.Model):
    _inherit = 'asset.progress.line'

    cip_id = fields.Many2one('account.asset.cip', 'CIP Name')
    parent_id = fields.Many2one('account.asset', 'Parent', related='cip_id.parent_id', store=True)
    acquisition_date = fields.Date('Acquisition Date', related='cip_id.acquisition_date', store=True)
    original_value = fields.Monetary('Original Value', related='cip_id.original_value', store=True)
    method = fields.Selection(
        selection=[
            ('linear', 'Straight Line'),
            ('degressive', 'Declining'),
            ('degressive_then_linear', 'Declining then Straight Line')
        ],
        string='Method', related='cip_id.method', store=True)
    book_value = fields.Monetary('Book Value', related='cip_id.book_value', store=True)
    value_residual = fields.Monetary('Depreciable Value', related='cip_id.value_residual', store=True)
    account_asset_id = fields.Many2one('account.account', 'Fixed Asset Account', related='cip_id.account_asset_id', store=True)
    account_depreciation_id = fields.Many2one('account.account', 'Depreciation Account', related='cip_id.account_depreciation_id', store=True)
    account_depreciation_expense_id = fields.Many2one('account.account', 'Expense Account', related='cip_id.account_depreciation_expense_id', store=True)
    currency_id = fields.Many2one('res.currency', 'Currency', related='cip_id.currency_id', store=True)

