<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data>

        <record id="inherit_view_account_asset_form_id_inherit_account_asset_list" model="ir.ui.view">
            <field name="name">account.asset.view.list.inherit</field>
            <field name="model">account.asset</field>
            <field name="inherit_id" ref="account_asset.view_account_asset_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//list" position="attributes">
                    <attribute name="delete">0</attribute>
                </xpath>
            </field>
        </record>

        <record id="inherit_view_account_asset_form_id_inherit_account_asset" model="ir.ui.view">
            <field name="name">account.asset.view.form.inherit.asset</field>
            <field name="model">account.asset</field>
            <field name="inherit_id" ref="account_asset.view_account_asset_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='prorata_date']" position="attributes">
                    <attribute name="required">prorata_computation_type not in (False, 'none')</attribute>
                </xpath>

                <xpath expr="//field[@name='account_depreciation_id']" position="before">
                    <field name="account_asset_id" invisible="state != 'model'" />
                </xpath>
                <xpath expr="//field[@name='name']" position="attributes">
                    <attribute name="readonly">0</attribute>
                </xpath>
                <xpath expr="//page/group/group/field[@name='analytic_distribution']" position="attributes">
                    <attribute name="readonly">state != 'draft'</attribute>
                </xpath>
                <xpath expr="//form" position="attributes">
                    <attribute name="delete">0</attribute>
                </xpath>
                <xpath expr="//field[@name='model_id']" position="attributes">
                    <attribute name="required">state != 'model'</attribute>
                </xpath>
                <xpath expr="//field[@name='asset_group_id']" position="attributes">
                    <attribute name="required">state != 'model'</attribute>
                </xpath>
                <xpath expr="//field[@name='acquisition_date']" position="attributes">
                    <attribute name="readonly">0</attribute>
                </xpath>
                <xpath expr="//field[@name='model_id']" position="attributes">
                    <attribute name="string">Asset Major</attribute>
                </xpath>
                <xpath expr="//field[@name='asset_group_id']" position="attributes">
                    <attribute name="string">Asset Minor</attribute>
                    <attribute name="domain">[('asset_major_id','=', model_id)]</attribute>
                </xpath>
                <field name='asset_group_id' position="after">
                    <field name="stock_location_id" string="Location" required="state != 'model'"/>
                </field>
                
                <xpath expr="//field[@name='name']" position="attributes">
                    <attribute name="readonly">state != 'draft'</attribute>
                </xpath>
                <xpath expr="//field[@name='asset_group_id']" position="attributes">
                    <attribute name="readonly">state != 'draft'</attribute>
                </xpath>
                <xpath expr="//field[@name='acquisition_date']" position="attributes">
                    <attribute name="readonly">state != 'draft'</attribute>
                </xpath>
                <xpath expr="//field[@name='stock_location_id']" position="attributes">
                    <attribute name="readonly">state != 'draft'</attribute>
                </xpath>

                <xpath expr="//div[@class='oe_title']" position="after">
                    <div invisible="state == 'model'">
                        <label for="tag_number" invisible="state == 'model'" />
                        <field name="tag_number" readonly="state != 'draft'" invisible="state == 'model'" />  
                    </div>
                    <div invisible="state == 'model'">
                        <label for="asset_no" invisible="state == 'model'" />
                        <field name="asset_no" readonly="state != 'draft'" invisible="state == 'model'" />  
                    </div>
                </xpath>

                <xpath expr="//page/group/group/field[@name='account_depreciation_id']" position="replace">
                    <field name="account_depreciation_id" domain="[                                     ('company_ids', 'parent_of', company_id),                                     ('account_type', 'in', (                                         'asset_fixed',                                         'asset_non_current',                                         'asset_current')                                     ),                                 ]" context="{                                     'default_account_type': 'asset_non_current',                                     'hide_model_on_account': state == 'model' and not id,                                     'account_type_domain': [('account_type', 'in', (                                         'asset_fixed',                                         'asset_non_current'))]}" required="1" readonly="state != 'draft'"/>
                </xpath>
                <xpath expr="//page/group/group/field[@name='account_depreciation_expense_id']" position="replace">
                    <field name="account_depreciation_expense_id" domain="[                                     ('company_ids', 'parent_of', company_id),                                     ('account_type', 'in', (                                         'expense_depreciation',                                         'expense')                                     ),                                 ]" context="{                                     'default_account_type': 'expense_depreciation',                                     'default_tax_ids': [],                                     'hide_model_on_account': state == 'model' and not id,                                     'account_type_domain': [('account_type', 'in', (                                         'expense_depreciation',                                         'expense'))]}" required="1" readonly="state != 'draft'"/>
                </xpath>
                <xpath expr="//field[@name='name']" position="attributes">
                    <attribute name="readonly">state not in ('model')</attribute>
                    <attribute name="required">state in ('model')</attribute>
                </xpath>

                <xpath expr="//page[@name='related_items']" position="replace">
    <!-- Tidak mengubah attribute invisible pada page depreciation_board -->
                </xpath>
                <xpath expr="//field[@name='journal_id']" position="after">
                    <field name="assets_type" required="1" widget="radio" options="{'horizontal': True}"/>
                    <field name="cip_category_id" invisible="state != 'model'"/>
                    <field name="cip_account_id" invisible="state != 'model'"/>
                    <field name="ir_sequence_id" invisible="state != 'model'"/>
                </xpath>

                <xpath expr="//field[@name='model_id']" position="after">
                    <field name="assets_type_id" invisible="state == 'model'"/>
                </xpath>
            </field>
        </record>

        <!-- form asset minor -->
        <record id="inherit_group_asset_inherit_id_inherit_account_asset_extension" model="ir.ui.view">
            <field name="name">account.asset.view.form.inherit</field>
            <field name="model">account.asset.group</field>
            <field name="inherit_id" ref="account_asset_extension.group_asset_inherit"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='asset_major_id']" position="attributes">
                    <attribute name="domain">[('state', '=', 'model')]</attribute>
                </xpath>
            </field>
        </record>

        <record id="inherit_view_account_asset_form_id_inherit_account_asset_extension" model="ir.ui.view">
            <field name="name">account.asset.view.form.inherit</field>
            <field name="model">account.asset</field>
            <field name="inherit_id" ref="account_asset_extension.view_account_asset_form"/>
            <field name="arch" type="xml">
                <xpath expr="//button[@name='button_accumulate']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='is_internal']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//page[@name='asset_origin']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//page[.//field[@name='depreciation_line_ids']]" position="replace">
                    <!-- Comment -->
                </xpath>
                <xpath expr="//page[@name='depreciation_board']" position="replace">
    <!-- Tidak mengubah attribute invisible pada page depreciation_board -->
                </xpath>

                <xpath expr="//page[@name='related_items']" position="replace">
    <!-- Tidak mengubah attribute invisible pada page depreciation_board -->
                </xpath>
            </field>
        </record>



        <record id="view_asset_location_form_form" model="ir.ui.view">
            <field name="name">asset.location.form</field>
            <field name="model">asset.location</field>
            <field name="inherit_id" ref="account_asset_extension.view_asset_location_form" />
            <field name="arch" type="xml">
                <xpath expr="//sheet" position="replace">
                    <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button type="object"
                                name="action_view_assets"
                                class="oe_stat_button"
                                icon="fa-cubes"
                                string="Assets"
                                invisible="asset_count == 0">
                            <field name="asset_count" widget="statinfo"/>
                        </button>
                    </div>
                        <widget name="web_ribbon" title="Archived" bg_color="bg-danger" invisible="active"/>
                        <group col="4">
                            <field name="code" required="1"/>
                            <field name="name" required="1"/>
                            <!-- <field name="city_id" required="1"/>
                            <field name="area_id" domain="[('city_id', '=', city_id)]" required="1"/>
                            <field name="building_id" domain="[('city_id', '=', city_id), ('area_id', '=', area_id)]" required="1"/>
                            <field name="floor_id" required="1"/> -->
                        </group>
                        <group>
                            <field name="note"/>
                        </group>
                    </sheet>
                </xpath>
            </field>
        </record>

        <record id="view_asset_location_form_list" model="ir.ui.view">
            <field name="name">asset.location.list</field>
            <field name="model">asset.location</field>
            <field name="inherit_id" ref="account_asset_extension.view_asset_location_tree" />
            <field name="arch" type="xml">
                <xpath expr="//list" position="replace">
                    <list>
                        <field name="code"/>
                        <field name="name"/>
                        <!-- <field name="city_id"/>
                        <field name="area_id"/>
                        <field name="building_id"/>
                        <field name="floor_id"/> -->
                        <field name="note"/>
                    </list>
                </xpath>
            </field>
        </record>



        <record id="view_account_asset_group_form" model="ir.ui.view">
            <field name="name">account.asset.group.form</field>
            <field name="model">account.asset.group</field>
            <field name="inherit_id" ref="account_asset.asset_group_form_view" />
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="replace">
                        <field name="name" required="1"/>
                </xpath>
            </field>
        </record>







    </data>


</odoo>
