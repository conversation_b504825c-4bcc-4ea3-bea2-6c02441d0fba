<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data>
    
        <record id="inherit_cip_configuration_form_view_id_inherit_account_asset_extension" model="ir.ui.view">
            <field name="name">cip.configuration.view.form.inherit</field>
            <field name="model">cip.configuration</field>
            <field name="inherit_id" ref="account_asset_extension.cip_configuration_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='account_id']" position="after">
                    <field name="model_id" invisible="1" />
                </xpath>
            </field>
        </record>

        <record id="account_asset_extension.cip_configuration_menu" model="ir.ui.menu">
            <field name="name">CIP Category</field>
        </record>

        <record id="view_tree_account_asset_ui" model="ir.ui.view">
            <field name="name">account_asset.tree.view</field>
            <field name="model">account.asset.cip</field>
            <field name="arch" type="xml">
                <list string="Assets" create="0" edit="0" delete="0" decoration-info="(state == 'draft')" decoration-muted="(state in ['close', 'cancelled'])" decoration-warning="(state == 'close' and value_residual != 0)" sample="1">
                    <field name="name"/>
                    <field name="parent_id" optional="hide"/>
                    <field name="acquisition_date" readonly="state != 'draft'"/>
                    <field name="original_value" readonly="state != 'draft'"/>
                    <field name="method" readonly="state not in ['draft', 'model']"/>
                    <field name="book_value"/>
                    <field name="value_residual" widget="monetary"/>
                    <field name="account_asset_id" optional="hide" readonly="state == 'close'"/>
                    <field name="account_depreciation_id" optional="hide" readonly="state == 'close'"/>
                    <field name="account_depreciation_expense_id" optional="hide" readonly="state == 'close'"/>
                    <field name="currency_id" groups="base.group_multi_currency" optional="hide"/>
                    <field name="company_id" groups="base.group_multi_company" optional="hide" readonly="state != 'draft'"/>
                    <field name="company_id" groups="!base.group_multi_company" column_invisible="True" readonly="state != 'draft'"/>
                    <field name="asset_properties"/>
                    <field name="cip_status" string="Status"/>
                    <field name="activity_exception_decoration" widget="activity_exception"/>
                    <field name="activity_ids" widget="list_activity" optional="hide"/>
                </list>
            </field>
        </record>
        
        <record  id="view_form_account_asset_ui" model="ir.ui.view">
            <field name="name">account_asset.form.view</field>
            <field name="model">account.asset.cip</field>
            <field name="arch" type="xml">
                <form string="Asset"  create="0" edit="0" delete="0">
                    <field name="company_id" invisible="1" readonly="state != 'draft'"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="account_type" invisible="1"/>
                    <field name="account_asset_id" invisible="1" readonly="state == 'close'"/>
                    <field name="display_account_asset_id" invisible="1"/>
                    <field name="state" invisible="1"/>
                    <field name="total_depreciation_entries_count" invisible="1"/>
                    <field name="parent_id" invisible="1"/>
                    <header>
                        <button name="validate" invisible="1" string="Confirm" type="object" class="oe_highlight" data-hotkey="q"/>
                        <button type="object" name="compute_depreciation_board" string="Compute Depreciation" invisible="1" data-hotkey="d"/>
                        <button name="set_to_draft" string="Set to Draft" type="object" invisible="1"/>
                        <button name="set_to_running" string="Set to Running" type="object" invisible="1"/>
                        <button name="resume_after_pause" string="Resume Depreciation" type="object" class="oe_highlight" invisible="1"/>
                        <button name="action_asset_modify" invisible="1" string="Modify Depreciation" type="object" data-hotkey="e" class="oe_highlight"/>
                        <button name="action_save_model" invisible="1" string="Save as Model" type="object" data-hotkey="m"/>
                        <button name="set_to_cancelled" string="Cancel Asset" type="object" invisible="1" data-hotkey="x"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,open" invisible="1"/>
                    </header>
                    <sheet>
                        <field name="asset_progress_ids" invisible="1"/>
                        <div class="oe_button_box" name="button_box" invisible="state == 'model'">
<!--                            <button name="action_open_linked_assets" type="object" class="oe_stat_button" icon="fa-id-card-o" invisible="count_linked_asset == 0 or warning_count_assets">-->
<!--                                <field string="Asset(s)" name="count_linked_asset" widget="statinfo"/>-->
<!--                            </button>-->
<!--                            <button name="action_open_linked_assets" type="object" class="oe_stat_button text-danger" icon="fa-id-card-o" invisible="count_linked_asset == 0 or not warning_count_assets">-->
<!--                                <field string="Asset(s)" name="count_linked_asset" widget="statinfo"/>-->
<!--                            </button>-->
<!--                            <button class="oe_stat_button" name="open_entries" type="object" icon="fa-bars" data-hotkey="p">-->
<!--                                <field string="Posted Entries" name="depreciation_entries_count" widget="statinfo"/>-->
<!--                            </button>-->
<!--                            <button class="oe_stat_button" name="open_increase" type="object" icon="fa-chevron-circle-up" invisible="gross_increase_count == 0">-->
<!--                                <field string="Gross Increase" name="gross_increase_count" widget="statinfo"/>-->
<!--                            </button>-->
<!--                            <button class="oe_stat_button" string="Parent Asset" name="open_parent_id" type="object" icon="fa-bars" invisible="not parent_id"/>-->
                        </div>
                        <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                        <div class="oe_title">
                            <label for="name" name="name_label" invisible="state == 'model'"/>
                            <label for="name" name="model_name_label" string="Asset Model name" invisible="state != 'model'"/>
                            <h1>
                                <field name="name" placeholder="e.g. Laptop iBook" required="1"/>
                            </h1>
                        </div>
                        <group invisible="state != 'model'">
                            <group string="Depreciation Method">
                                <field name="method" required="1" readonly="state not in ['draft', 'model']"/>
                                <field name="method_progress_factor" widget="percentage" invisible="method == 'linear'" readonly="state not in ['draft', 'model']" required="method in ('degressive', 'degressive_then_linear')"/>
                                <label for="method_number" string="Duration"/>
                                <div class="o_row">
                                    <field name="method_number" required="1" readonly="state not in ['draft', 'model']"/>
                                    <field name="method_period" required="1" readonly="state not in ['draft', 'model']" nolabel="1"/>
                                </div>
                                <field name="prorata_computation_type" readonly="state not in ['draft', 'model']"/>
                                <label for="salvage_value_pct"/>
                                <div>
                                    <field name="salvage_value_pct" widget="percentage" class="oe_inline"/>
                                </div>
                            </group>
                            <group string="Accounting">
                                <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company" readonly="state != 'draft'"/>
                                <field name="account_asset_id" domain="[                                     ('company_ids', 'in', company_id),                                     ('account_type', 'in', (                                         'asset_fixed',                                         'asset_non_current',                                         'asset_current')                                     ),                                 ]" context="{                                     'default_account_type': 'asset_fixed',                                     'hide_model_on_account': state == 'model' and not id,                                     'account_type_domain': [('account_type', 'in', (                                         'asset_fixed',                                         'asset_non_current'))]}" groups="base.group_no_one" invisible="not display_account_asset_id" readonly="state == 'close'"/>
                                <field name="account_depreciation_id" domain="[                                     ('company_ids', 'in', company_id),                                     ('account_type', 'in', (                                         'asset_fixed',                                         'asset_non_current',                                         'asset_current')                                     ),                                 ]" context="{                                     'default_account_type': 'asset_non_current',                                     'hide_model_on_account': state == 'model' and not id,                                     'account_type_domain': [('account_type', 'in', (                                         'asset_fixed',                                         'asset_non_current'))]}" required="1" readonly="state == 'close'"/>
                                <field name="account_depreciation_expense_id" domain="[                                     ('company_ids', 'in', company_id),                                     ('account_type', 'in', (                                         'expense_depreciation',                                         'expense')                                     ),                                 ]" context="{                                     'default_account_type': 'expense_depreciation',                                     'default_tax_ids': [],                                     'hide_model_on_account': state == 'model' and not id,                                     'account_type_domain': [('account_type', 'in', (                                         'expense_depreciation',                                         'expense'))]}" required="1" readonly="state == 'close'"/>
                                <field name="journal_id" required="1" readonly="state not in ['draft', 'model']"/>
                            </group>
                        </group>
                        <notebook colspan="4" invisible="state == 'model'">
                            <page string="Asset" name="main_page">
                                <group>
                                    <field name="active" invisible="1"/>
                                    <field name="asset_properties" columns="2"/>
                                    <group string="Asset Values" invisible="state == 'model'" name="asset_values">
                                        <field name="original_value" widget="monetary" options="{'currency_field': 'currency_id'}" invisible="state == 'model'" readonly="state != 'draft'"/>
                                        <field name="gross_increase_value" widget="monetary" invisible="state == 'model' or gross_increase_value == 0" options="{'currency_field': 'currency_id'}"/>
                                        <field name="acquisition_date" invisible="state == 'model'" readonly="state != 'draft'"/>
                                        <field name="model_id" string="Asset Major" domain="[('state', '=', 'model')]" options="{'no_quick_create': True}" context="{                                                 'default_state': 'model',                                                 'default_account_asset_id': account_asset_id,                                                 'default_account_depreciation_id': account_depreciation_id,                                                 'default_account_depreciation_expense_id': account_depreciation_expense_id                                             }" readonly="state != 'draft'"/>
                                        <field name="asset_group_id" string="Asset Minor"/>
                                        <field name="phase_project_cip_id" />
                                    </group>
                                    <group string="Current Values" invisible="state == 'model'" name="current_values">
                                        <field name="salvage_value" widget="monetary" options="{'currency_field': 'currency_id'}" readonly="state != 'draft'"/>
                                        <label for="book_value"/>
                                        <span>
                                            <field name="book_value" class="oe_inline" widget="monetary" required="1" invisible="state == 'model'" options="{'currency_field': 'currency_id'}"/>
                                            <span invisible="salvage_value == 0">
                                                (incl.
                                                <field name="value_residual" nolabel="1" force_save="1" widget="monetary" invisible="state == 'model'" options="{'currency_field': 'currency_id'}"/>
                                                    depreciable)
                                            </span>
                                        </span>
                                    </group>
                                    <group string="Depreciation Method">
                                        <field name="method" required="1" readonly="state not in ['draft', 'model']"/>
                                        <field name="method_progress_factor" widget="percentage" invisible="method == 'linear'" readonly="state not in ['draft', 'model']" required="method in ('degressive', 'degressive_then_linear')"/>
                                        <label for="method_number" string="Duration"/>
                                        <div class="o_row">
                                            <field name="method_number" required="1" readonly="state not in ['draft', 'model']" class="oe_inline"/>
                                            <field name="method_period" required="1" readonly="state not in ['draft', 'model']" nolabel="1"/>
                                        </div>
                                        <field name="prorata_computation_type" readonly="state not in ['draft', 'model']"/>
                                        <field name="prorata_date" invisible="prorata_computation_type == 'none'" readonly="state != 'draft'"/>
                                    </group>
                                    <group string="Accounting">
                                        <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company" readonly="state != 'draft'"/>
                                        <field name="account_asset_id" domain="[                                             ('company_ids', 'in', company_id),                                             ('account_type', 'in', (                                                 'asset_fixed',                                                 'asset_non_current',                                                 'asset_current')                                             ),                                         ]" context="{                                             'default_account_type': 'asset_fixed',                                             'hide_model_on_account': state == 'model' and not id,                                             'account_type_domain': [('account_type', 'in', (                                                 'asset_fixed',                                                 'asset_non_current'))]}" invisible="not display_account_asset_id" readonly="state == 'close'"/>
                                        <field name="account_depreciation_id" domain="[                                             ('company_ids', 'in', company_id),                                             ('account_type', 'in', (                                                 'asset_fixed',                                                 'asset_non_current',                                                 'asset_current')                                             ),                                         ]" context="{                                             'default_account_type': 'asset_non_current',                                             'hide_model_on_account': state == 'model' and not id,                                             'account_type_domain': [('account_type', 'in', (                                                 'asset_fixed',                                                 'asset_non_current'))]}" required="1" readonly="state == 'close'"/>
                                        <field name="account_depreciation_expense_id" domain="[                                             ('company_ids', 'in', company_id),                                             ('account_type', 'in', (                                                 'expense_depreciation',                                                 'expense')                                             ),                                         ]" context="{                                             'default_account_type': 'expense_depreciation',                                             'hide_model_on_account': state == 'model' and not id,                                             'account_type_domain': [('account_type', 'in', (                                                 'expense_depreciation',                                                 'expense'))]}" required="1" readonly="state == 'close'"/>
                                        <field name="journal_id" required="1" readonly="state not in ['draft', 'model']"/>
                                        <field name="analytic_distribution" groups="analytic.group_analytic_accounting" widget="analytic_distribution" options="{'account_field': 'account_depreciation_expense_id', 'business_domain': 'general'}" readonly="0"/>
                                    </group>
                                    <group string="Value at Import" invisible="state == 'model'">
                                        <field name="already_depreciated_amount_import" string="Depreciated Amount" readonly="state != 'draft'"/>
                                    </group>
                                </group>
                            </page>
                            <page string="Depreciation Board" name="depreciation_board" invisible="total_depreciation_entries_count == 0">
                                <field name="depreciation_move_ids" mode="list" options="{'reload_whole_on_button': true}" readonly="state not in ['draft', 'open', 'paused']">
                                    <list string="Depreciation Lines" decoration-info="state == 'draft'" create="0" default_order="date asc, id asc" editable="top">
                                        <field name="currency_id" column_invisible="True" readonly="state in ['cancel', 'posted']"/>
                                        <field name="date" string="Depreciation Date" readonly="state in ['cancel', 'posted']"/>
                                        <field name="ref" invisible="0"/>
                                        <field name="reversal_move_ids" widget="deprec_lines_reversed" nolabel="1"/>
                                        <field name="depreciation_value" widget="monetary" string="Depreciation" options="{'currency_field': 'currency_id'}" readonly="state == 'posted'"/>
                                        <field name="asset_depreciated_value" readonly="1" force_save="1" options="{'currency_field': 'currency_id'}"/>
                                        <field name="asset_remaining_value" readonly="1" widget="monetary" force_save="1" options="{'currency_field': 'currency_id'}"/>
                                        <field name="name" readonly="1" string="Journal Entry"/>
                                        <field name="state" column_invisible="True"/>
                                    </list>
                                </field>
                            </page>
                            <page string="Bills" name="related_items" invisible="1">
                                <field name="original_move_line_ids" readonly="state == 'posted'" domain="[                                     ('parent_state', '=', 'posted'),                                     ('company_id', '=', company_id),                                     ('account_id.account_type', 'in', ('asset_fixed', 'asset_non_current', 'asset_current')),                                     ('move_id.move_type', 'in', ('in_invoice', 'in_refund', 'in_receipt', 'entry')),                                     ('move_id.asset_id', '=', False),                                 ]" class="original_move_line_ids_field" context="{'disable_preview': 1, 'list_view_ref': 'account_asset.view_move_line_tree_asset'}">
                                    <list create="0" no_open="1">
                                        <field name="date"/>
                                        <field name="move_name" string="Journal Entry" widget="open_move_widget"/>
                                        <field name="account_id"/>
                                        <field name="name"/>
                                        <field name="debit" widget="monetary"/>
                                        <field name="credit" widget="monetary"/>
                                        <field name="company_currency_id" column_invisible="True"/>
                                    </list>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                    <chatter/>
                </form>
            </field>
        </record>

        <record id="inherit_view_account_asset_form_id_inherit_account_asset_cip" model="ir.ui.view">
            <field name="name">account.asset.cip.view.form.inherit</field>
            <field name="model">account.asset.cip</field>
            <field name="inherit_id" ref="fixed_asset_inherit.view_form_account_asset_ui"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='account_depreciation_id']" position="before">
                    <field name="account_asset_id" required="state == 'model'" readonly="0"/>
                </xpath>
                <xpath expr="//page/group/group/field[@name='account_asset_id']" position="attributes">
                    <attribute name="required">state != 'model'</attribute>
                    <attribute name="readonly">state != 'draft'</attribute>
                </xpath>
                <xpath expr="//field[@name='name']" position="attributes">
                    <attribute name="readonly">0</attribute>
                </xpath>
                <xpath expr="//page/group/group/field[@name='analytic_distribution']" position="attributes">
                    <attribute name="readonly">state != 'draft'</attribute>
                </xpath>
                <xpath expr="//form" position="attributes">
                    <attribute name="delete">0</attribute>
                </xpath>
                <xpath expr="//field[@name='model_id']" position="attributes">
                    <attribute name="required">state != 'model'</attribute>
                </xpath>
                <xpath expr="//field[@name='asset_group_id']" position="attributes">
                    <attribute name="required">state != 'model'</attribute>
                </xpath>
                <xpath expr="//field[@name='acquisition_date']" position="attributes">
                    <attribute name="readonly">0</attribute>
                </xpath>
                <xpath expr="//field[@name='model_id']" position="attributes">
                    <attribute name="string">Asset Major</attribute>
                </xpath>
                <xpath expr="//field[@name='asset_group_id']" position="attributes">
                    <attribute name="string">Asset Minor</attribute>
                    <attribute name="domain">[('asset_major_id','=', model_id)]</attribute>
                </xpath>
                <field name='asset_group_id' position="after">
                    <field name="stock_location_id" string="Location" />
                </field>
                
                <xpath expr="//field[@name='name']" position="attributes">
                    <attribute name="readonly">state != 'draft'</attribute>
                </xpath>
                <xpath expr="//field[@name='asset_group_id']" position="attributes">
                    <attribute name="readonly">state != 'draft'</attribute>
                </xpath>
                <xpath expr="//field[@name='acquisition_date']" position="attributes">
                    <attribute name="readonly">state != 'draft'</attribute>
                </xpath>
                <xpath expr="//field[@name='stock_location_id']" position="attributes">
                    <attribute name="readonly">state != 'draft'</attribute>
                </xpath>

                <xpath expr="//page/group/group/field[@name='account_depreciation_id']" position="replace">
                    <field name="account_depreciation_id" domain="[                                     ('company_ids', 'parent_of', company_id),                                     ('account_type', 'in', (                                         'asset_fixed',                                         'asset_non_current',                                         'asset_current')                                     ),                                 ]" context="{                                     'default_account_type': 'asset_non_current',                                     'hide_model_on_account': state == 'model' and not id,                                     'account_type_domain': [('account_type', 'in', (                                         'asset_fixed',                                         'asset_non_current'))]}" required="1" readonly="state != 'draft'"/>
                </xpath>
                <xpath expr="//page/group/group/field[@name='account_depreciation_expense_id']" position="replace">
                    <field name="account_depreciation_expense_id" domain="[                                     ('company_ids', 'parent_of', company_id),                                     ('account_type', 'in', (                                         'expense_depreciation',                                         'expense')                                     ),                                 ]" context="{                                     'default_account_type': 'expense_depreciation',                                     'default_tax_ids': [],                                     'hide_model_on_account': state == 'model' and not id,                                     'account_type_domain': [('account_type', 'in', (                                         'expense_depreciation',                                         'expense'))]}" required="1" readonly="state != 'draft'"/>
                </xpath>
            </field>
        </record>


 


        <record id="account_asset_list_action" model="ir.actions.act_window">
            <field name="name">CIP</field>
            <field name="res_model">account.asset.cip</field>
            <field name="view_mode">list,form</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'list', 'view_id': ref('fixed_asset_inherit.view_tree_account_asset_ui')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('fixed_asset_inherit.view_form_account_asset_ui')})]"/>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                Click to start preparing a new CIP
                </p><p>
                </p>
            </field>
        </record>




        <menuitem
            id="menu_cip_asset"
            name="CIP"
            action="fixed_asset_inherit.account_asset_list_action"
            parent="account_asset_extension.menu_asset"
            sequence="1"/>

    </data>
</odoo>
