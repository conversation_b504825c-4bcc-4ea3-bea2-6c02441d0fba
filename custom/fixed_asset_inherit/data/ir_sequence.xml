<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data>
        <!-- Sequence for Asset -->
        <record id="sequence_asset" model="ir.sequence">
            <field name="name">Asset Sequence</field>
            <field name="code">account.asset.seq</field>
            <field name="prefix">AST/</field>
            <field name="suffix">/%(month)s/%(year)s</field>
            <field name="padding">5</field>
        </record>

        <!-- Sequence for CIP -->
        <record id="sequence_asset_cip" model="ir.sequence">
            <field name="name">Asset CIP Sequence</field>
            <field name="code">account.asset.cip.seq</field>
            <field name="prefix">CIP/</field>
            <field name="suffix">/%(month)s/%(year)s</field>
            <field name="padding">5</field>
        </record>
    
        
    
    </data>
    

</odoo>
