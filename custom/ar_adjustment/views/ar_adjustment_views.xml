<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- AR Adjustment Form View -->
    <record id="view_ar_adjustment_form" model="ir.ui.view">
        <field name="name">ar.adjustment.form</field>
        <field name="model">ar.adjustment</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_post" string="Confirm" type="object"
                            class="oe_highlight"
                            invisible="state != 'draft'"/>
                    <button name="action_cancel" string="Cancel" type="object"
                            class="oe_highlight"
                            invisible="state not in ('draft','posted')"/>
                    <field name="state" widget="statusbar"
                           statusbar_visible="draft,posted,cancel"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name" readonly="1" force_save="1"/>
                            <field name="journal_id"
                                   options="{'no_create': True}"
                                   required='1'
                                   readonly="state in ('posted', 'cancel')"/>
                            <field name="partner_id"
                                    required='1'
                                   options="{'no_create': True}"
                                   readonly="state in ('posted', 'cancel')"/>
                            <field name="invoice_id"
                                    required='1'
                                    force_save='1'
                                   options="{'no_create': True}"
                                   readonly="state in ('posted', 'cancel') or from_invoice"/>
                            <field name="from_invoice" invisible='1' />
                            <field name="type_adjustment"
                                required='1'
                                options="{'no_create': True}" readonly="1"/>
                        </group>
                        <group>
                            <field name="company_id" readonly="1" force_save="1"
                                   options="{'no_create': True}" />
                            <field name="transaction_date"
                                   readonly="state in ('posted','cancel')"/>
                            <field name="move_id" required="0" readonly="1" force_save="1"/>
                            <field name="period_id" invisible="1"/>
							<field name="currency_id" invisible="1"/>
                            <field name="total_amount" readonly="1" force_save='1' />
                        </group>
                    </group>
                     <notebook>
                        <page id="adjustment_tab" string="Adjustment Lines">
                            <field name="adjustment_line_ids" readonly="state in ('posted', 'cancel')" widget="one2many">
                                <list editable="bottom">
                                    <field name="line_type" required='1' />
                                    <field name="invoice_line_id" required='1' />
                                    <field name="invoice_amount" readonly="1" force_save='1' />
                                    <field name="amount" sum="Total"/>
                                    <field name="account_id" required='1' />
                                    <field name="counterpart_account_id" required='1' />
                                    <field name="currency_id" column_invisible="1"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- AR Adjustment List View -->
    <record id="view_ar_adjustment_list" model="ir.ui.view">
        <field name="name">ar.adjustment.list</field>
        <field name="model">ar.adjustment</field>
        <field name="arch" type="xml">
            <list>
                <field name="date"/>
                <field name="name"/>
                <field name="journal_id"/>
                <field name="invoice_id"/>
                <field name="type_adjustment"/>
                <field name="total_amount" sum="Total Amount"/>
                <field name="state"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <field name="currency_id" invisible="1"/>
            </list>
        </field>
    </record>

    <!-- AR Adjustment Search View -->
    <record id="view_ar_adjustment_search" model="ir.ui.view">
        <field name="name">ar.adjustment.search</field>
        <field name="model">ar.adjustment</field>
        <field name="arch" type="xml">
            <search string="Search AR Adjustments">
                <field name="name"/>
                <field name="partner_id"/>
                <field name="invoice_id"/>
                <field name="journal_id"/>
                <separator/>
                <filter string="Draft" name="draft" domain="[('state','=','draft')]"/>
                <filter string="Posted" name="posted" domain="[('state','=','posted')]"/>
                <filter string="Cancelled" name="cancelled" domain="[('state','=','cancel')]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="status" context="{'group_by':'state'}"/>
                    <filter string="Partner" name="partner" context="{'group_by':'partner_id'}"/>
                    <filter string="Journal" name="journal" context="{'group_by':'journal_id'}"/>
                    <filter string="Adjustment Type" name="type" context="{'group_by':'type_adjustment'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- AR Adjustment Action -->
    <record id="action_ar_adjustment" model="ir.actions.act_window">
        <field name="name">Adjustment Account Receivable</field>
        <field name="res_model">ar.adjustment</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_ar_adjustment_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new Account Receivable Adjustment
            </p>
        </field>
    </record>

    <!-- AR Adjustment Menu -->
    <menuitem id="menu_ar_adjustment"
              name="AR Adjustment"
              parent="account.menu_finance_receivables"
              sequence="16"
              action="action_ar_adjustment"/>

    <!-- AR Adjustment Line Form View -->
    <record id="view_ar_adjustment_line_form" model="ir.ui.view">
        <field name="name">ar.adjustment.line.form</field>
        <field name="model">ar.adjustment.line</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="line_type"/>
                            <field name="invoice_line_id"/>
                            <field name="invoice_amount" readonly="1" force_save='1' />
                            <field name="amount"/>
                        </group>
                        <group>
                            <field name="account_id"/>
                            <field name="counterpart_account_id"/>
                            <field name="currency_id" invisible="1"/>
                            <field name="company_id" groups="base.group_multi_company" invisible="1"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- AR Adjustment Line List View -->
    <record id="view_ar_adjustment_line_list" model="ir.ui.view">
        <field name="name">ar.adjustment.line.list</field>
        <field name="model">ar.adjustment.line</field>
        <field name="arch" type="xml">
            <list editable="bottom">
                <field name="line_type"/>
                <field name="invoice_line_id"/>
                <field name="invoice_amount" readonly="1" force_save='1' />
                <field name="amount" sum="Total"/>
                <field name="account_id"/>
                <field name="counterpart_account_id"/>
                <field name="currency_id" invisible="1"/>
                <field name="company_id" groups="base.group_multi_company" invisible="1"/>
            </list>
        </field>
    </record>
</odoo>
