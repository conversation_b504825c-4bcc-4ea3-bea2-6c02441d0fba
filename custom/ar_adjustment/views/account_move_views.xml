<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Add AR Adjustment fields to Invoice Form -->
        <record id="view_move_form_inherit_ar_adjustment" model="ir.ui.view">
            <field name="name">account.move.form.inherit.ar.adjustment</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <!-- Add button to create AR Adjustment -->
                <xpath expr="//header" position="inside">
                    <button name="action_create_ar_adjustment" 
                            string="Create AR Adjustment" 
                            type="object" 
                            class="oe_highlight"
                            invisible="move_type not in ('out_invoice', 'out_refund') or state != 'posted' or not amount_residual &gt; 0"
                            groups="account.group_account_user"/>
                </xpath>
                
                <!-- Add AR Adjustments tab -->
                <xpath expr="//notebook" position="inside">
                    <page string="AR Adjustments" name="ar_adjustments" 
                          invisible="move_type not in ('out_invoice', 'out_refund')">
                        <field name="adjustment_ids" readonly="1" widget="one2many">
                            <list editable="bottom">
                                <field name="name"/>
                                <field name="transaction_date"/>
                                <field name="type_adjustment"/>
                                <field name="total_amount"/>
                                <field name="state"/>
                            </list>
                        </field>
                        <group class="oe_subtotal_footer oe_right">
                            <field name="adjustment_amount" widget="monetary"/>
                        </group>
                    </page>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
