# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime
from dateutil import relativedelta
from odoo.addons.account.wizard.account_payment_register import AccountPaymentRegister

class InvoicingPeriodLine(models.Model):
    _inherit = 'invoicing.period.line'
    
    move_type = fields.Selection([
        ('out_invoice', "Customer Invoice"), ('in_invoice', "Vendor Bill")
    ], string="Type", default='out_invoice', related='invoice_period_id.move_type')
    
class AccountPaymentRegister(models.TransientModel):
    _inherit = 'account.payment.register'

    ### Add Period #
    period_id = fields.Many2one("invoicing.period.line", string="Period", compute="_compute_get_period", store=True)

    @api.depends("payment_date")
    def _compute_get_period(self):
        if self:
            for rec in self:
                rec.period_id = False
                if rec.payment_date:
                    period = (
                        self.env["invoicing.period.line"]
                        .sudo()
                        .search(
                            [
                                ("date_start", "<=", rec.payment_date),
                                ("date_end", ">=", rec.payment_date),
                                ('move_type', '=', 'out_invoice'),
                            ],
                            limit=1,
                        )
                    )
                    if period:
                        rec.period_id = period.id

    @api.depends("payment_date")
    def action_create_payments(self):
        for rec in self:
            if rec.payment_date:
                period = (
                    self.env["invoicing.period.line"]
                    .sudo()
                    .search(
                        [
                            ("date_start", "<=", rec.payment_date),
                            ("date_end", ">=", rec.payment_date),
                            ('move_type', '=', 'out_invoice'),
                        ],
                        limit=1,
                    )
                )
                if period and period.state == 'close':
                    raise UserError(
                        _("You cannot select a payment date from a closed invoice period.")
                    )
                    
        return super(AccountPaymentRegister, self).action_create_payments()

    def _create_payments(self):
        res = super(AccountPaymentRegister, self)._create_payments()
        bill = self.env['account.move'].browse(self._context.get('active_id'))
        create_payment_invoice = self.env['account.payment.invoice'].create({
            'move_id': bill.id,
            'payment_id': res.id,
        })
        create_payment_invoice._onchange_set_values()
        return res
