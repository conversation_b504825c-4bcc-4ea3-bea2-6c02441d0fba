<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data>

        <record id="bill_tax_reminder" model="mail.template">
            <field name="name">Tax Check Reminder for Vendor Bill</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="subject">[Action Required] Tax Check on Vendor Bill</field>
            <field name="email_to">{{ctx['mail_to']}}</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p>Dear Accounting &amp; Tax Unit,</p>
                    
                    <p style="margin: 5px 0px; padding: 0px; font-size: 12px;">
                        <div style="margin: 0px; padding: 0px;">
                            Please check the tax aspects of the following List Vendor Bills:<br/><br/>
                            <table style="width: 100%; border-collapse: collapse;" border="1">
                                <tr>
                                    <td>No</td>
                                    <td>Vendor Name</td>
                                    <td>Bill Date</td>
                                    <td>Due Date</td>
                                    <td>No. Invoice</td>
                                    <td>Link</td>
                                </tr>

                                <t t-foreach="ctx['invoices']" t-as="invoice">
                                    <tr>
                                        <td><t t-out="invoice.get('no')"/></td>
                                        <td><t t-out="invoice.get('vendor_name')"/></td>
                                        <td><t t-out="invoice.get('bill_date')"/></td>
                                        <td><t t-out="invoice.get('due_date')"/></td>
                                        <td><t t-out="invoice.get('no_invoice')"/></td>
                                        <td><a t-att-href="invoice.get('link')" ><t t-out="invoice.get('link')" /></a></td>
                                    </tr>
                                </t>
                            </table>
                        </div>
                    </p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>
    
    </data>
    

</odoo>
