from odoo import _, api, fields, models
from odoo.exceptions import UserError, ValidationError
from datetime import datetime


class AccountPayment(models.Model):
    _inherit = "account.payment"

    payment_plan_id = fields.Many2one(comodel_name="account.payment.plan", string="Payment Plan")
    payment_plans_count = fields.Integer(compute='_payment_plans_count', string='Payment Plans Count')
    payment_invoice_ids = fields.One2many('account.payment.invoice', 'payment_id', 'Invoices')
    amount_payment_invoice = fields.Monetary(currency_field='currency_id', string="Total", compute='_set_amount_payment_invoice')

    def open_payment_plans(self):
        return {
            'name': _('Payment Plan'),
            'view_mode': 'list,form',
            'res_model': 'account.payment.plan',
            'views': [(self.env.ref('account_payment_plan.view_payment_plan_tree').id, 'list'), (False, 'form')],
            'type': 'ir.actions.act_window',
            'domain': [('id', '=', self.payment_plan_id.id)],
            'context': dict(self._context, create=False),
        }

    def _payment_plans_count(self):
        for rec in self:
            res = self.env['account.payment.plan'].search_count([('id', '=', rec.payment_plan_id.id)])
            rec.payment_plans_count = res or 0

    @api.model
    def create(self, vals):
        res = super(AccountPayment, self).create(vals)
        for payment in res.payment_invoice_ids:
            if res.date and payment.date_invoice:
                if res.date < payment.date_invoice:
                    raise ValidationError('Payment date tidak boleh lebih kecil dari Bill date')
        return res

    def write(self, vals):
        res = super(AccountPayment, self).write(vals)
        for payment in self.payment_invoice_ids:
            if self.date and payment.date_invoice:
                if self.date < payment.date_invoice:
                    raise ValidationError('Payment date tidak boleh lebih kecil dari Bill date')
        return res

    def action_validate(self):
        res = super(AccountPayment, self).action_validate()

        for rec in self:
            if rec.state in 'paid':
                domain = [('account_type', 'in', ('asset_receivable', 'liability_payable')), ('reconciled', '=', False)]
                to_reconcile = []
                for invoice in rec.payment_invoice_ids:
                    for inv_line in invoice.move_id.line_ids.filtered_domain(domain):
                        to_reconcile.append(inv_line)
                    if invoice.move_id.amount_residual_signed != 0:
                        if abs(invoice.move_id.amount_residual_signed) == abs(invoice.move_id.amount_total_signed):
                            invoice.move_id.payment_state = 'not_paid'
                        else:
                            invoice.move_id.payment_state = 'partial'
                    elif invoice.move_id.amount_residual_signed == 0:
                        # invoice.move_id.payment_state = 'in_payment'
                        for payment in invoice.payment_id.move_id.line_ids:
                            if payment.reconciled is True:
                                if invoice.payment_id.destination_account_id != payment.account_id:
                                    invoice.move_id.payment_state = 'in_payment'
                                # elif invoice.payment_id.date_bank_statement is False:
                                #     invoice.move_id.payment_state = 'in_payment'
                                else:
                                    # invoice.move_id.with_context(pass_validation=True).payment_state = 'paid'
                                    invoice.move_id.payment_state = 'paid'
                            elif payment.reconciled is False:
                                invoice.move_id.payment_state = 'in_payment'
                    # else:
                    #     invoice.move_id.payment_state = 'not_paid'

                for lines in to_reconcile:
                    payment_lines = rec.move_id.line_ids.filtered_domain(domain)
                    for account in payment_lines.account_id:
                        (payment_lines + lines).filtered_domain([
                            ('account_id', '=', account.id),
                            ('reconciled', '=', False)
                        ]).with_context(no_exchange_difference=True).reconcile()

                # for lines in to_reconcile:
                #     payment_lines = rec.line_ids.filtered_domain(domain)
                #     for account in payment_lines.account_id:
                #         for matching in payment_lines.full_reconcile_id:
                #             (payment_lines + lines).filtered_domain([
                #                 ('account_id', '=', account.id),
                #                 ('full_reconcile_id', '=', matching.id),
                #                 ('reconciled', '=', False)
                #             ]).reconcile()

            # if rec.payment_doc_id:
            #     rec.payment_doc_id.payment_id = rec.id
            #
            # if rec.giro_id:
            #     rec.giro_id.payment_id = rec.id
        return res

    @api.depends('payment_invoice_ids')
    def _set_amount_payment_invoice(self):
        self.amount_payment_invoice = sum(self.payment_invoice_ids.mapped('amount'))

